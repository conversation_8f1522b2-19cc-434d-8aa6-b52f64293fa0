import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import Layout from '@/views/layout/Layout.vue'
import Dashboard from '@/views/dashboard/Dashboard.vue'
import Login from '@/views/login/Login.vue'
import NotFound from '@/views/error/404.vue'

// 扩展meta类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    icon?: string
    requiresAuth?: boolean
    roles?: string[]
  }
}

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '首页', icon: 'el-icon-s-home', requiresAuth: true }
      },
      // 学生管理模块
      {
        path: 'students',
        name: 'StudentList',
        component: () => import('@/views/student/StudentList.vue'),
        meta: { title: '学生列表', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'students/add',
        name: 'StudentAdd',
        component: () => import('@/views/student/StudentEdit.vue'),
        meta: { title: '添加学生', requiresAuth: true }
      },
      {
        path: 'students/edit/:id',
        name: 'StudentEdit',
        component: () => import('@/views/student/StudentEdit.vue'),
        meta: { title: '编辑学生', requiresAuth: true }
      },
      // 教师管理模块
      {
        path: 'teachers',
        name: 'TeacherList',
        component: () => import('@/views/teacher/TeacherList.vue'),
        meta: { title: '教师列表', icon: 'el-icon-user', requiresAuth: true }
      },
      {
        path: 'teachers/add',
        name: 'TeacherAdd',
        component: () => import('@/views/teacher/TeacherEdit.vue'),
        meta: { title: '添加教师', requiresAuth: true }
      },
      {
        path: 'teachers/edit/:id',
        name: 'TeacherEdit',
        component: () => import('@/views/teacher/TeacherEdit.vue'),
        meta: { title: '编辑教师', requiresAuth: true }
      },
      // 课程管理模块
      {
        path: 'courses',
        name: 'CourseList',
        component: () => import('@/views/course/CourseList.vue'),
        meta: { title: '课程列表', icon: 'el-icon-reading', requiresAuth: true }
      },
      {
        path: 'courses/add',
        name: 'CourseAdd',
        component: () => import('@/views/course/CourseEdit.vue'),
        meta: { title: '添加课程', requiresAuth: true }
      },
      {
        path: 'courses/edit/:id',
        name: 'CourseEdit',
        component: () => import('@/views/course/CourseEdit.vue'),
        meta: { title: '编辑课程', requiresAuth: true }
      },
      // 院系管理模块
      {
        path: 'departments',
        name: 'DepartmentList',
        component: () => import('@/views/department/DepartmentList.vue'),
        meta: { title: '院系列表', icon: 'el-icon-office-building', requiresAuth: true }
      },
      {
        path: 'departments/add',
        name: 'DepartmentAdd',
        component: () => import('@/views/department/DepartmentEdit.vue'),
        meta: { title: '添加院系', requiresAuth: true }
      },
      {
        path: 'departments/edit/:id',
        name: 'DepartmentEdit',
        component: () => import('@/views/department/DepartmentEdit.vue'),
        meta: { title: '编辑院系', requiresAuth: true }
      },
      // 教室管理模块
      {
        path: 'classrooms',
        name: 'ClassroomList',
        component: () => import('@/views/classroom/ClassroomList.vue'),
        meta: { title: '教室列表', icon: 'el-icon-school', requiresAuth: true }
      },
      {
        path: 'classrooms/add',
        name: 'ClassroomAdd',
        component: () => import('@/views/classroom/ClassroomEdit.vue'),
        meta: { title: '添加教室', requiresAuth: true }
      },
      {
        path: 'classrooms/edit/:id',
        name: 'ClassroomEdit',
        component: () => import('@/views/classroom/ClassroomEdit.vue'),
        meta: { title: '编辑教室', requiresAuth: true }
      },
      // 宿舍管理模块
      {
        path: 'dormitories',
        name: 'DormitoryList',
        component: () => import('@/views/dormitory/DormitoryList.vue'),
        meta: { title: '宿舍列表', icon: 'el-icon-house', requiresAuth: true }
      },
      {
        path: 'dormitories/add',
        name: 'DormitoryAdd',
        component: () => import('@/views/dormitory/DormitoryEdit.vue'),
        meta: { title: '添加宿舍', requiresAuth: true }
      },
      {
        path: 'dormitories/edit/:id',
        name: 'DormitoryEdit',
        component: () => import('@/views/dormitory/DormitoryEdit.vue'),
        meta: { title: '编辑宿舍', requiresAuth: true }
      },
      // 图书馆管理模块
      {
        path: 'books',
        name: 'BookList',
        component: () => import('@/views/book/BookList.vue'),
        meta: { title: '图书列表', icon: 'el-icon-collection', requiresAuth: true }
      },
      {
        path: 'books/add',
        name: 'BookAdd',
        component: () => import('@/views/book/BookEdit.vue'),
        meta: { title: '添加图书', requiresAuth: true }
      },
      {
        path: 'books/edit/:id',
        name: 'BookEdit',
        component: () => import('@/views/book/BookEdit.vue'),
        meta: { title: '编辑图书', requiresAuth: true }
      },
      // 体育场馆模块
      {
        path: 'sports',
        name: 'SportsVenueList',
        component: () => import('@/views/sports/SportsVenueList.vue'),
        meta: { title: '场馆列表', icon: 'el-icon-football', requiresAuth: true }
      },
      {
        path: 'sports/add',
        name: 'SportsVenueAdd',
        component: () => import('@/views/sports/SportsVenueEdit.vue'),
        meta: { title: '添加场馆', requiresAuth: true }
      },
      {
        path: 'sports/edit/:id',
        name: 'SportsVenueEdit',
        component: () => import('@/views/sports/SportsVenueEdit.vue'),
        meta: { title: '编辑场馆', requiresAuth: true }
      },
      // 用户管理模块
      {
        path: 'users',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: { title: '用户列表', icon: 'el-icon-user', requiresAuth: true, roles: ['admin'] }
      },
      {
        path: 'users/add',
        name: 'UserAdd',
        component: () => import('@/views/user/UserEdit.vue'),
        meta: { title: '添加用户', requiresAuth: true, roles: ['admin'] }
      },
      {
        path: 'users/edit/:id',
        name: 'UserEdit',
        component: () => import('@/views/user/UserEdit.vue'),
        meta: { title: '编辑用户', requiresAuth: true, roles: ['admin'] }
      },
      // 角色管理模块
      {
        path: 'roles',
        name: 'RoleList',
        component: () => import('@/views/role/RoleList.vue'),
        meta: { title: '角色列表', icon: 'el-icon-s-custom', requiresAuth: true, roles: ['admin'] }
      },
      {
        path: 'roles/add',
        name: 'RoleAdd',
        component: () => import('@/views/role/RoleEdit.vue'),
        meta: { title: '添加角色', requiresAuth: true, roles: ['admin'] }
      },
      {
        path: 'roles/edit/:id',
        name: 'RoleEdit',
        component: () => import('@/views/role/RoleEdit.vue'),
        meta: { title: '编辑角色', requiresAuth: true, roles: ['admin'] }
      },
      {
        path: 'roles/:id/permissions',
        name: 'RolePermission',
        component: () => import('@/views/role/RolePermission.vue'),
        meta: { title: '角色权限', requiresAuth: true, roles: ['admin'] }
      },
      // 个人中心模块
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/profile/Profile.vue'),
        meta: { title: '个人信息', requiresAuth: true }
      },
      {
        path: 'change-password',
        name: 'ChangePassword',
        component: () => import('@/views/profile/ChangePassword.vue'),
        meta: { title: '修改密码', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/profile/Settings.vue'),
        meta: { title: '系统设置', requiresAuth: true }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '404', requiresAuth: false }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - 大学学生管理系统` : '大学学生管理系统'

  const token = localStorage.getItem('token')
  if (to.meta.requiresAuth && !token) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router 