package com.university.management.controller;

import com.university.management.model.entity.Dormitory;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.DormitoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(tags = "宿舍管理")
@RestController
@RequestMapping("/api/dormitories")
public class DormitoryController {

    private final DormitoryService dormitoryService;

    @Autowired
    public DormitoryController(DormitoryService dormitoryService) {
        this.dormitoryService = dormitoryService;
    }

    @ApiOperation("获取所有宿舍")
    @GetMapping
    public ApiResponse<List<Dormitory>> getAllDormitories() {
        List<Dormitory> dormitories = dormitoryService.findAll();
        return ApiResponse.success("获取宿舍列表成功", dormitories);
    }

    @ApiOperation("分页获取宿舍")
    @GetMapping("/page")
    public ApiResponse<Page<Dormitory>> getDormitoriesByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {
        Page<Dormitory> dormitories = dormitoryService.findAll(
                PageRequest.of(page, size, Sort.by(sortBy)));
        return ApiResponse.success("获取宿舍分页列表成功", dormitories);
    }

    @ApiOperation("根据ID获取宿舍")
    @GetMapping("/{id}")
    public ApiResponse<Dormitory> getDormitoryById(@PathVariable Integer id) {
        Optional<Dormitory> dormitory = dormitoryService.findById(id);
        return dormitory.map(value -> ApiResponse.success("获取宿舍成功", value))
                .orElseGet(() -> ApiResponse.errorGeneric("宿舍不存在"));
    }

    @ApiOperation("创建宿舍")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Dormitory> createDormitory(@Valid @RequestBody Dormitory dormitory) {
        Dormitory savedDormitory = dormitoryService.save(dormitory);
        return ApiResponse.success("创建宿舍成功", savedDormitory);
    }

    @ApiOperation("更新宿舍")
    @PutMapping("/{id}")
    public ApiResponse<Dormitory> updateDormitory(
            @PathVariable Integer id, @Valid @RequestBody Dormitory dormitoryDetails) {
        Optional<Dormitory> dormitory = dormitoryService.findById(id);
        if (dormitory.isPresent()) {
            dormitoryDetails.setId(id);
            Dormitory updatedDormitory = dormitoryService.save(dormitoryDetails);
            return ApiResponse.success("更新宿舍成功", updatedDormitory);
        } else {
            return ApiResponse.errorGeneric("宿舍不存在");
        }
    }

    @ApiOperation("删除宿舍")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDormitory(@PathVariable Integer id) {
        Optional<Dormitory> dormitory = dormitoryService.findById(id);
        if (dormitory.isPresent()) {
            dormitoryService.deleteById(id);
            return ApiResponse.success("删除宿舍成功", null);
        } else {
            return ApiResponse.errorGeneric("宿舍不存在");
        }
    }
} 