package com.university.management.controller;

import com.university.management.model.entity.Course;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.CourseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Api(tags = "课程管理")
@RestController
@RequestMapping("/api/courses")
public class CourseController {

    private final CourseService courseService;

    @Autowired
    public CourseController(CourseService courseService) {
        this.courseService = courseService;
    }

    @ApiOperation("获取所有课程")
    @GetMapping
    public ApiResponse<List<Course>> getAllCourses() {
        return ApiResponse.success(courseService.findAll());
    }

    @ApiOperation("分页获取课程")
    @GetMapping("/page")
    public ApiResponse<Page<Course>> getCoursesPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "DESC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success(courseService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询课程")
    @GetMapping("/search")
    public ApiResponse<Page<Course>> searchCourses(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) Integer credits,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(courseService.findByConditions(name, department, credits, pageRequest));
    }

    @ApiOperation("获取课程详情")
    @GetMapping("/{id}")
    public ApiResponse<Course> getCourseById(@PathVariable Integer id) {
        Optional<Course> course = courseService.findById(id);
        return course.map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.errorGeneric(404, "课程不存在"));
    }

    @ApiOperation("创建课程")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Course> createCourse(@Valid @RequestBody Course course) {
        return ApiResponse.success("创建成功", courseService.createCourse(course));
    }

    @ApiOperation("更新课程")
    @PutMapping("/{id}")
    public ApiResponse<Course> updateCourse(@PathVariable Integer id, @Valid @RequestBody Course courseDetails) {
        return ApiResponse.success("更新成功", courseService.updateCourse(id, courseDetails));
    }

    @ApiOperation("删除课程")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCourse(@PathVariable Integer id) {
        courseService.deleteCourse(id);
        return ApiResponse.success();
    }

    @ApiOperation("获取课程院系分布统计")
    @GetMapping("/stats/department")
    public ApiResponse<Map<String, Long>> getCourseStatsByDepartment() {
        return ApiResponse.success(courseService.getCourseStatsByDepartment());
    }

    @ApiOperation("获取课程学分分布统计")
    @GetMapping("/stats/credits")
    public ApiResponse<Map<Integer, Long>> getCourseStatsByCredits() {
        return ApiResponse.success(courseService.getCourseStatsByCredits());
    }

    @ApiOperation("获取课程学期分布统计")
    @GetMapping("/stats/semester")
    public ApiResponse<Map<String, Long>> getCourseStatsBySemester() {
        return ApiResponse.success(courseService.getCourseStatsBySemester());
    }
}