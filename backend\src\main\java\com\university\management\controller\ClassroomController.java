package com.university.management.controller;

import com.university.management.model.entity.Classroom;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.ClassroomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(tags = "教室管理")
@RestController
@RequestMapping("/api/classrooms")
public class ClassroomController {

    private final ClassroomService classroomService;

    @Autowired
    public ClassroomController(ClassroomService classroomService) {
        this.classroomService = classroomService;
    }

    @ApiOperation("获取所有教室")
    @GetMapping
    public ApiResponse<List<Classroom>> getAllClassrooms() {
        List<Classroom> classrooms = classroomService.findAll();
        return ApiResponse.success("获取教室列表成功", classrooms);
    }

    @ApiOperation("分页获取教室")
    @GetMapping("/page")
    public ApiResponse<Page<Classroom>> getClassroomsByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {
        Page<Classroom> classrooms = classroomService.findAll(
                PageRequest.of(page, size, Sort.by(sortBy)));
        return ApiResponse.success("获取教室分页列表成功", classrooms);
    }

    @ApiOperation("根据ID获取教室")
    @GetMapping("/{id}")
    public ApiResponse<Classroom> getClassroomById(@PathVariable Integer id) {
        Optional<Classroom> classroom = classroomService.findById(id);
        return classroom.map(value -> ApiResponse.success("获取教室成功", value))
                .orElseGet(() -> ApiResponse.errorGeneric("教室不存在"));
    }

    @ApiOperation("创建教室")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Classroom> createClassroom(@Valid @RequestBody Classroom classroom) {
        Classroom savedClassroom = classroomService.save(classroom);
        return ApiResponse.success("创建教室成功", savedClassroom);
    }

    @ApiOperation("更新教室")
    @PutMapping("/{id}")
    public ApiResponse<Classroom> updateClassroom(
            @PathVariable Integer id, @Valid @RequestBody Classroom classroomDetails) {
        Optional<Classroom> classroom = classroomService.findById(id);
        if (classroom.isPresent()) {
            classroomDetails.setId(id);
            Classroom updatedClassroom = classroomService.save(classroomDetails);
            return ApiResponse.success("更新教室成功", updatedClassroom);
        } else {
            return ApiResponse.errorGeneric("教室不存在");
        }
    }

    @ApiOperation("删除教室")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteClassroom(@PathVariable Integer id) {
        Optional<Classroom> classroom = classroomService.findById(id);
        if (classroom.isPresent()) {
            classroomService.deleteById(id);
            return ApiResponse.success("删除教室成功", null);
        } else {
            return ApiResponse.errorGeneric("教室不存在");
        }
    }
} 