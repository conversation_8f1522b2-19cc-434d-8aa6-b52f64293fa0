package com.university.management.repository;

import com.university.management.model.entity.Teacher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TeacherRepository extends JpaRepository<Teacher, Integer> {
    
    Optional<Teacher> findByTeacherNo(String teacherNo);
    
    Page<Teacher> findByNameContaining(String name, Pageable pageable);
    
    @Query("SELECT t FROM Teacher t WHERE " +
           "(:name IS NULL OR t.name LIKE %:name%) AND " +
           "(:department IS NULL OR t.collegeId = :department) AND " +
           "(:position IS NULL OR t.title = :position)")
    Page<Teacher> findByConditions(
            @Param("name") String name, 
            @Param("department") String department, 
            @Param("position") String position, 
            Pageable pageable);
    
    boolean existsByTeacherNo(String teacherNo);
    
    long countByCollegeId(Integer collegeId);
} 