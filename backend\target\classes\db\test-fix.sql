-- 测试修复脚本 - 先修复几个关键数据测试
USE university_management;

-- 设置字符集
SET NAMES utf8mb4;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 测试修复几个班级数据
UPDATE class SET name = '计算机科学与技术2020级1班' WHERE name = 'Computer Science 2020 Class 1';
UPDATE class SET name = '软件工程2020级1班' WHERE name = 'Software Engineering 2020 Class 1';
UPDATE class SET name = '网络工程2020级1班' WHERE name = 'Network Engineering 2020 Class 1';

-- 测试修复几个专业数据
UPDATE major SET name = '计算机科学与技术' WHERE name = 'Computer Science and Technology';
UPDATE major SET name = '软件工程' WHERE name = 'Software Engineering';
UPDATE major SET name = '网络工程' WHERE name = 'Network Engineering';

-- 测试修复几个课程数据
UPDATE course SET name = '程序设计基础' WHERE name = 'Programming Fundamentals';
UPDATE course SET name = '数据结构' WHERE name = 'Data Structures';
UPDATE course SET name = '算法分析' WHERE name = 'Algorithm Analysis';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;

-- 显示测试结果
SELECT '测试修复完成' AS 状态;

-- 检查修复结果
SELECT '班级测试结果' AS 类型, name FROM class WHERE name LIKE '%级%班' LIMIT 3;
SELECT '专业测试结果' AS 类型, name FROM major WHERE name IN ('计算机科学与技术', '软件工程', '网络工程');
SELECT '课程测试结果' AS 类型, name FROM course WHERE name IN ('程序设计基础', '数据结构', '算法分析');
