<template>
  <div class="not-found">
    <div class="content">
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被移除</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.not-found {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
  text-align: center;
}

.content {
  padding: 40px;
}

h1 {
  font-size: 120px;
  color: #409EFF;
  margin: 0;
  line-height: 1;
}

h2 {
  font-size: 30px;
  color: #606266;
  margin: 15px 0;
}

p {
  font-size: 16px;
  color: #909399;
  margin-bottom: 30px;
}
</style> 