<template>
  <div class="sports-venue-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑体育场馆' : '新增体育场馆' }}</h2>
    </div>

    <el-card class="form-container">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        label-position="right">

        <el-form-item label="场馆名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入场馆名称" />
        </el-form-item>

        <el-form-item label="场馆类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择场馆类型" style="width: 100%">
            <el-option label="篮球场" value="篮球场" />
            <el-option label="足球场" value="足球场" />
            <el-option label="网球场" value="网球场" />
            <el-option label="羽毛球馆" value="羽毛球馆" />
            <el-option label="乒乓球室" value="乒乓球室" />
            <el-option label="游泳馆" value="游泳馆" />
            <el-option label="健身房" value="健身房" />
          </el-select>
        </el-form-item>

        <el-form-item label="位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入场馆位置" />
        </el-form-item>

        <el-form-item label="容量" prop="capacity">
          <el-input-number v-model="form.capacity" :min="1" :max="1000" />
          <span class="unit">人</span>
        </el-form-item>

        <el-form-item label="开放时间" prop="openTime">
          <el-time-picker
            v-model="form.openTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="关闭时间" prop="closeTime">
          <el-time-picker
            v-model="form.closeTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="费用" prop="fee">
          <el-input-number v-model="form.fee" :min="0" :step="5" :precision="0" />
          <span class="unit">元/小时 (0表示免费)</span>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">开放</el-radio>
            <el-radio :label="1">维修中</el-radio>
            <el-radio :label="2">已预约</el-radio>
            <el-radio :label="3">关闭</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="设施设备" prop="equipment">
          <el-input v-model="form.equipment" type="textarea" :rows="3" placeholder="请输入场馆设施设备信息" />
        </el-form-item>

        <el-form-item label="场馆描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入场馆描述" />
        </el-form-item>

        <el-form-item label="使用规则" prop="rules">
          <el-input v-model="form.rules" type="textarea" :rows="3" placeholder="请输入场馆使用规则" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 引入API，实际项目中需要添加
// import { getVenue, createVenue, updateVenue } from '@/api/sportsVenue'

export default {
  name: 'SportsVenueEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const venueId = computed(() => route.params.id)
    const isEdit = computed(() => !!venueId.value)

    // 表单数据
    const form = reactive({
      name: '',
      type: '',
      location: '',
      capacity: 100,
      openTime: '08:00',
      closeTime: '22:00',
      fee: 0,
      status: 0,
      equipment: '',
      description: '',
      rules: ''
    })

    // 表单验证规则
    const rules = reactive({
      name: [{ required: true, message: '请输入场馆名称', trigger: 'blur' }],
      type: [{ required: true, message: '请选择场馆类型', trigger: 'change' }],
      location: [{ required: true, message: '请输入场馆位置', trigger: 'blur' }],
      capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }],
      openTime: [{ required: true, message: '请选择开放时间', trigger: 'change' }],
      closeTime: [{ required: true, message: '请选择关闭时间', trigger: 'change' }],
      status: [{ required: true, message: '请选择场馆状态', trigger: 'change' }]
    })

    // 获取场馆详情
    const fetchVenueDetail = async (id) => {
      try {
        // 实际项目中这里需要调用API
        // const response = await getVenue(id)
        // Object.assign(form, response.data)
        ElMessage.info('模拟加载场馆数据')
        // 模拟数据
        Object.assign(form, {
          name: '中心篮球场',
          type: '篮球场',
          location: '校园中心区域体育中心',
          capacity: 200,
          openTime: '08:00',
          closeTime: '22:00',
          fee: 0,
          status: 0,
          equipment: '标准篮球架6个，电子记分牌，观众席200个座位',
          description: '室外标准篮球场，设有观众席',
          rules: '1. 爱护场地设施\n2. 遵守场馆开放时间\n3. 禁止在场内吸烟、饮酒\n4. 禁止穿钉鞋入场'
        })
      } catch (error) {
        ElMessage.error('获取场馆信息失败')
        console.error(error)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        // 开放时间不能晚于关闭时间
        if (form.openTime >= form.closeTime) {
          ElMessage.warning('开放时间不能晚于或等于关闭时间')
          return
        }
        
        // 实际项目中这里需要调用API
        if (isEdit.value) {
          // await updateVenue(venueId.value, form)
          ElMessage.success('更新场馆成功')
        } else {
          // await createVenue(form)
          ElMessage.success('创建场馆成功')
        }
        
        // 返回列表页
        router.push({ name: 'SportsVenueList' })
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.back()
    }

    // 组件挂载时，如果是编辑模式，获取场馆详情
    onMounted(() => {
      if (isEdit.value) {
        fetchVenueDetail(venueId.value)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.sports-venue-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 800px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}
</style> 