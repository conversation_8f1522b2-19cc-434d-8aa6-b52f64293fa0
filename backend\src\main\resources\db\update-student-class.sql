-- 更新学生的班级分配
USE university_management;

-- 更新Computer Science专业的学生到相应班级
-- CS2020-1班级 (class_id = 1071, major_id = 1058)
UPDATE student SET class_id = 1071 WHERE major_id = 1058 AND enroll_year = 2020 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1058 AND enroll_year = 2020 ORDER BY id LIMIT 5) AS temp
);

-- CS2021-1班级 (class_id = 1073, major_id = 1058)  
UPDATE student SET class_id = 1073 WHERE major_id = 1058 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1058 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Software Engineering专业的学生
-- SE2020-1班级 (class_id = 1074, major_id = 1059)
UPDATE student SET class_id = 1074 WHERE major_id = 1059 AND enroll_year = 2020 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1059 AND enroll_year = 2020 ORDER BY id LIMIT 5) AS temp
);

-- SE2021-1班级 (class_id = 1075, major_id = 1059)
UPDATE student SET class_id = 1075 WHERE major_id = 1059 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1059 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Network Engineering专业的学生
-- NE2020-1班级 (class_id = 1076, major_id = 1060)
UPDATE student SET class_id = 1076 WHERE major_id = 1060 AND enroll_year = 2020 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1060 AND enroll_year = 2020 ORDER BY id LIMIT 5) AS temp
);

-- NE2021-1班级 (class_id = 1077, major_id = 1060)
UPDATE student SET class_id = 1077 WHERE major_id = 1060 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1060 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新AI专业的学生
-- AI2021-1班级 (class_id = 1078, major_id = 1061)
UPDATE student SET class_id = 1078 WHERE major_id = 1061 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1061 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Data Science专业的学生
-- DS2021-1班级 (class_id = 1079, major_id = 1062)
UPDATE student SET class_id = 1079 WHERE major_id = 1062 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1062 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Electronic Engineering专业的学生
-- EE2021-1班级 (class_id = 1080, major_id = 1063)
UPDATE student SET class_id = 1080 WHERE major_id = 1063 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1063 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Communication Engineering专业的学生
-- TC2021-1班级 (class_id = 1081, major_id = 1064)
UPDATE student SET class_id = 1081 WHERE major_id = 1064 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1064 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Mechanical Engineering专业的学生
-- ME2021-1班级 (class_id = 1083, major_id = 1066)
UPDATE student SET class_id = 1083 WHERE major_id = 1066 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1066 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新Business Administration专业的学生
-- BM2021-1班级 (class_id = 1086, major_id = 1069)
UPDATE student SET class_id = 1086 WHERE major_id = 1069 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1069 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);

-- 更新English专业的学生
-- EN2021-1班级 (class_id = 1090, major_id = 1073)
UPDATE student SET class_id = 1090 WHERE major_id = 1073 AND enroll_year = 2021 AND id IN (
    SELECT * FROM (SELECT id FROM student WHERE major_id = 1073 AND enroll_year = 2021 ORDER BY id LIMIT 5) AS temp
);
