package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 图书实体类
 */
@Entity
@Table(name = "book")
@TableName("book")
@ApiModel(value = "图书实体", description = "图书信息")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Book extends BaseEntity {

    /**
     * 图书编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("图书编号")
    private String bookNo;

    /**
     * 图书名称
     */
    @Column(nullable = false, length = 100)
    @ApiModelProperty("图书名称")
    private String name;

    /**
     * 图书标题
     */
    @Column(name = "title", nullable = false, length = 100)
    @ApiModelProperty("图书标题")
    private String title;

    /**
     * 作者
     */
    @Column(length = 50)
    @ApiModelProperty("作者")
    private String author;

    /**
     * 出版社
     */
    @Column(length = 50)
    @ApiModelProperty("出版社")
    private String publisher;

    /**
     * 出版日期
     */
    @Column
    @ApiModelProperty("出版日期")
    private LocalDate publishDate;

    /**
     * ISBN
     */
    @Column(length = 20)
    @ApiModelProperty("ISBN")
    private String isbn;

    /**
     * 分类
     */
    @Column(length = 50)
    @ApiModelProperty("分类")
    private String category;

    /**
     * 语言
     */
    @Column(length = 20)
    @ApiModelProperty("语言")
    private String language;

    /**
     * 页数
     */
    @Column
    @ApiModelProperty("页数")
    private Integer pages;

    /**
     * 价格
     */
    @Column
    @ApiModelProperty("价格")
    private Float price;

    /**
     * 库存数量
     */
    @Column
    @ApiModelProperty("库存数量")
    private Integer stock;

    /**
     * 可借数量
     */
    @Column
    @ApiModelProperty("可借数量")
    private Integer availableStock;

    /**
     * 可用数量（别名）- 移除重复映射，使用getter方法返回availableStock的值
     */
    @Transient
    @ApiModelProperty("可用数量")
    private Integer available;

    /**
     * 借阅次数
     */
    @Column
    @ApiModelProperty("借阅次数")
    private Integer borrowTimes;

    /**
     * 图书简介
     */
    @Column(length = 500)
    @ApiModelProperty("图书简介")
    private String description;

    /**
     * 图书位置
     */
    @Column(length = 50)
    @ApiModelProperty("图书位置")
    private String location;

    /**
     * 状态（0-可借，1-已借完，2-下架）
     */
    @Column
    @ApiModelProperty("状态（0-可借，1-已借完，2-下架）")
    private Integer status;

    /**
     * 借阅记录列表
     */
    @OneToMany(mappedBy = "book", fetch = FetchType.LAZY)
    @TableField(exist = false)
    @JsonIgnore
    private List<BorrowRecord> borrowRecords = new ArrayList<>();

    // Getter和Setter方法
    public String getBookNo() {
        return bookNo;
    }

    public void setBookNo(String bookNo) {
        this.bookNo = bookNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public LocalDate getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(LocalDate publishDate) {
        this.publishDate = publishDate;
    }

    public String getIsbn() {
        return isbn;
    }

    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(Integer availableStock) {
        this.availableStock = availableStock;
    }

    public Integer getAvailable() {
        return this.availableStock;
    }

    public void setAvailable(Integer available) {
        this.availableStock = available;
    }

    public Integer getBorrowTimes() {
        return borrowTimes;
    }

    public void setBorrowTimes(Integer borrowTimes) {
        this.borrowTimes = borrowTimes;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<BorrowRecord> getBorrowRecords() {
        return borrowRecords;
    }

    public void setBorrowRecords(List<BorrowRecord> borrowRecords) {
        this.borrowRecords = borrowRecords;
    }
}