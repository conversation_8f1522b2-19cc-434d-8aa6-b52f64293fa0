package com.university.management.controller;

import com.university.management.model.entity.Book;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.BookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@Api(tags = "图书管理")
@RestController
@RequestMapping("/api/books")
public class BookController {

    private final BookService bookService;

    @Autowired
    public BookController(BookService bookService) {
        this.bookService = bookService;
    }

    @ApiOperation("获取所有图书")
    @GetMapping
    public ApiResponse<List<Book>> getAllBooks() {
        List<Book> books = bookService.findAll();
        return ApiResponse.success("获取图书列表成功", books);
    }

    @ApiOperation("分页获取图书")
    @GetMapping("/page")
    public ApiResponse<Page<Book>> getBooksByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {
        Page<Book> books = bookService.findAll(
                PageRequest.of(page, size, Sort.by(sortBy)));
        return ApiResponse.success("获取图书分页列表成功", books);
    }

    @ApiOperation("根据ID获取图书")
    @GetMapping("/{id}")
    public ApiResponse<Book> getBookById(@PathVariable Integer id) {
        Optional<Book> book = bookService.findById(id);
        return book.map(value -> ApiResponse.success("获取图书成功", value))
                .orElseGet(() -> ApiResponse.errorGeneric("图书不存在"));
    }

    @ApiOperation("创建图书")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Book> createBook(@Valid @RequestBody Book book) {
        Book savedBook = bookService.save(book);
        return ApiResponse.success("创建图书成功", savedBook);
    }

    @ApiOperation("更新图书")
    @PutMapping("/{id}")
    public ApiResponse<Book> updateBook(
            @PathVariable Integer id, @Valid @RequestBody Book bookDetails) {
        Optional<Book> book = bookService.findById(id);
        if (book.isPresent()) {
            bookDetails.setId(id);
            Book updatedBook = bookService.save(bookDetails);
            return ApiResponse.success("更新图书成功", updatedBook);
        } else {
            return ApiResponse.errorGeneric("图书不存在");
        }
    }

    @ApiOperation("删除图书")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteBook(@PathVariable Integer id) {
        Optional<Book> book = bookService.findById(id);
        if (book.isPresent()) {
            bookService.deleteById(id);
            return ApiResponse.success("删除图书成功", null);
        } else {
            return ApiResponse.errorGeneric("图书不存在");
        }
    }
} 