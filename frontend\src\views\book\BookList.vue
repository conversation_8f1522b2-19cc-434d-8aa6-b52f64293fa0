<template>
  <div class="book-list-container">
    <div class="page-header">
      <h2>图书管理</h2>
      <el-button type="primary" @click="handleAddBook">新增图书</el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="书名">
          <el-input v-model="queryParams.title" placeholder="请输入书名" clearable />
        </el-form-item>
        <el-form-item label="作者">
          <el-input v-model="queryParams.author" placeholder="请输入作者" clearable />
        </el-form-item>
        <el-form-item label="ISBN">
          <el-input v-model="queryParams.isbn" placeholder="请输入ISBN" clearable />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="queryParams.category" placeholder="请选择分类" clearable>
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="文学" value="文学" />
            <el-option label="历史" value="历史" />
            <el-option label="艺术" value="艺术" />
            <el-option label="科学" value="科学" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.available" placeholder="请选择状态" clearable>
            <el-option label="可借阅" :value="1" />
            <el-option label="已借出" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-container">
      <el-table
        v-loading="loading"
        :data="bookList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="title" label="书名" min-width="180" show-overflow-tooltip />
        <el-table-column prop="author" label="作者" min-width="120" show-overflow-tooltip />
        <el-table-column prop="publisher" label="出版社" min-width="150" show-overflow-tooltip />
        <el-table-column prop="isbn" label="ISBN" min-width="150" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="publishDate" label="出版日期" width="120" />
        <el-table-column prop="available" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.available ? 'success' : 'danger'">
              {{ scope.row.available ? '可借阅' : '已借出' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:page-size="queryParams.pageSize"
          v-model:current-page="queryParams.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getBooksByPage, searchBooks, deleteBook } from '@/api/book'

export default {
  name: 'BookList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const total = ref(0)
    const bookList = ref([])
    
    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      title: '',
      author: '',
      isbn: '',
      category: '',
      available: undefined
    })
    
    // 获取图书列表
    const getList = async () => {
      loading.value = true
      try {
        console.log('📡 发送真实API请求: GET /api/books/page')
        const response = await getBooksByPage(
          queryParams.pageNum - 1,
          queryParams.pageSize,
          'id'
        )

        if (response.data) {
          bookList.value = response.data.content || response.data
          total.value = response.data.totalElements || response.data.length || 0
          console.log('✅ 成功获取图书数据，共', total.value, '条记录')
        }
      } catch (error) {
        console.error('❌ 图书API请求失败，使用模拟数据:', error)
        // API失败时使用模拟数据
          bookList.value = [
            {
              id: 1,
              title: 'JavaScript高级程序设计',
              author: '尼古拉斯·泽卡斯',
              publisher: '人民邮电出版社',
              isbn: '9787115547538',
              category: '计算机科学',
              publishDate: '2021-01-01',
              available: 1
            },
            {
              id: 2,
              title: 'Vue.js实战',
              author: '梁灏',
              publisher: '电子工业出版社',
              isbn: '9787121349515',
              category: '计算机科学',
              publishDate: '2019-04-01',
              available: 0
            },
            {
              id: 3,
              title: '平凡的世界',
              author: '路遥',
              publisher: '人民文学出版社',
              isbn: '9787020049295',
              category: '文学',
              publishDate: '2017-06-01',
              available: 1
            }
          ]
          total.value = 3
          console.log('🔄 使用模拟图书数据，共', total.value, '条记录')
          ElMessage.warning('图书API不可用，使用模拟数据')
      } finally {
        loading.value = false
      }
    }
    
    // 处理查询按钮点击
    const handleQuery = () => {
      queryParams.pageNum = 1
      getList()
    }
    
    // 重置查询条件
    const resetQuery = () => {
      queryParams.title = ''
      queryParams.author = ''
      queryParams.isbn = ''
      queryParams.category = ''
      queryParams.available = undefined
      queryParams.pageNum = 1
      getList()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      queryParams.pageNum = val
      getList()
    }
    
    // 处理每页数量变化
    const handleSizeChange = (val) => {
      queryParams.pageSize = val
      queryParams.pageNum = 1
      getList()
    }
    
    // 查看图书详情
    const handleView = (row) => {
      ElMessage.info(`查看图书：${row.title}`)
    }
    
    // 编辑图书
    const handleEdit = (row) => {
      router.push({ name: 'BookEdit', params: { id: row.id } })
    }
    
    // 添加图书
    const handleAddBook = () => {
      router.push({ name: 'BookAdd' })
    }
    
    // 删除图书
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除图书《${row.title}》吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 实际项目中这里需要调用API
          // await deleteBook(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      loading,
      bookList,
      queryParams,
      total,
      handleQuery,
      resetQuery,
      handleCurrentChange,
      handleSizeChange,
      handleView,
      handleEdit,
      handleAddBook,
      handleDelete
    }
  }
}
</script>

<style scoped>
.book-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}
</style> 