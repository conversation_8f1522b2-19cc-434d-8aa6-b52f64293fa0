package com.university.management.repository;

import com.university.management.model.entity.SportsVenue;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SportsVenueRepository extends JpaRepository<SportsVenue, Integer> {
    
    Optional<SportsVenue> findByName(String venueName);
    
    @Query("SELECT sv FROM SportsVenue sv WHERE " +
           "(:venueName IS NULL OR sv.name LIKE %:venueName%) AND " +
           "(:venueType IS NULL OR sv.type = :venueType) AND " +
           "(:location IS NULL OR sv.location LIKE %:location%) AND " +
           "(:isAvailable IS NULL OR sv.status = 0)")
    Page<SportsVenue> findByConditions(
            @Param("venueName") String venueName, 
            @Param("venueType") Integer venueType, 
            @Param("location") String location,
            @Param("isAvailable") Boolean isAvailable,
            Pageable pageable);
    
    List<SportsVenue> findByType(Integer type);
    
    List<SportsVenue> findByLocation(String location);
    
    List<SportsVenue> findByStatus(Integer status);
    
    boolean existsByName(String venueName);
    
    @Query("SELECT COUNT(sv) FROM SportsVenue sv WHERE sv.type = :type")
    long countByType(@Param("type") Integer type);
    
    @Query("SELECT COUNT(sv) FROM SportsVenue sv WHERE sv.status = 0")
    long countByAvailable();
} 