import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './style.css'

// 导入数据模式切换工具
import { showCurrentDataMode, autoSwitchDataMode } from './utils/switchToRealData'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 挂载状态管理
app.use(createPinia())

// 挂载路由
app.use(router)

// 挂载Element Plus
app.use(ElementPlus, { size: 'default' })

// 全局异常处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
}

// 挂载应用
app.mount('#app')

// 应用启动后检查数据模式
setTimeout(() => {
  console.log('🚀 大学学生管理系统启动完成')
  showCurrentDataMode()

  // 在开发环境下自动检测数据模式
  if (import.meta.env.DEV) {
    console.log('🔍 开发环境：自动检测数据模式...')
    // autoSwitchDataMode() // 取消注释以启用自动检测
  }
}, 1000)
