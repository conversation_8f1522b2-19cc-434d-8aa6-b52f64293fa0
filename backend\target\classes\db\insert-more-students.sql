-- 插入更多学生数据
USE university_management;

-- 插入更多学生数据（使用正确的major_id, department_id, college_id, class_id）
INSERT INTO student (student_no, name, gender, birthday, age, phone, email, major_id, class_id, department_id, college_id, enroll_year, status) VALUES
-- 2022年新生 - Computer Science
('S2022001', '<PERSON>', 1, '2004-03-15', 20, '13800003001', '<EMAIL>', 1058, 1071, 529, 529, 2022, 0),
('S2022002', '<PERSON>', 0, '2004-05-20', 20, '13800003002', '<EMAIL>', 1058, 1071, 529, 529, 2022, 0),
('S2022003', '<PERSON>', 1, '2004-07-10', 20, '13800003003', '<EMAIL>', 1058, 1072, 529, 529, 2022, 0),
('S2022004', '<PERSON>', 0, '2004-09-25', 20, '13800003004', '<EMAIL>', 1058, 1072, 529, 529, 2022, 0),
('S2022005', '<PERSON><PERSON>e', 1, '2004-11-30', 20, '13800003005', '<EMAIL>', 1058, 1073, 529, 529, 2022, 0),

-- 2022年新生 - Software Engineering
('S2022006', 'Meng Xiaoyu', 0, '2004-01-12', 20, '13800003006', '<EMAIL>', 1059, 1074, 529, 529, 2022, 0),
('S2022007', 'Nie Xiaolei', 1, '2004-04-18', 20, '13800003007', '<EMAIL>', 1059, 1074, 529, 529, 2022, 0),
('S2022008', 'Ou Xiaoxin', 0, '2004-06-22', 20, '13800003008', '<EMAIL>', 1059, 1075, 529, 529, 2022, 0),
('S2022009', 'Peng Xiaohao', 1, '2004-08-14', 20, '13800003009', '<EMAIL>', 1059, 1075, 529, 529, 2022, 0),
('S2022010', 'Qin Xiaomei', 0, '2004-02-28', 20, '13800003010', '<EMAIL>', 1059, 1075, 529, 529, 2022, 0),

-- 2022年新生 - Electronic Engineering
('S2022101', 'Ren Xiaodong', 1, '2004-10-05', 20, '13800103001', '<EMAIL>', 1063, 1080, 530, 530, 2022, 0),
('S2022102', 'Song Xiaoli', 0, '2004-01-15', 20, '13800103002', '<EMAIL>', 1063, 1080, 530, 530, 2022, 0),
('S2022103', 'Tu Xiaoming', 1, '2004-03-20', 20, '13800103003', '<EMAIL>', 1063, 1080, 530, 530, 2022, 0),
('S2022104', 'Wang Xiaojuan', 0, '2004-05-10', 20, '13800103004', '<EMAIL>', 1063, 1080, 530, 530, 2022, 0),
('S2022105', 'Xie Xiaofeng', 1, '2004-07-25', 20, '13800103005', '<EMAIL>', 1063, 1080, 530, 530, 2022, 0),

-- 2022年新生 - Mechanical Engineering
('S2022201', 'Yang Xiaohui', 0, '2004-02-14', 20, '***********', '<EMAIL>', 1066, 1083, 531, 531, 2022, 0),
('S2022202', 'Zhang Xiaoliang', 1, '2004-04-18', 20, '***********', '<EMAIL>', 1066, 1083, 531, 531, 2022, 0),
('S2022203', 'Zhao Xiaoxia', 0, '2004-06-22', 20, '***********', '<EMAIL>', 1066, 1083, 531, 531, 2022, 0),
('S2022204', 'Zhou Xiaojun', 1, '2004-08-30', 20, '***********', '<EMAIL>', 1066, 1083, 531, 531, 2022, 0),
('S2022205', 'Zhu Xiaolan', 0, '2004-01-10', 20, '***********', '<EMAIL>', 1066, 1083, 531, 531, 2022, 0),

-- 2022年新生 - Business Administration
('S2022301', 'Cai Xiaotao', 1, '2004-03-15', 20, '***********', '<EMAIL>', 1069, 1086, 532, 532, 2022, 0),
('S2022302', 'Ding Xiaohui', 0, '2004-05-20', 20, '***********', '<EMAIL>', 1069, 1086, 532, 532, 2022, 0),
('S2022303', 'Fan Xiaopeng', 1, '2004-07-25', 20, '***********', '<EMAIL>', 1069, 1086, 532, 532, 2022, 0),
('S2022304', 'Gao Xiaoli', 0, '2004-09-30', 20, '***********', '<EMAIL>', 1069, 1086, 532, 532, 2022, 0),
('S2022305', 'Han Xiaojun', 1, '2004-11-12', 20, '***********', '<EMAIL>', 1069, 1086, 532, 532, 2022, 0),

-- 2022年新生 - English
('S2022401', 'Jiang Xiaomei', 0, '2004-01-15', 20, '***********', '<EMAIL>', 1073, 1090, 533, 533, 2022, 0),
('S2022402', 'Li Xiaofeng', 1, '2004-03-22', 20, '***********', '<EMAIL>', 1073, 1090, 533, 533, 2022, 0),
('S2022403', 'Liu Xiaoxue', 0, '2004-05-30', 20, '***********', '<EMAIL>', 1073, 1090, 533, 533, 2022, 0),
('S2022404', 'Ma Xiaotao', 1, '2004-07-18', 20, '***********', '<EMAIL>', 1073, 1090, 533, 533, 2022, 0),
('S2022405', 'Qiu Xiaoli', 0, '2004-09-25', 20, '13800403005', '<EMAIL>', 1073, 1090, 533, 533, 2022, 0),

-- 2023年新生 - Computer Science
('S2023001', 'Shi Xiaohao', 1, '2005-03-15', 19, '13800004001', '<EMAIL>', 1058, 1073, 529, 529, 2023, 0),
('S2023002', 'Tang Xiaoxin', 0, '2005-05-20', 19, '13800004002', '<EMAIL>', 1058, 1073, 529, 529, 2023, 0),
('S2023003', 'Wang Xiaolei', 1, '2005-07-10', 19, '13800004003', '<EMAIL>', 1058, 1073, 529, 529, 2023, 0),
('S2023004', 'Xu Xiaoyue', 0, '2005-09-25', 19, '13800004004', '<EMAIL>', 1058, 1073, 529, 529, 2023, 0),
('S2023005', 'Yu Xiaojie', 1, '2005-11-30', 19, '13800004005', '<EMAIL>', 1058, 1073, 529, 529, 2023, 0);
