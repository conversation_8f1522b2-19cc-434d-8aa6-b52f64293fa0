2025-07-27 04:54:14 [main] INFO  c.u.m.UniversityManagementApplication - Starting UniversityManagementApplication using Java 24.0.1 on BF-202504030152 with PID 40956 (D:\1项目解析\大学学生管理系统\backend\target\classes started by Administrator in D:\1项目解析\大学学生管理系统\backend)
2025-07-27 04:54:14 [main] DEBUG c.u.m.UniversityManagementApplication - Running with Spring Boot v2.7.10, Spring v5.3.26
2025-07-27 04:54:14 [main] INFO  c.u.m.UniversityManagementApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-27 04:54:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-27 04:54:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-27 04:54:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 168 ms. Found 11 JPA repository interfaces.
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'bookRepository' and 'com.university.management.repository.BookRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'borrowRecordRepository' and 'com.university.management.repository.BorrowRecordRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classRepository' and 'com.university.management.repository.ClassRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'classroomRepository' and 'com.university.management.repository.ClassroomRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'courseRepository' and 'com.university.management.repository.CourseRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'departmentRepository' and 'com.university.management.repository.DepartmentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'dormitoryRepository' and 'com.university.management.repository.DormitoryRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'majorRepository' and 'com.university.management.repository.MajorRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'sportsVenueRepository' and 'com.university.management.repository.SportsVenueRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'studentRepository' and 'com.university.management.repository.StudentRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'teacherRepository' and 'com.university.management.repository.TeacherRepository' mapperInterface. Bean already defined with the same name!
2025-07-27 04:54:16 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.university.management.repository]' package. Please check your configuration.
2025-07-27 04:54:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8083 (http)
2025-07-27 04:54:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-27 04:54:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-07-27 04:54:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-27 04:54:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2091 ms
2025-07-27 04:54:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-27 04:54:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-27 04:54:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-27 04:54:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - JSqlParser is in classpath. If applicable JSqlParser will be used.
2025-07-27 04:54:20 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 0aca7c4b-ccfc-45ac-b2e5-e2ab9fc3f6a5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-27 04:54:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b25ce5e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b99e726, org.springframework.security.web.context.SecurityContextPersistenceFilter@56a8b19f, org.springframework.security.web.header.HeaderWriterFilter@e62d757, org.springframework.web.filter.CorsFilter@3a788fe0, org.springframework.security.web.authentication.logout.LogoutFilter@915c47b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@36c45b54, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@377a1763, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b841396, org.springframework.security.web.session.SessionManagementFilter@2be49c8c, org.springframework.security.web.access.ExceptionTranslationFilter@3003d288, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2474331d]
2025-07-27 04:54:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8083 (http) with context path ''
2025-07-27 04:54:22 [main] INFO  c.u.m.UniversityManagementApplication - Started UniversityManagementApplication in 8.412 seconds (JVM running for 8.801)
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 开始初始化数据库...
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库表结构初始化完成
2025-07-27 04:54:22 [main] ERROR c.u.m.config.DatabaseInitializer - 数据库初始化失败：
org.springframework.jdbc.datasource.init.ScriptStatementFailedException: Failed to execute SQL script statement #10 of class path resource [db/init-data.sql]: INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status) VALUES ('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0), ('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0), ('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0), ('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0), ('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0), ('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0), ('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0), ('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0), ('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0), ('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0); nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:282)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.populate(ResourceDatabasePopulator.java:254)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:54)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.university.management.config.DatabaseInitializer.lambda$initDatabase$0(DatabaseInitializer.java:48)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:768)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:314)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.university.management.UniversityManagementApplication.main(UniversityManagementApplication.java:21)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'total' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.springframework.jdbc.datasource.init.ScriptUtils.executeSqlScript(ScriptUtils.java:261)
	... 10 common frames omitted
2025-07-27 04:54:22 [main] INFO  c.u.m.config.DatabaseInitializer - 数据库初始化完成
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-27 04:54:39 [http-nio-8083-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 36 ms
