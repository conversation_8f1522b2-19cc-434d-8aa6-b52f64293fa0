import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '', // 从环境变量获取API基础路径
  timeout: 15000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 在发送请求之前做些什么
    const userStore = useUserStore();
    if (userStore.token) {
      // 让每个请求携带自定义token
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${userStore.token}`
      };
    }
    return config;
  },
  (error: AxiosError) => {
    // 对请求错误做些什么
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data;
    // 如果返回的状态码不是200，说明请求出错了
    if (res.code && res.code !== '000000' && res.code !== 200) {
      ElMessage({
        message: res.message || res.msg || '请求失败',
        type: 'error',
        duration: 5 * 1000
      });

      // 401: 未登录或token已过期
      if (res.code === 401 || res.code === '000401') {
        // 重新登录
        ElMessageBox.confirm('您已登出，可以取消继续留在该页面，或者重新登录', '确认登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const userStore = useUserStore();
          const router = useRouter();
          userStore.resetToken();
          router.push(`/login?redirect=${router.currentRoute.value.fullPath}`);
        });
      }
      
      // 如果是下载类型的请求，直接返回response
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/octet-stream')) {
        return response;
      }
      
      return Promise.reject(new Error(res.message || res.msg || '请求失败'));
    } else {
      return res;
    }
  },
  (error: AxiosError) => {
    console.log('err: ' + error);
    let message = '';
    
    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 400:
          message = '请求错误';
          break;
        case 401:
          message = '未授权，请登录';
          // 处理401未授权的情况，重定向到登录页
          const userStore = useUserStore();
          const router = useRouter();
          userStore.resetToken();
          router.push('/login');
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = `请求地址出错: ${error.response.config.url}`;
          break;
        case 408:
          message = '请求超时';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        case 501:
          message = '服务未实现';
          break;
        case 502:
          message = '网关错误';
          break;
        case 503:
          message = '服务不可用';
          break;
        case 504:
          message = '网关超时';
          break;
        case 505:
          message = 'HTTP版本不支持';
          break;
        default:
          message = `未知错误: ${status}`;
      }
    } else if (error.request) {
      message = '服务器无响应';
    } else {
      message = '请求配置错误';
    }

    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    });
    
    return Promise.reject(error);
  }
);

/**
 * 封装的请求方法，增加了类型支持
 * @param config Axios请求配置
 * @returns Promise
 */
const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
  return service(config) as unknown as Promise<T>;
};

export default request; 