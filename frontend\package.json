{"name": "frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix"}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "path-browserify": "^1.0.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^20.15.10", "@typescript-eslint/eslint-plugin": "^7.12.0", "@typescript-eslint/parser": "^7.12.0", "@vitejs/plugin-vue": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.24.0", "typescript": "^5.5.2", "vite": "^7.0.4", "vue-tsc": "^2.0.0"}}