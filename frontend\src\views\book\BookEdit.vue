<template>
  <div class="book-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑图书' : '新增图书' }}</h2>
    </div>

    <el-card class="form-container">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        label-position="right">

        <el-form-item label="书名" prop="title">
          <el-input v-model="form.title" placeholder="请输入书名" />
        </el-form-item>

        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>

        <el-form-item label="出版社" prop="publisher">
          <el-input v-model="form.publisher" placeholder="请输入出版社" />
        </el-form-item>

        <el-form-item label="ISBN" prop="isbn">
          <el-input v-model="form.isbn" placeholder="请输入ISBN" />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="计算机科学" value="计算机科学" />
            <el-option label="文学" value="文学" />
            <el-option label="历史" value="历史" />
            <el-option label="艺术" value="艺术" />
            <el-option label="科学" value="科学" />
          </el-select>
        </el-form-item>

        <el-form-item label="出版日期" prop="publishDate">
          <el-date-picker
            v-model="form.publishDate"
            type="date"
            placeholder="选择出版日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="总数量" prop="totalCount">
          <el-input-number v-model="form.totalCount" :min="1" :max="1000" />
        </el-form-item>

        <el-form-item label="可借数量" prop="availableCount">
          <el-input-number v-model="form.availableCount" :min="0" :max="1000" />
        </el-form-item>

        <el-form-item label="状态" prop="available">
          <el-radio-group v-model="form.available">
            <el-radio :label="1">可借阅</el-radio>
            <el-radio :label="0">不可借阅</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="简介" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入简介" />
        </el-form-item>

        <el-form-item label="位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入图书位置信息（如书架号）" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 引入API，实际项目中需要添加
// import { getBook, createBook, updateBook } from '@/api/book'

export default {
  name: 'BookEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const bookId = computed(() => route.params.id)
    const isEdit = computed(() => !!bookId.value)

    // 表单数据
    const form = reactive({
      title: '',
      author: '',
      publisher: '',
      isbn: '',
      category: '',
      publishDate: '',
      totalCount: 1,
      availableCount: 1,
      available: 1,
      description: '',
      location: ''
    })

    // 表单验证规则
    const rules = reactive({
      title: [{ required: true, message: '请输入书名', trigger: 'blur' }],
      author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
      publisher: [{ required: true, message: '请输入出版社', trigger: 'blur' }],
      isbn: [{ required: true, message: '请输入ISBN', trigger: 'blur' }],
      category: [{ required: true, message: '请选择分类', trigger: 'change' }],
      publishDate: [{ required: true, message: '请选择出版日期', trigger: 'change' }],
      totalCount: [{ required: true, message: '请输入总数量', trigger: 'blur' }],
      availableCount: [{ required: true, message: '请输入可借数量', trigger: 'blur' }]
    })

    // 获取图书详情
    const fetchBookDetail = async (id) => {
      try {
        // 实际项目中这里需要调用API
        // const response = await getBook(id)
        // Object.assign(form, response.data)
        ElMessage.info('模拟加载图书数据')
        // 模拟数据
        Object.assign(form, {
          title: 'JavaScript高级程序设计',
          author: '尼古拉斯·泽卡斯',
          publisher: '人民邮电出版社',
          isbn: '9787115547538',
          category: '计算机科学',
          publishDate: '2021-01-01',
          totalCount: 10,
          availableCount: 5,
          available: 1,
          description: 'JavaScript 经典教程，涵盖了JavaScript的核心语法、进阶用法以及HTML5相关的API。',
          location: 'A区12架'
        })
      } catch (error) {
        ElMessage.error('获取图书信息失败')
        console.error(error)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        // 确保可借数量不大于总数量
        if (form.availableCount > form.totalCount) {
          ElMessage.warning('可借数量不能大于总数量')
          return
        }
        
        // 实际项目中这里需要调用API
        if (isEdit.value) {
          // await updateBook(bookId.value, form)
          ElMessage.success('更新图书成功')
        } else {
          // await createBook(form)
          ElMessage.success('创建图书成功')
        }
        
        // 返回列表页
        router.push({ name: 'BookList' })
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.back()
    }

    // 组件挂载时，如果是编辑模式，获取图书详情
    onMounted(() => {
      if (isEdit.value) {
        fetchBookDetail(bookId.value)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.book-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 800px;
}
</style> 