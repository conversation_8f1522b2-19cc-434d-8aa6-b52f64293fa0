<template>
  <div class="major-edit-container">
    <h2><el-icon><School /></el-icon> {{ isEdit ? '编辑专业' : '添加专业' }}</h2>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="major-form">
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专业代码" prop="code">
            <el-input v-model="form.code" placeholder="请输入专业代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入专业名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属院系" prop="departmentId">
            <el-select v-model="form.departmentId" placeholder="请选择院系" style="width: 100%">
              <el-option 
                v-for="dept in departmentOptions" 
                :key="dept.id" 
                :label="dept.name" 
                :value="dept.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学位类型" prop="degree">
            <el-select v-model="form.degree" placeholder="请选择学位类型" style="width: 100%">
              <el-option label="学士" value="BACHELOR"></el-option>
              <el-option label="硕士" value="MASTER"></el-option>
              <el-option label="博士" value="DOCTOR"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学制" prop="duration">
            <el-input-number v-model="form.duration" :min="1" :max="8" placeholder="学制年数" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="ACTIVE">启用</el-radio>
              <el-radio label="INACTIVE">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="专业介绍" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入专业介绍"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
        <el-button @click="handleCancel">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { School, Check, Close } from '@element-plus/icons-vue'
import { getMajorById, createMajor, updateMajor } from '@/api/major'
import { getAllDepartments } from '@/api/department'

export default {
  name: 'MajorEdit',
  components: {
    School, Check, Close
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)
    const departmentOptions = ref([])

    const isEdit = computed(() => !!route.params.id)

    // 表单数据
    const form = reactive({
      code: '',
      name: '',
      departmentId: null,
      degree: 'BACHELOR',
      duration: 4,
      status: 'ACTIVE',
      description: ''
    })

    // 表单验证规则
    const rules = {
      code: [
        { required: true, message: '请输入专业代码', trigger: 'blur' },
        { min: 2, max: 20, message: '专业代码长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入专业名称', trigger: 'blur' },
        { min: 2, max: 50, message: '专业名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      departmentId: [
        { required: true, message: '请选择所属院系', trigger: 'change' }
      ],
      degree: [
        { required: true, message: '请选择学位类型', trigger: 'change' }
      ],
      duration: [
        { required: true, message: '请输入学制', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    // 获取院系选项
    const fetchDepartmentOptions = async () => {
      try {
        const res = await getAllDepartments()
        if (res) {
          departmentOptions.value = res
        }
      } catch (error) {
        console.error('获取院系数据失败:', error)
      }
    }

    // 获取专业详情
    const fetchMajorDetail = async () => {
      if (!isEdit.value) return
      
      try {
        const res = await getMajorById(route.params.id)
        if (res) {
          Object.assign(form, res)
        }
      } catch (error) {
        console.error('获取专业详情失败:', error)
        ElMessage.error('获取专业详情失败')
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        loading.value = true

        if (isEdit.value) {
          await updateMajor(route.params.id, form)
          ElMessage.success('更新成功')
        } else {
          await createMajor(form)
          ElMessage.success('创建成功')
        }

        router.push('/majors')
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('保存专业失败:', error)
          ElMessage.error('保存失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.push('/majors')
    }

    onMounted(() => {
      fetchDepartmentOptions()
      fetchMajorDetail()
    })

    return {
      formRef,
      loading,
      departmentOptions,
      isEdit,
      form,
      rules,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.major-edit-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
}

.major-edit-container h2 {
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.major-form {
  max-width: 800px;
}
</style>
