-- 插入场馆预订数据
USE university_management;

-- 获取体育场馆的id范围
-- 插入场馆预订数据（使用正确的venue_id和user_id）
INSERT INTO venue_booking (venue_id, user_id, user_type, booking_date, start_time, end_time, purpose, status, create_time, update_time, is_deleted) VALUES
-- 学生预订记录 (user_type = 0 表示学生)
-- 使用student_id作为user_id
(341, 965, 0, '2024-07-20', '08:00', '10:00', 'Basketball practice with classmates', 1, NOW(), NOW(), 0),
(342, 966, 0, '2024-07-20', '14:00', '16:00', 'Football training session', 1, NOW(), NOW(), 0),
(343, 967, 0, '2024-07-21', '09:00', '11:00', 'Football match preparation', 1, NOW(), NOW(), 0),
(344, 968, 0, '2024-07-21', '16:00', '18:00', 'Basketball club activity', 1, NOW(), NOW(), 0),
(345, 969, 0, '2024-07-22', '10:00', '12:00', 'Basketball team practice', 1, NOW(), NOW(), 0),

(346, 970, 0, '2024-07-22', '15:00', '17:00', 'Tennis lesson', 1, NOW(), NOW(), 0),
(347, 971, 0, '2024-07-23', '08:00', '10:00', 'Tennis tournament practice', 1, NOW(), NOW(), 0),
(348, 972, 0, '2024-07-23', '18:00', '20:00', 'Gymnasium fitness training', 1, NOW(), NOW(), 0),
(349, 973, 0, '2024-07-24', '07:00', '08:00', 'Swimming practice', 1, NOW(), NOW(), 0),
(350, 974, 0, '2024-07-24', '19:00', '21:00', 'Fitness center workout', 1, NOW(), NOW(), 0),

(351, 975, 0, '2024-07-25', '16:00', '18:00', 'Table tennis club meeting', 1, NOW(), NOW(), 0),
(352, 976, 0, '2024-07-25', '14:00', '16:00', 'Badminton practice', 1, NOW(), NOW(), 0),
(353, 977, 0, '2024-07-26', '10:00', '12:00', 'Martial arts training', 1, NOW(), NOW(), 0),
(354, 978, 0, '2024-07-26', '15:00', '17:00', 'Rock climbing practice', 1, NOW(), NOW(), 0),

-- 教师预订记录 (user_type = 1 表示教师)
-- 使用teacher_id作为user_id
(341, 889, 1, '2024-07-27', '09:00', '11:00', 'Physical Education Class - Track and Field', 1, NOW(), NOW(), 0),
(342, 890, 1, '2024-07-27', '14:00', '16:00', 'Football PE Class', 1, NOW(), NOW(), 0),
(348, 891, 1, '2024-07-28', '10:00', '12:00', 'Basketball PE Class', 1, NOW(), NOW(), 0),
(349, 892, 1, '2024-07-28', '08:00', '09:00', 'Swimming PE Class', 1, NOW(), NOW(), 0),

-- 未来预订记录
(344, 979, 0, '2024-07-30', '16:00', '18:00', 'Basketball club weekly practice', 0, NOW(), NOW(), 0),
(345, 980, 0, '2024-07-30', '18:00', '20:00', 'Basketball team training', 0, NOW(), NOW(), 0),
(346, 981, 0, '2024-07-31', '09:00', '11:00', 'Tennis doubles practice', 0, NOW(), NOW(), 0),
(349, 982, 0, '2024-07-31', '07:00', '08:00', 'Morning swimming session', 0, NOW(), NOW(), 0),

-- 取消的预订记录
(350, 983, 0, '2024-07-29', '19:00', '21:00', 'Fitness training - cancelled due to schedule conflict', 2, NOW(), NOW(), 0),
(352, 984, 0, '2024-07-29', '15:00', '17:00', 'Badminton match - cancelled due to weather', 2, NOW(), NOW(), 0);
