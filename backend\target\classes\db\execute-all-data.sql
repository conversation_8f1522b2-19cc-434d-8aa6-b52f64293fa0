-- 执行所有扩展数据的脚本
-- 请按顺序执行以下SQL文件：

-- 1. 首先执行基础扩展数据
-- mysql -u root -p university_management < expanded-data.sql

-- 2. 然后执行学生数据（按顺序）
-- mysql -u root -p university_management < student-data.sql
-- mysql -u root -p university_management < student-data-part2.sql  
-- mysql -u root -p university_management < student-data-part3.sql
-- mysql -u root -p university_management < student-data-final.sql

-- 3. 最后执行完整的扩展数据
-- mysql -u root -p university_management < complete-expanded-data.sql

-- 或者可以直接在MySQL客户端中执行：
-- USE university_management;
-- \. expanded-data.sql
-- \. student-data.sql
-- \. student-data-part2.sql
-- \. student-data-part3.sql
-- \. student-data-final.sql
-- \. complete-expanded-data.sql

-- 数据统计摘要：
-- 院系：50个（原5个 + 新增45个）
-- 专业：50个（原10个 + 新增40个）
-- 班级：50个（原10个 + 新增40个）
-- 教师：100个（原8个 + 新增92个）
-- 学生：600个（原8个 + 新增592个）
-- 课程：50个（原10个 + 新增40个）
-- 教室：50个（原8个 + 新增42个）
-- 宿舍：50个（原8个 + 新增42个）
-- 图书：50本（原10本 + 新增40本）
-- 体育场馆：10个（原5个 + 新增5个）

-- 学生分布：
-- 2019级：20个学生（临床医学、口腔医学5年制）
-- 2020级：150个学生（各专业）
-- 2021级：200个学生（各专业）
-- 2022级：120个学生（各专业）
-- 2023级：110个学生（各专业）

-- 专业覆盖：
-- 工学：计算机、软件、网络、电子、通信、机械、工业设计、化工、材料、生物医学、环境、土木、能源等
-- 理学：数学、统计、物理、化学、生物技术、药学、中药学等
-- 管理学：工商管理、物流管理等
-- 文学：英语、新闻学、广播电视学等
-- 艺术学：视觉传达设计、产品设计、音乐表演、音乐学等
-- 教育学：体育教育、运动训练等
-- 法学：法学、知识产权等
-- 医学：临床医学、口腔医学、护理学、助产学等

SELECT '数据导入完成！' AS message;
SELECT 
    '院系数量' AS item, COUNT(*) AS count FROM department
UNION ALL
SELECT 
    '专业数量' AS item, COUNT(*) AS count FROM major
UNION ALL
SELECT 
    '班级数量' AS item, COUNT(*) AS count FROM class
UNION ALL
SELECT 
    '教师数量' AS item, COUNT(*) AS count FROM teacher
UNION ALL
SELECT 
    '学生数量' AS item, COUNT(*) AS count FROM student
UNION ALL
SELECT 
    '课程数量' AS item, COUNT(*) AS count FROM course
UNION ALL
SELECT 
    '教室数量' AS item, COUNT(*) AS count FROM classroom
UNION ALL
SELECT 
    '宿舍数量' AS item, COUNT(*) AS count FROM dormitory
UNION ALL
SELECT 
    '图书数量' AS item, COUNT(*) AS count FROM book
UNION ALL
SELECT 
    '体育场馆数量' AS item, COUNT(*) AS count FROM sports_venue;
