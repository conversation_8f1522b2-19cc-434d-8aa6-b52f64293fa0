package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 专业实体类
 */
@ApiModel(value = "Major", description = "专业信息")
@Entity
@TableName("major")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Major extends BaseEntity {

    /**
     * 专业编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("专业编号")
    private String majorNo;

    /**
     * 专业名称
     */
    @Column(nullable = false, length = 50)
    @ApiModelProperty("专业名称")
    private String name;

    /**
     * 院系ID
     */
    @Column
    @ApiModelProperty("院系ID")
    private Integer departmentId;

    /**
     * 专业描述
     */
    @Column(length = 500)
    @ApiModelProperty("专业描述")
    private String description;

    /**
     * 学制
     */
    @Column(length = 20)
    @ApiModelProperty("学制")
    private String length;

    /**
     * 学位
     */
    @Column(length = 20)
    @ApiModelProperty("学位")
    private String degree;

    /**
     * 院系 - 通过departmentId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "departmentId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Department department;

    /**
     * 班级列表
     */
    @OneToMany(mappedBy = "major", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Class> classes = new ArrayList<>();

    /**
     * 学生列表
     */
    @OneToMany(mappedBy = "major", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Student> students = new ArrayList<>();

    // Getter和Setter方法
    public String getMajorNo() {
        return majorNo;
    }

    public void setMajorNo(String majorNo) {
        this.majorNo = majorNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    public List<Class> getClasses() {
        return classes;
    }

    public void setClasses(List<Class> classes) {
        this.classes = classes;
    }

    public List<Student> getStudents() {
        return students;
    }

    public void setStudents(List<Student> students) {
        this.students = students;
    }
}