# 🚀 数据库英文数据快速修复指南

## ❌ 问题原因
错误 `ERROR 1271 (HY000): Illegal mix of collations for operation 'case'` 是由于数据库字符集和排序规则不匹配导致的。

## ✅ 解决方案

### 方法一：使用简单修复脚本（推荐）
```bash
# 进入数据库目录
cd backend/src/main/resources/db

# 执行简单修复脚本
mysql -u root -p7121020qing university_management < simple-chinese-fix.sql
```

### 方法二：使用字符集修复脚本
```bash
# 如果方法一不行，尝试这个
mysql -u root -p7121020qing university_management < fix-collation-chinese-data.sql
```

### 方法三：手动逐步执行
```bash
# 进入MySQL命令行
mysql -u root -p7121020qing university_management

# 然后逐步执行以下命令：
```

```sql
-- 设置字符集
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 修复班级数据（示例）
UPDATE class SET name = '计算机科学与技术2020级1班' WHERE name = 'Computer Science 2020 Class 1';
UPDATE class SET name = '软件工程2020级1班' WHERE name = 'Software Engineering 2020 Class 1';
UPDATE class SET name = '网络工程2020级1班' WHERE name = 'Network Engineering 2020 Class 1';

-- 修复专业数据（示例）
UPDATE major SET name = '计算机科学与技术' WHERE name = 'Computer Science and Technology';
UPDATE major SET name = '软件工程' WHERE name = 'Software Engineering';
UPDATE major SET name = '网络工程' WHERE name = 'Network Engineering';

-- 修复课程数据（示例）
UPDATE course SET name = '程序设计基础' WHERE name = 'Programming Fundamentals';
UPDATE course SET name = '数据结构' WHERE name = 'Data Structures';
UPDATE course SET name = '算法分析' WHERE name = 'Algorithm Analysis';

-- 修复教室数据（示例）
UPDATE classroom SET name = '计算机实验室1' WHERE name = 'Computer Lab 1';
UPDATE classroom SET name = '计算机实验室2' WHERE name = 'Computer Lab 2';
UPDATE classroom SET name = '网络实验室' WHERE name = 'Network Lab';

-- 修复建筑名称（示例）
UPDATE classroom SET building = '第一教学楼' WHERE building = 'Teaching Building A';
UPDATE classroom SET building = '第二教学楼' WHERE building = 'Teaching Building B';
UPDATE classroom SET building = '实验楼' WHERE building = 'Laboratory Building';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;
```

## 🔍 验证修复结果

### 检查班级数据
```sql
SELECT name FROM class WHERE name LIKE '%级%班' LIMIT 10;
```

### 检查专业数据
```sql
SELECT name FROM major WHERE name NOT LIKE '%Engineering%' LIMIT 10;
```

### 检查课程数据
```sql
SELECT name FROM course WHERE name NOT LIKE '%Programming%' LIMIT 10;
```

### 检查教室数据
```sql
SELECT name, building FROM classroom WHERE name LIKE '%实验室%' OR name LIKE '%教室%' LIMIT 10;
```

### 统计修复结果
```sql
SELECT 
    '班级数据' AS 类型, 
    COUNT(*) AS 总数, 
    SUM(CASE WHEN name LIKE '%级%班' THEN 1 ELSE 0 END) AS 中文数量
FROM class
UNION ALL
SELECT 
    '专业数据', 
    COUNT(*), 
    SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END)
FROM major
UNION ALL
SELECT 
    '课程数据', 
    COUNT(*), 
    SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' THEN 1 ELSE 0 END)
FROM course
UNION ALL
SELECT 
    '教室数据', 
    COUNT(*), 
    SUM(CASE WHEN name LIKE '%实验室%' OR name LIKE '%教室%' THEN 1 ELSE 0 END)
FROM classroom;
```

## 📋 修复内容预览

### 班级名称修复示例
- `Computer Science 2020 Class 1` → `计算机科学与技术2020级1班`
- `Software Engineering 2021 Class 1` → `软件工程2021级1班`
- `Network Engineering 2020 Class 1` → `网络工程2020级1班`

### 专业名称修复示例
- `Computer Science and Technology` → `计算机科学与技术`
- `Software Engineering` → `软件工程`
- `Electronic Information Engineering` → `电子信息工程`

### 课程名称修复示例
- `Programming Fundamentals` → `程序设计基础`
- `Data Structures` → `数据结构`
- `Computer Networks` → `计算机网络`

### 教室名称修复示例
- `Computer Lab 1` → `计算机实验室1`
- `Teaching Building A` → `第一教学楼`
- `Laboratory Building` → `实验楼`

## ⚠️ 注意事项

1. **备份数据**：执行前请备份数据库
   ```bash
   mysqldump -u root -p7121020qing university_management > backup_before_fix.sql
   ```

2. **字符集问题**：如果仍有字符集问题，可以尝试：
   ```sql
   ALTER DATABASE university_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **分步执行**：如果脚本执行失败，可以分步手动执行

4. **检查结果**：执行完成后务必检查修复结果

## 🎉 成功标志

修复成功后，您应该看到：
- ✅ 班级名称显示为：`计算机科学与技术2020级1班`
- ✅ 专业名称显示为：`计算机科学与技术`
- ✅ 课程名称显示为：`程序设计基础`
- ✅ 教室名称显示为：`计算机实验室1`
- ✅ 建筑名称显示为：`第一教学楼`

## 🔧 故障排除

### 问题1：仍有字符集错误
```sql
-- 检查数据库字符集
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 修改数据库字符集
ALTER DATABASE university_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题2：部分数据未更新
```sql
-- 查找剩余的英文数据
SELECT name FROM class WHERE name LIKE '%Class%' OR name LIKE '%Engineering%';
SELECT name FROM major WHERE name LIKE '%Engineering%' OR name LIKE '%Science%';
SELECT name FROM course WHERE name LIKE '%Programming%' OR name LIKE '%Data%';
```

### 问题3：权限问题
```bash
# 确保有足够的权限
mysql -u root -p7121020qing -e "SHOW GRANTS;"
```

---

**💡 提示**：建议使用 `simple-chinese-fix.sql` 脚本，它避免了复杂的CASE语句，减少了字符集冲突的可能性。
