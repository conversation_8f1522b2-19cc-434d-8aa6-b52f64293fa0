/**
 * 模拟数据配置
 * 控制是否启用模拟数据服务
 */

// 模拟数据开关配置
export const mockConfig = {
  // 完全禁用模拟数据，强制使用真实数据库数据
  enabled: false,

  // 模拟延迟时间（毫秒）
  delay: 500,

  // 是否显示模拟数据提示
  showTips: true,

  // 模拟数据的模块开关（全部禁用，使用真实API）
  modules: {
    students: false,      // 学生管理 - 使用真实API
    teachers: false,      // 教师管理 - 使用真实API
    courses: false,       // 课程管理 - 使用真实API
    departments: false,   // 院系管理 - 使用真实API
    majors: false,        // 专业管理 - 使用真实API
    classes: false,       // 班级管理 - 使用真实API
    books: false,         // 图书管理 - 使用真实API
    borrowingRecords: false, // 借阅记录 - 使用真实API
    classrooms: false,    // 教室管理 - 使用真实API
    dormitories: false,   // 宿舍管理 - 使用真实API
    sportsVenues: false,  // 体育场馆 - 使用真实API
    users: false,         // 用户管理 - 使用真实API
    roles: false          // 角色管理 - 使用真实API
  }
}

// 切换模拟数据状态（强制禁用模拟数据）
export const toggleMockData = (enabled) => {
  // 强制禁用模拟数据，确保只使用真实数据
  mockConfig.enabled = false
  localStorage.setItem('useMockData', 'false')

  if (mockConfig.showTips) {
    console.log('🔧 模拟数据已被强制禁用，系统只使用真实数据库数据')
  }
}

// 检查指定模块是否启用模拟数据（强制返回false，确保使用真实数据）
export const isMockEnabled = (module) => {
  return false // 强制禁用所有模拟数据，确保使用真实API
}

// 获取模拟数据配置
export const getMockConfig = () => {
  return { ...mockConfig }
}

// 设置模拟延迟
export const setMockDelay = (delay) => {
  mockConfig.delay = delay
}

// 显示模拟数据状态
export const showMockStatus = () => {
  console.group('🔧 模拟数据服务状态')
  console.log('启用状态:', mockConfig.enabled ? '✅ 已启用' : '❌ 已关闭')
  console.log('延迟时间:', mockConfig.delay + 'ms')
  console.log('模块状态:')
  
  Object.entries(mockConfig.modules).forEach(([module, enabled]) => {
    const status = enabled && mockConfig.enabled ? '✅' : '❌'
    console.log(`  ${module}: ${status}`)
  })
  
  console.groupEnd()
}

// 在开发环境下显示真实数据模式提示
if (import.meta.env.DEV && mockConfig.showTips) {
  console.log('%c📡 真实数据模式已启用', 'color: #67C23A; font-weight: bold;')
  console.log('💡 系统正在使用后端数据库的真实数据')
  console.log('🔗 后端API地址: http://localhost:8083')
}
