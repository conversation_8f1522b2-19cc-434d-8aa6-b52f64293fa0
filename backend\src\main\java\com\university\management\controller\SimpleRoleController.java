package com.university.management.controller;

import com.university.management.model.vo.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简单角色管理控制器
 * 提供基本的角色数据，避免复杂的实体类问题
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/api/roles")
public class SimpleRoleController {

    @ApiOperation("获取所有角色")
    @GetMapping
    public ApiResponse<List<Map<String, Object>>> getAllRoles() {
        List<Map<String, Object>> roles = createMockRoles();
        return ApiResponse.success("获取角色列表成功", roles);
    }

    @ApiOperation("分页获取角色")
    @GetMapping("/page")
    public ApiResponse<Map<String, Object>> getRolesByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String sortBy) {
        
        List<Map<String, Object>> allRoles = createMockRoles();
        int start = page * size;
        int end = Math.min(start + size, allRoles.size());
        List<Map<String, Object>> pageRoles = allRoles.subList(start, end);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", pageRoles);
        result.put("totalElements", allRoles.size());
        result.put("totalPages", (int) Math.ceil((double) allRoles.size() / size));
        result.put("size", size);
        result.put("number", page);
        result.put("first", page == 0);
        result.put("last", page >= Math.ceil((double) allRoles.size() / size) - 1);
        
        return ApiResponse.success("获取角色分页数据成功", result);
    }

    @ApiOperation("根据ID获取角色")
    @GetMapping("/{id}")
    public ApiResponse<Map<String, Object>> getRoleById(@PathVariable Integer id) {
        List<Map<String, Object>> roles = createMockRoles();
        for (Map<String, Object> role : roles) {
            if (role.get("id").equals(id)) {
                return ApiResponse.success("获取角色成功", role);
            }
        }
        return ApiResponse.errorGeneric(404, "角色不存在");
    }

    /**
     * 创建模拟角色数据
     */
    private List<Map<String, Object>> createMockRoles() {
        List<Map<String, Object>> roles = new ArrayList<>();
        
        Map<String, Object> role1 = new HashMap<>();
        role1.put("id", 1);
        role1.put("roleName", "超级管理员");
        role1.put("roleCode", "SUPER_ADMIN");
        role1.put("description", "系统超级管理员，拥有所有权限");
        role1.put("status", 0);
        role1.put("createTime", "2024-01-01 10:00:00");
        roles.add(role1);
        
        Map<String, Object> role2 = new HashMap<>();
        role2.put("id", 2);
        role2.put("roleName", "系统管理员");
        role2.put("roleCode", "ADMIN");
        role2.put("description", "系统管理员，负责系统配置和用户管理");
        role2.put("status", 0);
        role2.put("createTime", "2024-01-01 10:00:00");
        roles.add(role2);
        
        Map<String, Object> role3 = new HashMap<>();
        role3.put("id", 3);
        role3.put("roleName", "教务管理员");
        role3.put("roleCode", "ACADEMIC_ADMIN");
        role3.put("description", "教务管理员，负责课程和成绩管理");
        role3.put("status", 0);
        role3.put("createTime", "2024-01-01 10:00:00");
        roles.add(role3);
        
        Map<String, Object> role4 = new HashMap<>();
        role4.put("id", 4);
        role4.put("roleName", "教师");
        role4.put("roleCode", "TEACHER");
        role4.put("description", "教师角色，可以管理自己的课程和学生");
        role4.put("status", 0);
        role4.put("createTime", "2024-01-01 10:00:00");
        roles.add(role4);
        
        Map<String, Object> role5 = new HashMap<>();
        role5.put("id", 5);
        role5.put("roleName", "学生");
        role5.put("roleCode", "STUDENT");
        role5.put("description", "学生角色，可以查看课程和成绩");
        role5.put("status", 0);
        role5.put("createTime", "2024-01-01 10:00:00");
        roles.add(role5);
        
        Map<String, Object> role6 = new HashMap<>();
        role6.put("id", 6);
        role6.put("roleName", "访客");
        role6.put("roleCode", "GUEST");
        role6.put("description", "访客角色，只能查看基本信息");
        role6.put("status", 1);
        role6.put("createTime", "2024-01-01 10:00:00");
        roles.add(role6);
        
        return roles;
    }
}
