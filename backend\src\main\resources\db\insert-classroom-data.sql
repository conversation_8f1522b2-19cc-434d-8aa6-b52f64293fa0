-- 插入教室数据
USE university_management;

-- 插入教室数据
INSERT INTO classroom (room_no, name, building, floor, capacity, type, equipment, status) VALUES
-- 教学楼A - 计算机学院
('A101', 'Computer Lab 1', 'Teaching Building A', 1, 60, 1, 'Computers, Projector, Air Conditioning', 0),
('A102', 'Computer Lab 2', 'Teaching Building A', 1, 60, 1, 'Computers, Projector, Air Conditioning', 0),
('A103', 'Lecture Hall A103', 'Teaching Building A', 1, 120, 0, 'Projector, Sound System, Air Conditioning', 0),
('A201', 'Computer Lab 3', 'Teaching Building A', 2, 50, 1, 'Computers, Projector, Air Conditioning', 0),
('A202', 'AI Research Lab', 'Teaching Building A', 2, 40, 1, 'High-performance Computers, GPU Cluster', 0),
('A203', 'Lecture Hall A203', 'Teaching Building A', 2, 100, 0, 'Projector, Sound System, Air Conditioning', 0),
('A301', 'Software Engineering Lab', 'Teaching Building A', 3, 45, 1, 'Computers, Development Tools, Projector', 0),
('A302', 'Database Lab', 'Teaching Building A', 3, 50, 1, 'Computers, Database Servers, Projector', 0),

-- 教学楼B - 电子工程学院
('B101', 'Electronics Lab 1', 'Teaching Building B', 1, 40, 1, 'Oscilloscopes, Signal Generators, Multimeters', 0, 'Basic electronics laboratory'),
('B102', 'Electronics Lab 2', 'Teaching Building B', 1, 40, 1, 'Oscilloscopes, Signal Generators, Multimeters', 0, 'Advanced electronics laboratory'),
('B103', 'Lecture Hall B103', 'Teaching Building B', 1, 80, 0, 'Projector, Sound System, Air Conditioning', 0, 'Electronics engineering lecture hall'),
('B201', 'Communication Lab', 'Teaching Building B', 2, 35, 1, 'Communication Equipment, Spectrum Analyzers', 0, 'Communication systems laboratory'),
('B202', 'Control Systems Lab', 'Teaching Building B', 2, 35, 1, 'Control Equipment, Simulation Software', 0, 'Automatic control laboratory'),
('B203', 'Lecture Hall B203', 'Teaching Building B', 2, 90, 0, 'Projector, Sound System, Air Conditioning', 0, 'Medium lecture hall'),

-- 教学楼C - 机械工程学院
('C101', 'Mechanical Drawing Room', 'Teaching Building C', 1, 50, 0, 'Drawing Tables, CAD Computers, Projector', 0, 'Mechanical drawing classroom'),
('C102', 'Materials Lab', 'Teaching Building C', 1, 30, 1, 'Testing Equipment, Material Samples', 0, 'Materials science laboratory'),
('C103', 'Lecture Hall C103', 'Teaching Building C', 1, 100, 0, 'Projector, Sound System, Air Conditioning', 0, 'Mechanical engineering lecture hall'),
('C201', 'Manufacturing Lab', 'Teaching Building C', 2, 25, 1, 'CNC Machines, 3D Printers, Tools', 0, 'Manufacturing technology laboratory'),
('C202', 'Thermodynamics Lab', 'Teaching Building C', 2, 30, 1, 'Heat Transfer Equipment, Measurement Tools', 0, 'Thermodynamics laboratory'),

-- 教学楼D - 经济管理学院
('D101', 'Business Simulation Room', 'Teaching Building D', 1, 60, 1, 'Computers, Business Software, Projector', 0, 'Business simulation laboratory'),
('D102', 'Lecture Hall D102', 'Teaching Building D', 1, 150, 0, 'Projector, Sound System, Air Conditioning', 0, 'Large business lecture hall'),
('D201', 'Economics Research Room', 'Teaching Building D', 2, 40, 0, 'Computers, Statistical Software, Projector', 0, 'Economics research classroom'),
('D202', 'Accounting Lab', 'Teaching Building D', 2, 50, 1, 'Computers, Accounting Software, Projector', 0, 'Accounting practice laboratory'),

-- 教学楼E - 外国语学院
('E101', 'Language Lab 1', 'Teaching Building E', 1, 40, 1, 'Audio Equipment, Computers, Headphones', 0, 'English language laboratory'),
('E102', 'Language Lab 2', 'Teaching Building E', 1, 40, 1, 'Audio Equipment, Computers, Headphones', 0, 'Japanese language laboratory'),
('E103', 'Lecture Hall E103', 'Teaching Building E', 1, 80, 0, 'Projector, Sound System, Air Conditioning', 0, 'Foreign language lecture hall'),
('E201', 'Translation Lab', 'Teaching Building E', 2, 30, 1, 'Computers, Translation Software, Audio Equipment', 0, 'Translation practice laboratory'),

-- 教学楼F - 艺术设计学院
('F101', 'Design Studio 1', 'Teaching Building F', 1, 30, 1, 'Drawing Tables, Computers, Design Software', 0, 'Visual design studio'),
('F102', 'Design Studio 2', 'Teaching Building F', 1, 30, 1, 'Drawing Tables, Computers, Design Software', 0, 'Environmental design studio'),
('F103', 'Art Gallery', 'Teaching Building F', 1, 100, 2, 'Display Equipment, Lighting System', 0, 'Art exhibition space'),
('F201', 'Digital Media Lab', 'Teaching Building F', 2, 25, 1, 'High-end Computers, Graphics Tablets, Software', 0, 'Digital media design laboratory');
