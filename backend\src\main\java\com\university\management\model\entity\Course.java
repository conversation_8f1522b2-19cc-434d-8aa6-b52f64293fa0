package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

/**
 * 课程实体类
 */
@Entity
@Table(name = "course")
@TableName("course")
@ApiModel(value = "课程实体", description = "课程信息")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Course extends BaseEntity {

    /**
     * 课程编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("课程编号")
    private String courseNo;

    /**
     * 课程名称
     */
    @Column(nullable = false, length = 50)
    @ApiModelProperty("课程名称")
    private String name;

    /**
     * 课程类型（0-必修，1-选修）
     */
    @Column
    @ApiModelProperty("课程类型（0-必修，1-选修）")
    private Integer type;

    /**
     * 学分
     */
    @Column
    @ApiModelProperty("学分")
    private Float credit;

    /**
     * 学时
     */
    @Column
    @ApiModelProperty("学时")
    private Integer period;

    /**
     * 课时
     */
    @Column
    @ApiModelProperty("课时")
    private Integer hours;

    /**
     * 开课学期
     */
    @Column(length = 20)
    @ApiModelProperty("开课学期")
    private String semester;

    /**
     * 院系ID
     */
    @Column
    @ApiModelProperty("院系ID")
    private Integer departmentId;

    /**
     * 学院ID
     */
    @Column
    @ApiModelProperty("学院ID")
    private Integer collegeId;

    /**
     * 专业ID
     */
    @Column
    @ApiModelProperty("专业ID")
    private Integer majorId;

    /**
     * 教师ID
     */
    @Column
    @ApiModelProperty("教师ID")
    private Integer teacherId;

    /**
     * 课程简介
     */
    @Column(length = 500)
    @ApiModelProperty("课程简介")
    private String description;

    /**
     * 课程状态（0-正常，1-停用）
     */
    @Column
    @ApiModelProperty("课程状态（0-正常，1-停用）")
    private Integer status;

    /**
     * 教师
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacherId", insertable = false, updatable = false)
    @TableField(exist = false)
    @JsonIgnore
    private Teacher teacher;

    // Getter和Setter方法
    public String getCourseNo() {
        return courseNo;
    }

    public void setCourseNo(String courseNo) {
        this.courseNo = courseNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Float getCredit() {
        return credit;
    }

    public void setCredit(Float credit) {
        this.credit = credit;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getHours() {
        return hours;
    }

    public void setHours(Integer hours) {
        this.hours = hours;
    }

    public String getSemester() {
        return semester;
    }

    public void setSemester(String semester) {
        this.semester = semester;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Integer collegeId) {
        this.collegeId = collegeId;
    }

    public Integer getMajorId() {
        return majorId;
    }

    public void setMajorId(Integer majorId) {
        this.majorId = majorId;
    }

    public Integer getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Integer teacherId) {
        this.teacherId = teacherId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Teacher getTeacher() {
        return teacher;
    }

    public void setTeacher(Teacher teacher) {
        this.teacher = teacher;
    }
}