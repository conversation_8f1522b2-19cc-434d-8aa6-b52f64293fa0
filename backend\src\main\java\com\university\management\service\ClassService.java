package com.university.management.service;

import com.university.management.model.entity.Class;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ClassService {
    
    List<Class> findAll();
    
    Page<Class> findAll(PageRequest pageRequest);
    
    Page<Class> findByConditions(String name, Integer majorId, Integer grade, PageRequest pageRequest);
    
    Optional<Class> findById(Integer id);

    Class save(Class clazz);

    void deleteById(Integer id);
    
    List<Class> findByMajorId(Integer majorId);

    List<Class> findByGrade(Integer grade);

    Map<String, Object> getClassStats();
}
