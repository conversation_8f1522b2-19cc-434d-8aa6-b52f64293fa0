<template>
  <div class="app-wrapper" :class="{'is-collapse': isCollapse, 'is-dashboard': isDashboard}">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <img src="@/assets/logo.png" class="logo" alt="logo">
        <h1 v-show="!isCollapse" class="title">学生管理系统</h1>
      </div>
      <sidebar :is-collapse="isCollapse" :is-dashboard="isDashboard" />
    </div>
    
    <!-- 主内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <div class="left-menu">
          <i 
            :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'" 
            class="fold-btn"
            @click="toggleSidebar"
          ></i>
          <breadcrumb class="breadcrumb-container" />
        </div>
        
        <div class="right-menu">
          <!-- 全局搜索 -->
          <div class="search-container">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索学生、教师、课程..."
              size="small"
              style="width: 200px;"
              @keyup.enter="handleGlobalSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 通知中心 -->
          <el-dropdown trigger="click" class="notification-dropdown">
            <span class="notification-btn">
              <el-badge :value="unreadCount" :hidden="unreadCount === 0">
                <el-icon :size="18"><Bell /></el-icon>
              </el-badge>
            </span>
            <template #dropdown>
              <el-dropdown-menu class="notification-menu">
                <div class="notification-header">
                  <span>通知中心</span>
                  <el-button type="text" size="small" @click="markAllAsRead">全部已读</el-button>
                </div>
                <div class="notification-list">
                  <div
                    v-for="notification in notifications"
                    :key="notification.id"
                    class="notification-item"
                    :class="{ 'unread': !notification.read }"
                    @click="handleNotificationClick(notification)"
                  >
                    <div class="notification-content">
                      <div class="notification-title">{{ notification.title }}</div>
                      <div class="notification-desc">{{ notification.description }}</div>
                      <div class="notification-time">{{ formatTime(notification.time) }}</div>
                    </div>
                  </div>
                </div>
                <div class="notification-footer">
                  <el-button type="text" size="small" @click="viewAllNotifications">查看全部</el-button>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 主题切换 -->
          <el-tooltip content="切换主题" placement="bottom">
            <div class="theme-switch" @click="toggleTheme">
              <el-icon :size="18">
                <Sunny v-if="isDarkTheme" />
                <Moon v-else />
              </el-icon>
            </div>
          </el-tooltip>

          <!-- 全屏切换 -->
          <el-tooltip content="全屏" placement="bottom">
            <div class="fullscreen-btn" @click="toggleFullscreen">
              <el-icon :size="18">
                <CopyDocument v-if="isFullscreen" />
                <FullScreen v-else />
              </el-icon>
            </div>
          </el-tooltip>

          <!-- 用户菜单 -->
          <el-dropdown trigger="click" class="user-dropdown">
            <span class="user-dropdown-link">
              <el-avatar :size="32" :src="userAvatar" class="user-avatar">
                {{ username.charAt(0) }}
              </el-avatar>
              <span class="username">{{ username }}</span>
              <el-icon :size="12"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="viewProfile">
                  <el-icon><User /></el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item @click="changePassword">
                  <el-icon><Key /></el-icon>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item @click="viewSettings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容主体 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Bell, User, Setting, Sunny, Moon, FullScreen, CopyDocument, ArrowDown, Key, SwitchButton } from '@element-plus/icons-vue'
import Sidebar from './components/Sidebar.vue'
import Breadcrumb from './components/Breadcrumb.vue'

export default {
  name: 'Layout',
  components: {
    Sidebar,
    Breadcrumb,
    Search,
    Bell,
    User,
    Setting,
    Sunny,
    Moon,
    FullScreen,
    CopyDocument,
    ArrowDown,
    Key,
    SwitchButton
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()

    // 侧边栏折叠状态
    const isCollapse = ref(false)

    // 判断是否为首页
    const isDashboard = computed(() => {
      return route.path === '/dashboard' || route.path === '/'
    })

    // 切换侧边栏折叠状态
    const toggleSidebar = () => {
      isCollapse.value = !isCollapse.value
    }

    // 用户信息
    const username = ref('管理员')
    const userAvatar = ref('')

    // 搜索相关
    const searchKeyword = ref('')

    // 通知相关
    const unreadCount = ref(3)
    const notifications = ref([
      {
        id: 1,
        title: '系统更新通知',
        description: '系统将于今晚22:00进行维护更新',
        time: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
        read: false
      },
      {
        id: 2,
        title: '新学期课程安排',
        description: '2024春季学期课程安排已发布，请及时查看',
        time: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
        read: false
      },
      {
        id: 3,
        title: '学生信息更新',
        description: '请各院系及时更新学生基本信息',
        time: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
        read: true
      }
    ])

    // 主题相关
    const isDarkTheme = ref(false)

    // 全屏相关
    const isFullscreen = ref(false)

    // 全局搜索
    const handleGlobalSearch = () => {
      if (!searchKeyword.value.trim()) {
        ElMessage.warning('请输入搜索关键词')
        return
      }

      // 这里可以实现全局搜索逻辑
      ElMessage.info(`搜索: ${searchKeyword.value}`)
      // 可以跳转到搜索结果页面
      // router.push(`/search?q=${encodeURIComponent(searchKeyword.value)}`)
    }

    // 通知相关方法
    const handleNotificationClick = (notification) => {
      if (!notification.read) {
        notification.read = true
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      // 可以根据通知类型跳转到相应页面
      ElMessage.info(`查看通知: ${notification.title}`)
    }

    const markAllAsRead = () => {
      notifications.value.forEach(n => n.read = true)
      unreadCount.value = 0
      ElMessage.success('所有通知已标记为已读')
    }

    const viewAllNotifications = () => {
      router.push('/notifications')
    }

    // 主题切换
    const toggleTheme = () => {
      isDarkTheme.value = !isDarkTheme.value
      const theme = isDarkTheme.value ? 'dark' : 'light'
      document.documentElement.setAttribute('data-theme', theme)
      localStorage.setItem('theme', theme)
      ElMessage.success(`已切换到${isDarkTheme.value ? '深色' : '浅色'}主题`)
    }

    // 全屏切换
    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
        isFullscreen.value = true
      } else {
        document.exitFullscreen()
        isFullscreen.value = false
      }
    }

    // 用户菜单方法
    const viewProfile = () => {
      router.push('/profile')
    }

    const changePassword = () => {
      router.push('/change-password')
    }

    const viewSettings = () => {
      router.push('/settings')
    }

    // 格式化时间
    const formatTime = (time) => {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }

    // 退出登录
    const logout = () => {
      ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除Token
        localStorage.removeItem('token')
        // 跳转到登录页
        router.push('/login')
        // 提示
        ElMessage.success('退出登录成功')
      }).catch(() => {
        // 取消退出操作
      })
    }
    
    return {
      isCollapse,
      isDashboard,
      toggleSidebar,
      username,
      userAvatar,
      searchKeyword,
      unreadCount,
      notifications,
      isDarkTheme,
      isFullscreen,
      handleGlobalSearch,
      handleNotificationClick,
      markAllAsRead,
      viewAllNotifications,
      toggleTheme,
      toggleFullscreen,
      viewProfile,
      changePassword,
      viewSettings,
      formatTime,
      logout
    }
  }
}
</script>

<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.is-collapse .sidebar-container {
  width: 64px;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #2b3649;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.title {
  color: #fff;
  font-size: 18px;
  margin: 0;
  white-space: nowrap;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
}

.left-menu {
  display: flex;
  align-items: center;
}

.fold-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 15px;
}

.breadcrumb-container {
  display: inline-block;
}

.right-menu {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-container {
  margin-right: 10px;
}

.notification-dropdown {
  cursor: pointer;
}

.notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.notification-btn:hover {
  background-color: #409EFF;
  color: white;
}

.notification-btn i {
  font-size: 18px;
}

.theme-switch,
.fullscreen-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
}

.theme-switch:hover,
.fullscreen-btn:hover {
  background-color: #409EFF;
  color: white;
}

.theme-switch i,
.fullscreen-btn i {
  font-size: 16px;
}

.user-dropdown {
  cursor: pointer;
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  transition: all 0.3s;
}

.user-dropdown-link:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.user-avatar {
  margin-right: 4px;
}

.username {
  font-weight: 500;
}

/* 通知菜单样式 */
.notification-menu {
  width: 320px;
  max-height: 400px;
  padding: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #ecf5ff;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #409EFF;
}

.notification-content {
  position: relative;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #c0c4cc;
}

.notification-footer {
  padding: 8px 16px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

.app-main {
  flex: 1;
  overflow: auto;
  padding: 15px;
  background-color: #f0f2f5;
}

/* 页面过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 