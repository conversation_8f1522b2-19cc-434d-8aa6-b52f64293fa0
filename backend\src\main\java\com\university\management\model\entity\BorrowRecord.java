package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 借阅记录实体类
 */
@ApiModel(value = "BorrowRecord", description = "借阅记录信息")
@Entity
@TableName("borrow_record")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class BorrowRecord extends BaseEntity {

    /**
     * 借阅编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("借阅编号")
    private String borrowNo;

    /**
     * 学生ID
     */
    @Column
    @ApiModelProperty("学生ID")
    private Integer studentId;

    /**
     * 图书ID
     */
    @Column
    @ApiModelProperty("图书ID")
    private Integer bookId;

    /**
     * 借阅日期
     */
    @Column
    @ApiModelProperty("借阅日期")
    private LocalDate borrowDate;

    /**
     * 应还日期
     */
    @Column
    @ApiModelProperty("应还日期")
    private LocalDate dueDate;

    /**
     * 预期归还日期（别名）- 移除重复映射，使用getter方法返回dueDate的值
     */
    @Transient
    @ApiModelProperty("预期归还日期")
    private LocalDate expectedReturnDate;

    /**
     * 实际归还日期
     */
    @Column
    @ApiModelProperty("实际归还日期")
    private LocalDate returnDate;

    /**
     * 实际归还日期（别名）- 移除重复映射，使用getter方法返回returnDate的值
     */
    @Transient
    @ApiModelProperty("实际归还日期")
    private LocalDate actualReturnDate;

    /**
     * 续借次数
     */
    @Column
    @ApiModelProperty("续借次数")
    private Integer renewCount;

    /**
     * 罚款金额
     */
    @Column
    @ApiModelProperty("罚款金额")
    private BigDecimal fine;

    /**
     * 状态（0-借阅中，1-已归还，2-已逾期，3-已续借）
     */
    @Column
    @ApiModelProperty("状态（0-借阅中，1-已归还，2-已逾期，3-已续借）")
    private Integer status;

    /**
     * 学生
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "studentId", insertable = false, updatable = false)
    @TableField(exist = false)
    @JsonIgnore
    private Student student;

    /**
     * 图书
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bookId", insertable = false, updatable = false)
    @TableField(exist = false)
    @JsonIgnore
    private Book book;

    // Getter和Setter方法
    public String getBorrowNo() {
        return borrowNo;
    }

    public void setBorrowNo(String borrowNo) {
        this.borrowNo = borrowNo;
    }

    public Integer getStudentId() {
        return studentId;
    }

    public void setStudentId(Integer studentId) {
        this.studentId = studentId;
    }

    public Integer getBookId() {
        return bookId;
    }

    public void setBookId(Integer bookId) {
        this.bookId = bookId;
    }

    public LocalDate getBorrowDate() {
        return borrowDate;
    }

    public void setBorrowDate(LocalDate borrowDate) {
        this.borrowDate = borrowDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDate getExpectedReturnDate() {
        return this.dueDate;
    }

    public void setExpectedReturnDate(LocalDate expectedReturnDate) {
        this.dueDate = expectedReturnDate;
    }

    public LocalDate getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(LocalDate returnDate) {
        this.returnDate = returnDate;
    }

    public LocalDate getActualReturnDate() {
        return this.returnDate;
    }

    public void setActualReturnDate(LocalDate actualReturnDate) {
        this.returnDate = actualReturnDate;
    }

    public Integer getRenewCount() {
        return renewCount;
    }

    public void setRenewCount(Integer renewCount) {
        this.renewCount = renewCount;
    }

    public BigDecimal getFine() {
        return fine;
    }

    public void setFine(BigDecimal fine) {
        this.fine = fine;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Student getStudent() {
        return student;
    }

    public void setStudent(Student student) {
        this.student = student;
    }

    public Book getBook() {
        return book;
    }

    public void setBook(Book book) {
        this.book = book;
    }
}