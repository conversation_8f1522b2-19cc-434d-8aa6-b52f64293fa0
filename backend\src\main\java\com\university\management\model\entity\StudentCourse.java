package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 选课记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "StudentCourse", description = "选课记录信息")
@Entity
@TableName("student_course")
public class StudentCourse extends BaseEntity {

    @ApiModelProperty(value = "学生ID")
    @Column(name = "student_id", nullable = false)
    private Integer studentId;

    @ApiModelProperty(value = "排课ID")
    @Column(name = "schedule_id", nullable = false)
    private Integer scheduleId;

    @ApiModelProperty(value = "成绩")
    @Column(precision = 5, scale = 2)
    private BigDecimal score;

    @ApiModelProperty(value = "绩点")
    @Column(precision = 3, scale = 2)
    private BigDecimal gradePoint;

    @ApiModelProperty(value = "评价")
    @Column(length = 500)
    private String evaluation;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private Student student;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "schedule_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private Schedule schedule;
} 