<template>
  <div class="sidebar" :class="{ 'dashboard-mode': isDashboard }">
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :collapse="isCollapse"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      unique-opened
      router
    >
      <sidebar-item
        v-for="route in routes"
        :key="route.path"
        :item="route"
        :base-path="route.path"
        :is-dashboard="isDashboard"
      />
    </el-menu>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import SidebarItem from './SidebarItem.vue'

export default {
  name: 'Sidebar',
  components: { SidebarItem },
  props: {
    isCollapse: {
      type: Boolean,
      default: false
    },
    isDashboard: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const route = useRoute()
    
    // 当前激活的菜单
    const activeMenu = computed(() => {
      const { meta, path } = route
      // 如果设置了activeMenu属性，则使用它
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    })

    // 导航菜单路由
    const routes = [
      {
        path: '/dashboard',
        name: 'Dashboard',
        meta: { title: '首页', icon: 'el-icon-s-home' }
      },
      // 基础信息管理
      {
        path: '/basic',
        name: 'BasicManagement',
        meta: { title: '基础信息', icon: 'el-icon-s-grid' },
        children: [
          {
            path: '/departments',
            name: 'DepartmentList',
            meta: { title: '院系管理', icon: 'el-icon-office-building' }
          },
          {
            path: '/majors',
            name: 'MajorList',
            meta: { title: '专业管理', icon: 'el-icon-school' }
          },
          {
            path: '/classes',
            name: 'ClassList',
            meta: { title: '班级管理', icon: 'el-icon-user-solid' }
          }
        ]
      },
      // 人员管理
      {
        path: '/personnel',
        name: 'PersonnelManagement',
        meta: { title: '人员管理', icon: 'el-icon-user' },
        children: [
          {
            path: '/students',
            name: 'StudentList',
            meta: { title: '学生管理', icon: 'el-icon-user' }
          },
          {
            path: '/teachers',
            name: 'TeacherList',
            meta: { title: '教师管理', icon: 'el-icon-s-custom' }
          }
        ]
      },
      // 教学管理
      {
        path: '/academic',
        name: 'AcademicManagement',
        meta: { title: '教学管理', icon: 'el-icon-reading' },
        children: [
          {
            path: '/courses',
            name: 'CourseList',
            meta: { title: '课程管理', icon: 'el-icon-reading' }
          }
        ]
      },
      // 资源管理
      {
        path: '/resources',
        name: 'ResourceManagement',
        meta: { title: '资源管理', icon: 'el-icon-s-goods' },
        children: [
          {
            path: '/classrooms',
            name: 'ClassroomList',
            meta: { title: '教室管理', icon: 'el-icon-school' }
          },
          {
            path: '/dormitories',
            name: 'DormitoryList',
            meta: { title: '宿舍管理', icon: 'el-icon-house' }
          },
          {
            path: '/sports',
            name: 'SportsVenueList',
            meta: { title: '体育场馆', icon: 'el-icon-football' }
          }
        ]
      },
      // 图书管理
      {
        path: '/library',
        name: 'LibraryManagement',
        meta: { title: '图书管理', icon: 'el-icon-collection' },
        children: [
          {
            path: '/books',
            name: 'BookList',
            meta: { title: '图书信息', icon: 'el-icon-collection' }
          },
          {
            path: '/borrowing-records',
            name: 'BorrowingList',
            meta: { title: '借阅记录', icon: 'el-icon-notebook-1' }
          }
        ]
      },
      // 系统管理
      {
        path: '/system',
        name: 'SystemManagement',
        meta: { title: '系统管理', icon: 'el-icon-setting' },
        children: [
          {
            path: '/users',
            name: 'UserList',
            meta: { title: '用户管理', icon: 'el-icon-user', roles: ['admin'] }
          },
          {
            path: '/roles',
            name: 'RoleList',
            meta: { title: '角色管理', icon: 'el-icon-s-custom', roles: ['admin'] }
          }
        ]
      }
    ]

    return {
      activeMenu,
      routes
    }
  }
}
</script>

<style scoped>
.sidebar {
  height: 100%;
  background-color: #304156;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu {
  height: auto;
  min-height: 100%;
  border-right: none;
  width: 100%;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 210px;
}

/* 菜单整体样式优化 - 仅在非首页模式下应用 */
.sidebar:not(.dashboard-mode) .sidebar-menu :deep(.el-menu) {
  background-color: transparent;
  border-right: none;
}

.sidebar:not(.dashboard-mode) .sidebar-menu :deep(.el-menu-item),
.sidebar:not(.dashboard-mode) .sidebar-menu :deep(.el-submenu) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar:not(.dashboard-mode) .sidebar-menu :deep(.el-menu-item:last-child),
.sidebar:not(.dashboard-mode) .sidebar-menu :deep(.el-submenu:last-child) {
  border-bottom: none;
}

/* 折叠状态下的样式 */
.sidebar-menu.el-menu--collapse :deep(.el-menu-item),
.sidebar-menu.el-menu--collapse :deep(.el-submenu__title) {
  padding: 0 !important;
  text-align: center;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item i),
.sidebar-menu.el-menu--collapse :deep(.el-submenu__title i) {
  margin: 0;
  width: auto;
}

.sidebar-menu.el-menu--collapse :deep(.el-menu-item span),
.sidebar-menu.el-menu--collapse :deep(.el-submenu__title span) {
  display: none;
}

/* 自定义滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #2b3649;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #5a6578;
}
</style>