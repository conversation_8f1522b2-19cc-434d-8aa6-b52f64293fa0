package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.Course;
import com.university.management.model.entity.Department;
import com.university.management.repository.CourseRepository;
import com.university.management.repository.DepartmentRepository;
import com.university.management.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class CourseServiceImpl implements CourseService {

    private final CourseRepository courseRepository;
    private final DepartmentRepository departmentRepository;

    @Autowired
    public CourseServiceImpl(CourseRepository courseRepository, DepartmentRepository departmentRepository) {
        this.courseRepository = courseRepository;
        this.departmentRepository = departmentRepository;
    }

    @Override
    public Course createCourse(Course course) {
        return courseRepository.save(course);
    }

    @Override
    public Course updateCourse(Integer id, Course courseDetails) {
        Course course = courseRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Course not found with id: " + id));

        course.setName(courseDetails.getName());
        course.setCourseNo(courseDetails.getCourseNo());
        course.setCollegeId(courseDetails.getCollegeId());
        course.setCredit(courseDetails.getCredit());
        course.setDescription(courseDetails.getDescription());
        course.setType(courseDetails.getType());
        course.setHours(courseDetails.getHours());
        course.setStatus(courseDetails.getStatus());
        
        return courseRepository.save(course);
    }

    @Override
    public void deleteCourse(Integer id) {
        Course course = courseRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Course not found with id: " + id));
        courseRepository.delete(course);
    }

    @Override
    public Optional<Course> findById(Integer id) {
        return courseRepository.findById(id);
    }

    @Override
    public Optional<Course> findByCourseNo(String courseCode) {
        return courseRepository.findByCourseNo(courseCode);
    }

    @Override
    public List<Course> findAll() {
        return courseRepository.findAll();
    }

    @Override
    public Page<Course> findAll(Pageable pageable) {
        return courseRepository.findAll(pageable);
    }

    @Override
    public Page<Course> findByConditions(String name, String department, Integer credits, Pageable pageable) {
        // 这里根据实际情况修改，使用collegeId代替department，credit代替credits
        return courseRepository.findByConditions(name, department, credits, pageable);
    }

    @Override
    public List<Course> findByTeacherId(Integer teacherId) {
        // 根据实际情况调整，Course实体可能不再直接关联teacherId
        return courseRepository.findByCollegeId(teacherId);
    }

    @Override
    public boolean existsByCourseNo(String courseCode) {
        return courseRepository.existsByCourseNo(courseCode);
    }

    @Override
    public Map<String, Long> getCourseStatsByDepartment() {
        Map<String, Long> stats = new HashMap<>();
        List<Course> courses = courseRepository.findAll();

        // 使用部门名称进行分组，而不是collegeId
        // 注意：Course实体中没有department关联，需要通过departmentId查询
        stats = courses.stream()
                .filter(course -> course.getDepartmentId() != null) // 过滤掉没有部门信息的课程
                .collect(Collectors.groupingBy(
                    course -> {
                        // 通过departmentId查询部门名称
                        return departmentRepository.findById(course.getDepartmentId())
                                .map(Department::getName)
                                .orElse("未知部门");
                    },
                    Collectors.counting()
                ));

        return stats;
    }

    @Override
    public Map<Integer, Long> getCourseStatsByCredits() {
        Map<Integer, Long> stats = new HashMap<>();
        List<Course> courses = courseRepository.findAll();
        
        // 修改为使用credit进行分组
        stats = courses.stream()
                .collect(Collectors.groupingBy(course -> course.getCredit().intValue(), Collectors.counting()));

        return stats;
    }

    @Override
    public Map<String, Long> getCourseStatsBySemester() {
        Map<String, Long> stats = new HashMap<>();
        List<Course> courses = courseRepository.findAll();

        // 使用semester进行分组统计
        stats = courses.stream()
                .collect(Collectors.groupingBy(course -> course.getSemester() != null ? course.getSemester() : "未知", Collectors.counting()));

        return stats;
    }
}