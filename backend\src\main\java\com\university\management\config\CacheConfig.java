package com.university.management.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 缓存配置类
 * 使用内存缓存替代Redis缓存
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * 配置内存缓存管理器
     * 
     * @return 缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
            "studentCache", 
            "teacherCache", 
            "courseCache", 
            "statsCache"
        );
    }
} 