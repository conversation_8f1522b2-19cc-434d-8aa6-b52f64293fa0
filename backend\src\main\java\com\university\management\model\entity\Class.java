package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 班级实体类
 */
@ApiModel(value = "Class", description = "班级信息")
@Entity
@TableName("class")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Class extends BaseEntity {

    /**
     * 班级编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("班级编号")
    private String classNo;

    /**
     * 班级名称
     */
    @Column(nullable = false, length = 50)
    @ApiModelProperty("班级名称")
    private String name;

    /**
     * 专业ID
     */
    @Column
    @ApiModelProperty("专业ID")
    private Integer majorId;

    /**
     * 院系ID
     */
    @Column
    @ApiModelProperty("院系ID")
    private Integer departmentId;

    /**
     * 年级
     */
    @Column
    @ApiModelProperty("年级")
    private Integer grade;

    /**
     * 班主任ID
     */
    @Column
    @ApiModelProperty("班主任ID")
    private Integer teacherId;

    /**
     * 班级人数
     */
    @Column
    @ApiModelProperty("班级人数")
    private Integer classSize;

    /**
     * 专业 - 通过majorId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "majorId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Major major;

    /**
     * 院系 - 通过departmentId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "departmentId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Department department;

    /**
     * 班主任 - 通过teacherId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacherId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Teacher teacher;

    /**
     * 学生列表
     */
    @OneToMany(mappedBy = "clazz", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Student> students = new ArrayList<>();

    // Getter和Setter方法
    public String getClassNo() {
        return classNo;
    }

    public void setClassNo(String classNo) {
        this.classNo = classNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getMajorId() {
        return majorId;
    }

    public void setMajorId(Integer majorId) {
        this.majorId = majorId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getClassSize() {
        return classSize;
    }

    public void setClassSize(Integer classSize) {
        this.classSize = classSize;
    }

    public Integer getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(Integer teacherId) {
        this.teacherId = teacherId;
    }

    public Major getMajor() {
        return major;
    }

    public void setMajor(Major major) {
        this.major = major;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    public Teacher getTeacher() {
        return teacher;
    }

    public void setTeacher(Teacher teacher) {
        this.teacher = teacher;
    }

    public List<Student> getStudents() {
        return students;
    }

    public void setStudents(List<Student> students) {
        this.students = students;
    }
}