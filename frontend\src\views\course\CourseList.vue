<template>
  <div class="course-list-container">
    <div class="header">
      <h2>课程管理</h2>
      <el-button type="primary" @click="handleAdd">添加课程</el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="课程名称">
          <el-input v-model="searchForm.name" placeholder="请输入课程名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="开课部门">
          <el-input v-model="searchForm.department" placeholder="请输入开课部门" clearable></el-input>
        </el-form-item>
        <el-form-item label="学期">
          <el-select v-model="searchForm.semester" placeholder="请选择学期" clearable>
            <el-option label="春季学期" value="春季学期"></el-option>
            <el-option label="秋季学期" value="秋季学期"></el-option>
            <el-option label="夏季学期" value="夏季学期"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table 
      v-loading="loading" 
      :data="courseList" 
      border 
      style="width: 100%" 
      row-key="id"
      @sort-change="handleSortChange">
      <el-table-column prop="courseCode" label="课程代码" width="120" sortable></el-table-column>
      <el-table-column prop="name" label="课程名称" width="150"></el-table-column>
      <el-table-column prop="department" label="开课部门" width="150"></el-table-column>
      <el-table-column prop="credit" label="学分" width="80" sortable></el-table-column>
      <el-table-column prop="hours" label="学时" width="80" sortable></el-table-column>
      <el-table-column prop="semester" label="学期" width="100"></el-table-column>
      <el-table-column prop="courseType" label="课程类型" width="100">
        <template #default="scope">
          <el-tag 
            :type="getTagType(scope.row.courseType)" 
            size="small">
            {{ scope.row.courseType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="teacherName" label="授课教师" width="120"></el-table-column>
      <el-table-column prop="maxStudents" label="最大人数" width="100" sortable></el-table-column>
      <el-table-column prop="currentStudents" label="当前人数" width="100">
        <template #default="scope">
          <el-progress 
            :percentage="calculateProgress(scope.row.currentStudents, scope.row.maxStudents)" 
            :format="percentageFormat"
            :status="getProgressStatus(scope.row.currentStudents, scope.row.maxStudents)">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 数据可视化 -->
    <div class="data-visualization">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="chart-container">
            <h3>课程学分分布</h3>
            <div id="creditChart" style="height: 300px;"></div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="chart-container">
            <h3>课程学期分布</h3>
            <div id="semesterChart" style="height: 300px;"></div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="chart-container">
            <h3>开课部门分布</h3>
            <div id="departmentChart" style="height: 300px;"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { getCoursesByPage, searchCourses, deleteCourse, getCourseStatsByCredit, getCourseStatsBySemester, getCourseStatsByDepartment } from '@/api/course'

export default {
  name: 'CourseList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const courseList = ref([])
    const creditChart = ref(null)
    const semesterChart = ref(null)
    const departmentChart = ref(null)

    // 分页设置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortBy: 'id'
    })

    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      semester: ''
    })

    // 获取课程列表数据
    const fetchCourseData = async () => {
      loading.value = true
      try {
        console.log('📡 发送真实API请求: GET /api/courses/page')
        const response = await getCoursesByPage(
          pagination.currentPage - 1,
          pagination.pageSize,
          pagination.sortBy
        )

        if (response.data) {
          courseList.value = response.data.content || response.data
          pagination.total = response.data.totalElements || response.data.length || 0
          console.log('✅ 成功获取课程数据，共', pagination.total, '条记录')
        }
      } catch (error) {
        console.error('❌ 课程API请求失败，使用模拟数据:', error)
        // API失败时使用模拟数据

        // 真实的课程数据
        const mockCourses = [
          // 计算机科学与技术学院课程
          {
            id: 1,
            courseCode: 'CS101',
            name: '计算机科学导论',
            department: '计算机科学与技术学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '张教授',
            maxStudents: 120,
            currentStudents: 115,
            description: '计算机科学基础理论与实践入门课程'
          },
          {
            id: 2,
            courseCode: 'CS201',
            name: '数据结构与算法',
            department: '计算机科学与技术学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '李副教授',
            maxStudents: 100,
            currentStudents: 98,
            description: '数据结构设计与算法分析'
          },
          {
            id: 3,
            courseCode: 'CS301',
            name: '人工智能基础',
            department: '计算机科学与技术学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '王讲师',
            maxStudents: 80,
            currentStudents: 75,
            description: '人工智能理论与应用基础'
          },
          {
            id: 4,
            courseCode: 'CS401',
            name: '机器学习',
            department: '计算机科学与技术学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '陈博士',
            maxStudents: 60,
            currentStudents: 58,
            description: '机器学习算法与实践'
          },
          {
            id: 5,
            courseCode: 'CS501',
            name: '网络安全',
            department: '计算机科学与技术学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '许博士',
            maxStudents: 50,
            currentStudents: 45,
            description: '网络安全理论与实践'
          },

          // 数学与统计学院课程
          {
            id: 6,
            courseCode: 'MATH101',
            name: '高等数学A',
            department: '数学与统计学院',
            credit: 5,
            hours: 80,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '刘教授',
            maxStudents: 200,
            currentStudents: 195,
            description: '微积分与数学分析基础'
          },
          {
            id: 7,
            courseCode: 'MATH201',
            name: '线性代数',
            department: '数学与统计学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '杨副教授',
            maxStudents: 150,
            currentStudents: 148,
            description: '线性代数理论与应用'
          },
          {
            id: 8,
            courseCode: 'STAT301',
            name: '概率论与数理统计',
            department: '数学与统计学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '杨副教授',
            maxStudents: 120,
            currentStudents: 110,
            description: '概率论基础与统计方法'
          },

          // 物理学院课程
          {
            id: 9,
            courseCode: 'PHY101',
            name: '大学物理A',
            department: '物理学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '赵教授',
            maxStudents: 180,
            currentStudents: 175,
            description: '力学、热学、电磁学基础'
          },
          {
            id: 10,
            courseCode: 'PHY201',
            name: '量子物理',
            department: '物理学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '孙讲师',
            maxStudents: 60,
            currentStudents: 55,
            description: '量子力学基础理论'
          },

          // 化学学院课程
          {
            id: 11,
            courseCode: 'CHEM101',
            name: '无机化学',
            department: '化学学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '周副教授',
            maxStudents: 100,
            currentStudents: 95,
            description: '无机化学基础理论与实验'
          },
          {
            id: 12,
            courseCode: 'CHEM201',
            name: '有机化学',
            department: '化学学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '吴助教',
            maxStudents: 80,
            currentStudents: 78,
            description: '有机化学反应与合成'
          },

          // 生物学院课程
          {
            id: 13,
            courseCode: 'BIO101',
            name: '普通生物学',
            department: '生物学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '郑教授',
            maxStudents: 120,
            currentStudents: 118,
            description: '生物学基础理论与实验'
          },
          {
            id: 14,
            courseCode: 'BIO301',
            name: '分子生物学',
            department: '生物学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '冯讲师',
            maxStudents: 60,
            currentStudents: 58,
            description: '分子生物学理论与技术'
          },

          // 工程学院课程
          {
            id: 15,
            courseCode: 'ENG101',
            name: '工程制图',
            department: '工程学院',
            credit: 3,
            hours: 48,
            semester: '2024春',
            courseType: '必修课',
            teacherName: '韩副教授',
            maxStudents: 100,
            currentStudents: 95,
            description: '工程制图基础与CAD应用'
          },
          {
            id: 16,
            courseCode: 'ENG201',
            name: '自动控制原理',
            department: '工程学院',
            credit: 4,
            hours: 64,
            semester: '2024春',
            courseType: '选修课',
            teacherName: '曹助教',
            maxStudents: 80,
            currentStudents: 75,
            description: '自动控制系统分析与设计'
          }
        ]

        // 模拟分页数据
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize
        const endIndex = startIndex + pagination.pageSize
        courseList.value = mockCourses.slice(startIndex, endIndex)
        pagination.total = mockCourses.length

        console.log('🔄 使用模拟课程数据，共', mockCourses.length, '条记录，当前显示第', pagination.currentPage, '页')
        ElMessage.warning('课程API不可用，使用模拟数据')
      } finally {
        loading.value = false
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchCourseData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      pagination.currentPage = 1
      fetchCourseData()
    }

    // 处理排序变化
    const handleSortChange = (column) => {
      if (column.prop && column.order) {
        pagination.sortBy = column.prop + (column.order === 'ascending' ? ',asc' : ',desc')
      } else {
        pagination.sortBy = 'id'
      }
      fetchCourseData()
    }

    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      fetchCourseData()
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchCourseData()
    }

    // 添加课程
    const handleAdd = () => {
      router.push('/courses/add')
    }

    // 编辑课程
    const handleEdit = (row) => {
      router.push(`/courses/edit/${row.id}`)
    }

    // 删除课程
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除课程 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteCourse(row.id)
          ElMessage.success('删除成功')
          fetchCourseData()
        } catch (error) {
          console.error('删除课程失败:', error)
          ElMessage.error('删除课程失败')
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    }

    // 获取Tag类型
    const getTagType = (courseType) => {
      const typeMap = {
        '必修': 'danger',
        '选修': 'success',
        '公共课': 'info',
        '专业课': 'warning',
        '实验课': 'primary'
      }
      return typeMap[courseType] || ''
    }

    // 计算进度条百分比
    const calculateProgress = (current, max) => {
      if (!max) return 0
      return Math.round((current / max) * 100)
    }

    // 格式化进度条文字
    const percentageFormat = (percentage) => {
      return percentage === 100 ? '已满' : `${percentage}%`
    }

    // 获取进度条状态
    const getProgressStatus = (current, max) => {
      if (!max) return 'info'
      const percentage = (current / max) * 100
      if (percentage >= 90) return 'success'
      if (percentage >= 70) return 'warning'
      return 'info'
    }

    // 初始化图表
    const initCharts = async () => {
      try {
        // 获取学分分布数据
        const creditRes = await getCourseStatsByCredit()
        if (creditRes) {
          const creditData = creditRes
          const creditChartInstance = echarts.init(document.getElementById('creditChart'))
          creditChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: Object.keys(creditData)
            },
            series: [
              {
                name: '学分分布',
                type: 'pie',
                radius: '70%',
                data: Object.entries(creditData).map(([name, value]) => ({ name, value })),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
          creditChart.value = creditChartInstance
        }

        // 获取学期分布数据
        const semesterRes = await getCourseStatsBySemester()
        if (semesterRes) {
          const semesterData = semesterRes
          const semesterChartInstance = echarts.init(document.getElementById('semesterChart'))
          semesterChartInstance.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            xAxis: {
              type: 'category',
              data: Object.keys(semesterData)
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '课程数量',
                type: 'bar',
                data: Object.values(semesterData),
                itemStyle: {
                  color: '#91cc75'
                }
              }
            ]
          })
          semesterChart.value = semesterChartInstance
        }

        // 获取部门分布数据
        const departmentRes = await getCourseStatsByDepartment()
        if (departmentRes) {
          const departmentData = departmentRes
          const departmentChartInstance = echarts.init(document.getElementById('departmentChart'))
          departmentChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 20,
              bottom: 20,
              data: Object.keys(departmentData)
            },
            series: [
              {
                name: '部门分布',
                type: 'pie',
                radius: '60%',
                center: ['40%', '50%'],
                data: Object.entries(departmentData).map(([name, value]) => ({ name, value })),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  formatter: '{b}: {c} ({d}%)'
                }
              }
            ]
          })
          departmentChart.value = departmentChartInstance
        }
      } catch (error) {
        console.error('加载图表数据失败:', error)
        ElMessage.error('加载图表数据失败')
      }
    }

    // 监听窗口大小变化，调整图表大小
    const resizeCharts = () => {
      if (creditChart.value) {
        creditChart.value.resize()
      }
      if (semesterChart.value) {
        semesterChart.value.resize()
      }
      if (departmentChart.value) {
        departmentChart.value.resize()
      }
    }

    // 组件挂载后初始化
    onMounted(() => {
      fetchCourseData()
      initCharts()
      window.addEventListener('resize', resizeCharts)
    })

    // 组件卸载前清理事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', resizeCharts)
      if (creditChart.value) {
        creditChart.value.dispose()
      }
      if (semesterChart.value) {
        semesterChart.value.dispose()
      }
      if (departmentChart.value) {
        departmentChart.value.dispose()
      }
    })

    return {
      loading,
      courseList,
      pagination,
      searchForm,
      fetchCourseData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleEdit,
      handleDelete,
      getTagType,
      calculateProgress,
      percentageFormat,
      getProgressStatus
    }
  }
}
</script>

<style scoped>
.course-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.data-visualization {
  margin-top: 30px;
}

.chart-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}
</style> 