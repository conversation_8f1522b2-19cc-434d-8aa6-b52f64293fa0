# 🇨🇳 数据库英文数据中文化修复指南

## 🎯 问题描述
数据库中存在大量英文数据，包括：
- 班级名称：Computer Science 2020 Class 1
- 专业名称：Computer Science and Technology
- 课程名称：Programming Fundamentals
- 教室名称：Computer Lab 1
- 建筑名称：Teaching Building A
- 设备名称：Computers, Projector

## 🚀 快速修复方案

### 一键修复（推荐）
```bash
# 进入数据库目录
cd backend/src/main/resources/db

# 执行完整修复脚本
mysql -u root -p university_management < fix-all-english-data.sql
```

### 分步修复
```bash
# 1. 基础中文化更新
mysql -u root -p university_management < chinese-data-update.sql

# 2. 完整中文化更新
mysql -u root -p university_management < complete-chinese-update.sql

# 3. 修复所有英文数据
mysql -u root -p university_management < fix-all-english-data.sql
```

## 📋 修复内容对照表

### 班级名称修复
| 修复前 | 修复后 |
|--------|--------|
| Computer Science 2020 Class 1 | 计算机科学与技术2020级1班 |
| Software Engineering 2021 Class 1 | 软件工程2021级1班 |
| Network Engineering 2020 Class 1 | 网络工程2020级1班 |
| Electronic Information Engineering 2021 Class 1 | 电子信息工程2021级1班 |
| Communication Engineering 2021 Class 1 | 通信工程2021级1班 |
| Mechanical Design Manufacturing 2022 Class 1 | 机械设计制造及其自动化2022级1班 |
| Business Administration 2021 Class 1 | 工商管理2021级1班 |
| English 2021 Class 1 | 英语2021级1班 |

### 专业名称修复
| 修复前 | 修复后 |
|--------|--------|
| Computer Science and Technology | 计算机科学与技术 |
| Software Engineering | 软件工程 |
| Network Engineering | 网络工程 |
| Artificial Intelligence | 人工智能 |
| Data Science and Big Data Technology | 数据科学与大数据技术 |
| Electronic Information Engineering | 电子信息工程 |
| Communication Engineering | 通信工程 |
| Mechanical Design Manufacturing and Automation | 机械设计制造及其自动化 |
| Business Administration | 工商管理 |
| Visual Communication Design | 视觉传达设计 |

### 课程名称修复
| 修复前 | 修复后 |
|--------|--------|
| Programming Fundamentals | 程序设计基础 |
| Data Structures | 数据结构 |
| Algorithm Analysis | 算法分析 |
| Computer Networks | 计算机网络 |
| Database Systems | 数据库系统 |
| Operating Systems | 操作系统 |
| Software Engineering | 软件工程 |
| Artificial Intelligence | 人工智能 |
| Machine Learning | 机器学习 |
| Management Principles | 管理学原理 |

### 教室名称修复
| 修复前 | 修复后 |
|--------|--------|
| Computer Lab 1 | 计算机实验室1 |
| Computer Lab 2 | 计算机实验室2 |
| Lecture Hall A103 | 阶梯教室A103 |
| AI Research Lab | 人工智能研究实验室 |
| Software Engineering Lab | 软件开发实验室 |
| Electronics Lab 1 | 电子实验室1 |
| Communication Lab | 通信实验室 |
| Mechanical Drawing Room | 机械制图室 |
| Business Simulation Lab | 商务模拟实验室 |
| Language Lab 1 | 语言实验室1 |

### 建筑名称修复
| 修复前 | 修复后 |
|--------|--------|
| Teaching Building A | 第一教学楼 |
| Teaching Building B | 第二教学楼 |
| Teaching Building C | 第三教学楼 |
| Laboratory Building | 实验楼 |
| Engineering Building | 工程楼 |
| Science Building | 理科楼 |
| Liberal Arts Building | 文科楼 |
| Art Building | 艺术楼 |

### 设备名称修复
| 修复前 | 修复后 |
|--------|--------|
| Computers, Projector, Air Conditioning | 计算机, 投影仪, 空调 |
| High-performance Computers, GPU Cluster | 高性能计算机, GPU集群 |
| Oscilloscopes, Signal Generators, Multimeters | 示波器, 信号发生器, 万用表 |
| Communication Equipment, Spectrum Analyzers | 通信设备, 频谱分析仪 |
| CNC Machines, 3D Printers, Tools | 数控机床, 3D打印机, 工具 |

### 学位类型修复
| 修复前 | 修复后 |
|--------|--------|
| B.Eng / Bachelor of Engineering | 工学学士 |
| B.Sc / Bachelor of Science | 理学学士 |
| B.A / Bachelor of Arts | 文学学士 |
| B.M / Bachelor of Management | 管理学学士 |
| B.F.A / Bachelor of Fine Arts | 艺术学学士 |

## 🔍 验证修复结果

### 检查班级数据
```sql
SELECT name, 
       CASE WHEN name LIKE '%Class%' OR name LIKE '%Engineering%' OR name LIKE '%Science%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM class 
ORDER BY 语言类型, name;
```

### 检查专业数据
```sql
SELECT name, degree,
       CASE WHEN name LIKE '%Engineering%' OR name LIKE '%Science%' OR name LIKE '%Administration%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM major 
ORDER BY 语言类型, name;
```

### 检查课程数据
```sql
SELECT name,
       CASE WHEN name LIKE '%Programming%' OR name LIKE '%Data%' OR name LIKE '%System%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM course 
ORDER BY 语言类型, name;
```

### 检查教室数据
```sql
SELECT name, building,
       CASE WHEN name LIKE '%Lab%' OR name LIKE '%Hall%' OR building LIKE '%Building%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM classroom 
ORDER BY 语言类型, name;
```

## 📊 统计查询
```sql
-- 统计各类数据的中文化比例
SELECT 
    '班级' AS 数据类型,
    COUNT(*) AS 总数,
    SUM(CASE WHEN name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' THEN 1 ELSE 0 END) AS 中文数量,
    ROUND(SUM(CASE WHEN name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS 中文化比例
FROM class
UNION ALL
SELECT 
    '专业',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM major
UNION ALL
SELECT 
    '课程',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM course
UNION ALL
SELECT 
    '教室',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Lab%' AND building NOT LIKE '%Building%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Lab%' AND building NOT LIKE '%Building%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM classroom;
```

## ⚠️ 注意事项

1. **备份数据**：执行前请备份数据库
   ```bash
   mysqldump -u root -p university_management > backup_before_chinese_fix.sql
   ```

2. **检查外键约束**：脚本会临时禁用外键检查

3. **分批执行**：如果数据量很大，可以分批执行

4. **验证结果**：更新完成后使用验证查询检查结果

## 🎉 预期结果

修复完成后，您应该看到：
- ✅ 所有班级名称为中文（如：计算机科学与技术2020级1班）
- ✅ 所有专业名称为中文（如：计算机科学与技术）
- ✅ 所有课程名称为中文（如：程序设计基础）
- ✅ 所有教室名称为中文（如：计算机实验室1）
- ✅ 所有建筑名称为中文（如：第一教学楼）
- ✅ 所有设备名称为中文（如：计算机、投影仪）
- ✅ 所有学位类型为中文（如：工学学士）

## 🔧 故障排除

### 问题1：部分数据未更新
```sql
-- 查找剩余的英文数据
SELECT 'class' AS table_name, name FROM class WHERE name LIKE '%Class%' OR name LIKE '%Engineering%'
UNION ALL
SELECT 'major', name FROM major WHERE name LIKE '%Engineering%' OR name LIKE '%Science%'
UNION ALL
SELECT 'course', name FROM course WHERE name LIKE '%Programming%' OR name LIKE '%Data%'
UNION ALL
SELECT 'classroom', name FROM classroom WHERE name LIKE '%Lab%' OR building LIKE '%Building%';
```

### 问题2：外键约束错误
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行更新操作
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
```

---

**💡 提示**：建议在非生产环境先测试修复脚本，确认无误后再在生产环境执行。
