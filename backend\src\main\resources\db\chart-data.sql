-- 图表数据初始化脚本
USE university_management;

-- 清理现有数据（可选）
-- DELETE FROM student WHERE id > 0;
-- DELETE FROM teacher WHERE id > 0;
-- DELETE FROM course WHERE id > 0;

-- 插入学生数据用于图表显示
INSERT IGNORE INTO student (student_no, name, gender, birthday, age, phone, email, id_card, major_id, class_id, department_id, enroll_year, status)
VALUES
-- 计算机科学与技术专业学生
('S2020001', '张小明', 1, '2002-03-15', 22, '13800001001', '<EMAIL>', '330102200203150001', 1, 1, 1, 2020, 0),
('S2020002', '李小红', 0, '2002-05-20', 22, '13800001002', '<EMAIL>', '330102200205200002', 1, 1, 1, 2020, 0),
('S2020003', '王小刚', 1, '2002-07-10', 22, '13800001003', '<EMAIL>', '330102200207100003', 1, 1, 1, 2020, 0),
('S2020004', '赵小丽', 0, '2002-09-25', 22, '13800001004', '<EMAIL>', '330102200209250004', 1, 1, 1, 2020, 0),
('S2020005', '刘小强', 1, '2002-11-30', 22, '13800001005', '<EMAIL>', '330102200211300005', 1, 1, 1, 2020, 0),

-- 软件工程专业学生
('S2020006', '陈小华', 1, '2002-01-12', 22, '13800001006', '<EMAIL>', '330102200201120006', 2, 2, 1, 2020, 0),
('S2020007', '周小芳', 0, '2002-04-18', 22, '13800001007', '<EMAIL>', '330102200204180007', 2, 2, 1, 2020, 0),
('S2020008', '吴小军', 1, '2002-06-22', 22, '13800001008', '<EMAIL>', '330102200206220008', 2, 2, 1, 2020, 0),
('S2020009', '郑小燕', 0, '2002-08-14', 22, '13800001009', '<EMAIL>', '330102200208140009', 2, 2, 1, 2020, 0),

-- 网络工程专业学生
('S2020010', '孙小龙', 1, '2002-02-28', 22, '13800001010', '<EMAIL>', '330102200202280010', 3, 3, 1, 2020, 0),
('S2020011', '马小梅', 0, '2002-10-05', 22, '13800001011', '<EMAIL>', '330102200210050011', 3, 3, 1, 2020, 0),

-- 2021级学生
('S2021001', '林小伟', 1, '2003-01-15', 21, '13800002001', '<EMAIL>', '330102200301150021', 1, 4, 1, 2021, 0),
('S2021002', '何小静', 0, '2003-03-20', 21, '13800002002', '<EMAIL>', '330102200303200022', 1, 4, 1, 2021, 0),
('S2021003', '高小峰', 1, '2003-05-10', 21, '13800002003', '<EMAIL>', '330102200305100023', 2, 5, 1, 2021, 0),
('S2021004', '许小娟', 0, '2003-07-25', 21, '13800002004', '<EMAIL>', '330102200307250024', 2, 5, 1, 2021, 0),

-- 电子信息工程专业学生
('S2021005', '田小勇', 1, '2003-02-14', 21, '13800002005', '<EMAIL>', '330102200302140025', 4, 6, 2, 2021, 0),
('S2021006', '邓小玲', 0, '2003-04-18', 21, '13800002006', '<EMAIL>', '330102200304180026', 4, 6, 2, 2021, 0),

-- 通信工程专业学生
('S2021007', '韩小斌', 1, '2003-06-22', 21, '13800002007', '<EMAIL>', '330102200306220027', 5, 7, 2, 2021, 0),
('S2021008', '曹小敏', 0, '2003-08-30', 21, '13800002008', '<EMAIL>', '330102200308300028', 5, 7, 2, 2021, 0),

-- 2022级学生
('S2022001', '蒋小涛', 1, '2004-01-10', 20, '13800003001', '<EMAIL>', '330102200401100031', 6, 8, 3, 2022, 0),
('S2022002', '薛小慧', 0, '2004-03-15', 20, '13800003002', '<EMAIL>', '330102200403150032', 6, 8, 3, 2022, 0),
('S2022003', '雷小鹏', 1, '2004-05-20', 20, '13800003003', '<EMAIL>', '330102200405200033', 7, 9, 3, 2022, 0),
('S2022004', '方小兰', 0, '2004-07-25', 20, '13800003004', '<EMAIL>', '330102200407250034', 7, 9, 3, 2022, 0),
('S2022005', '石小军', 1, '2004-09-30', 20, '13800003005', '<EMAIL>', '330102200409300035', 8, 10, 4, 2022, 0),
('S2022006', '袁小丽', 0, '2004-11-12', 20, '13800003006', '<EMAIL>', '330102200411120036', 8, 10, 4, 2022, 0);

-- 插入更多教师数据
INSERT IGNORE INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, id_card, college_id, department_id, hire_date, status)
VALUES
('T006', '陈教授', 1, '1970-01-15', 54, 3, '13900006666', '<EMAIL>', '330102197001150066', 1, 1, '1995-07-01', 0),
('T007', '周副教授', 0, '1975-03-20', 49, 2, '13900007777', '<EMAIL>', '330102197503200077', 1, 1, '2000-07-01', 0),
('T008', '吴讲师', 1, '1980-05-10', 44, 1, '13900008888', '<EMAIL>', '330102198005100088', 1, 2, '2005-07-01', 0),
('T009', '郑助教', 0, '1985-07-25', 39, 0, '13900009999', '<EMAIL>', '330102198507250099', 1, 2, '2010-07-01', 0),
('T010', '孙教授', 1, '1968-09-30', 56, 3, '13900010000', '<EMAIL>', '330102196809300100', 1, 3, '1992-07-01', 0),
('T011', '马副教授', 0, '1973-11-12', 51, 2, '13900011111', '<EMAIL>', '330102197311120111', 1, 3, '1998-07-01', 0),
('T012', '林讲师', 1, '1978-02-14', 46, 1, '13900012222', '<EMAIL>', '330102197802140122', 1, 4, '2003-07-01', 0),
('T013', '何助教', 0, '1983-04-18', 41, 0, '13900013333', '<EMAIL>', '330102198304180133', 1, 4, '2008-07-01', 0),
('T014', '高教授', 1, '1965-06-22', 59, 3, '13900014444', '<EMAIL>', '330102196506220144', 1, 5, '1990-07-01', 0),
('T015', '许副教授', 0, '1972-08-30', 52, 2, '13900015555', '<EMAIL>', '330102197208300155', 1, 5, '1997-07-01', 0);

-- 插入课程数据
INSERT IGNORE INTO course (course_no, name, credits, hours, course_type, department_id, teacher_id, semester, description, status)
VALUES
('C001', '高等数学', 4, 64, 1, 1, 1, '2024-1', '高等数学基础课程', 0),
('C002', '线性代数', 3, 48, 1, 1, 2, '2024-1', '线性代数基础课程', 0),
('C003', '概率论与数理统计', 3, 48, 1, 1, 3, '2024-2', '概率论与数理统计课程', 0),
('C004', '数据结构', 4, 64, 2, 1, 4, '2024-1', '数据结构与算法', 0),
('C005', '计算机网络', 3, 48, 2, 1, 5, '2024-2', '计算机网络原理', 0),
('C006', '操作系统', 4, 64, 2, 1, 6, '2024-1', '操作系统原理与实践', 0),
('C007', '数据库系统', 3, 48, 2, 1, 7, '2024-2', '数据库系统原理', 0),
('C008', '软件工程', 3, 48, 2, 1, 8, '2024-1', '软件工程方法与实践', 0),
('C009', '人工智能', 3, 48, 3, 1, 9, '2024-2', '人工智能基础', 0),
('C010', '机器学习', 3, 48, 3, 1, 10, '2024-2', '机器学习算法与应用', 0),
('C011', '电路分析', 4, 64, 2, 2, 11, '2024-1', '电路分析基础', 0),
('C012', '信号与系统', 3, 48, 2, 2, 12, '2024-2', '信号与系统分析', 0),
('C013', '数字信号处理', 3, 48, 2, 2, 13, '2024-1', '数字信号处理技术', 0),
('C014', '通信原理', 4, 64, 2, 2, 14, '2024-2', '通信系统原理', 0),
('C015', '机械制图', 3, 48, 2, 3, 15, '2024-1', '机械制图基础', 0);
