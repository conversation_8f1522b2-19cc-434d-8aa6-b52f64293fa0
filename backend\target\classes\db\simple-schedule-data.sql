-- 插入简化的课程表数据
USE university_management;

-- 插入课程表数据（使用正确的course_id, teacher_id, classroom_id）
INSERT INTO schedule (course_id, teacher_id, classroom_id, semester, day_of_week, start_time, end_time, start_week, end_week, create_time, update_time, is_deleted) VALUES
-- 2024 Spring Semester Course Schedule
-- Computer Science Courses
(1131, 889, 665, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Programming Fundamentals
(1132, 890, 666, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Data Structures
(1133, 891, 667, '2024-Spring', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Algorithm Analysis
(1134, 892, 668, '2024-Spring', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Computer Networks
(1135, 893, 669, '2024-Spring', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Database Systems

-- Electronic Engineering Courses
(1143, 897, 670, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Circuit Analysis
(1144, 898, 671, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Analog Electronics
(1145, 899, 672, '2024-Spring', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Digital Electronics

-- Mechanical Engineering Courses
(1150, 902, 673, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Mechanical Drawing
(1151, 903, 674, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Theoretical Mechanics

-- Economics and Management Courses
(1156, 905, 675, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Management Principles
(1157, 906, 676, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Microeconomics

-- Foreign Language Courses
(1163, 909, 677, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Comprehensive English
(1164, 910, 678, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- English Grammar

-- Art and Design Courses
(1170, 913, 679, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Design Fundamentals
(1171, 914, 680, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0); -- Color Composition
