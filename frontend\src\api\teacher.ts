import request from '@/utils/request'

const baseUrl = '/api/teachers'

// 教师相关类型定义
export interface Teacher {
  id: number
  teacherNo: string
  name: string
  gender: number
  birthday?: string
  age?: number
  phone?: string
  email?: string
  address?: string
  departmentId?: number
  title?: string
  subject?: string
  entryYear?: number
  status: number
  createdTime?: string
  updatedTime?: string
}

export interface TeacherQuery {
  name?: string
  department?: string
  title?: string
  page?: number
  size?: number
}

export interface TeacherPageResult {
  content: Teacher[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

export interface DepartmentStats {
  [key: string]: number
}

export interface TitleStats {
  [key: string]: number
}

// 获取所有教师信息
export function getAllTeachers() {
  return request<{ data: Teacher[] }>({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取教师信息
export function getTeachersByPage(page = 0, size = 10, sortBy = 'id') {
  return request<{ data: TeacherPageResult }>({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询教师信息
export function searchTeachers({ name, department, title, page = 0, size = 10 }: TeacherQuery) {
  return request<{ data: TeacherPageResult }>({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      department,
      title,
      page,
      size
    }
  })
}

// 根据ID获取教师信息
export function getTeacherById(id: number) {
  return request<{ data: Teacher }>({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建教师信息
export function createTeacher(data: Partial<Teacher>) {
  return request<{ data: Teacher }>({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新教师信息
export function updateTeacher(id: number, data: Partial<Teacher>) {
  return request<{ data: Teacher }>({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除教师信息
export function deleteTeacher(id: number) {
  return request<void>({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取教师部门分布统计
export function getTeacherStatsByDepartment() {
  return request<{ data: DepartmentStats }>({
    url: `${baseUrl}/stats/department`,
    method: 'get'
  })
}

// 获取教师职称分布统计
export function getTeacherStatsByTitle() {
  return request<{ data: TitleStats }>({
    url: `${baseUrl}/stats/title`,
    method: 'get'
  })
} 