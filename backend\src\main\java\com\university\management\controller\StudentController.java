package com.university.management.controller;

import com.university.management.model.entity.Student;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags = "学生管理")
@RestController
@RequestMapping("/api/students")
public class StudentController {

    private final StudentService studentService;

    @Autowired
    public StudentController(StudentService studentService) {
        this.studentService = studentService;
    }

    @ApiOperation("获取所有学生信息")
    @GetMapping
    public ApiResponse<List<Student>> getAllStudents() {
        return ApiResponse.success(studentService.findAll());
    }

    @ApiOperation("分页获取学生信息")
    @GetMapping("/page")
    public ApiResponse<Page<Student>> getStudentsByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortBy));
        return ApiResponse.success(studentService.findAll(pageRequest));
    }

    @ApiOperation("条件查询学生信息")
    @GetMapping("/search")
    public ApiResponse<Page<Student>> searchStudents(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String major,
            @RequestParam(required = false) Integer grade,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(studentService.findByConditions(name, major, grade, pageRequest));
    }

    @ApiOperation("根据ID获取学生信息")
    @GetMapping("/{id}")
    public ApiResponse<Student> getStudentById(@PathVariable Integer id) {
        return studentService.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.errorGeneric(404, "学生不存在"));
    }

    @ApiOperation("创建学生信息")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Student> createStudent(@Valid @RequestBody Student student) {
        return ApiResponse.success("创建成功", studentService.createStudent(student));
    }

    @ApiOperation("更新学生信息")
    @PutMapping("/{id}")
    public ApiResponse<Student> updateStudent(@PathVariable Integer id, @Valid @RequestBody Student studentDetails) {
        return ApiResponse.success("更新成功", studentService.updateStudent(id, studentDetails));
    }

    @ApiOperation("删除学生信息")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteStudent(@PathVariable Integer id) {
        studentService.deleteStudent(id);
        return ApiResponse.success();
    }

    @ApiOperation("获取学生专业分布统计")
    @GetMapping("/stats/major")
    public ApiResponse<Map<String, Long>> getStudentStatsByMajor() {
        return ApiResponse.success(studentService.getStudentStatsByMajor());
    }

    @ApiOperation("获取学生年级分布统计")
    @GetMapping("/stats/grade")
    public ApiResponse<Map<Integer, Long>> getStudentStatsByGrade() {
        return ApiResponse.success(studentService.getStudentStatsByGrade());
    }
} 