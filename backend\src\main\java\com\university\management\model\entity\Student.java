package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.time.LocalDate;

/**
 * 学生实体类
 */
@Entity
@Table(name = "student")
@TableName("student")
@ApiModel(value = "学生实体", description = "学生信息")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Student extends BaseEntity {

    /**
     * 学号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("学号")
    private String studentNo;

    /**
     * 姓名
     */
    @Column(nullable = false, length = 20)
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别（0-女，1-男）
     */
    @Column
    @ApiModelProperty("性别（0-女，1-男）")
    private Integer gender;

    /**
     * 出生日期
     */
    @Column
    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    /**
     * 年龄
     */
    @Column
    @ApiModelProperty("年龄")
    private Integer age;

    /**
     * 身份证号
     */
    @Column(length = 18)
    @ApiModelProperty("身份证号")
    private String idCard;

    /**
     * 手机号
     */
    @Column(length = 11)
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 邮箱
     */
    @Column(length = 50)
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 家庭住址
     */
    @Column(length = 200)
    @ApiModelProperty("家庭住址")
    private String address;

    /**
     * 班级ID
     */
    @Column
    @ApiModelProperty("班级ID")
    private Integer classId;

    /**
     * 专业ID
     */
    @Column(name = "major_id")
    @ApiModelProperty("专业ID")
    private Integer majorId;

    /**
     * 院系ID
     */
    @Column(name = "department_id")
    @ApiModelProperty("院系ID")
    private Integer departmentId;

    /**
     * 学院ID
     */
    @Column
    @ApiModelProperty("学院ID")
    private Integer collegeId;

    /**
     * 入学年份
     */
    @Column
    @ApiModelProperty("入学年份")
    private Integer enrollYear;

    /**
     * 状态（0-在读，1-毕业，2-休学，3-退学）
     */
    @Column
    @ApiModelProperty("状态（0-在读，1-毕业，2-休学，3-退学）")
    private Integer status;

    /**
     * 宿舍ID
     */
    @Column
    @ApiModelProperty("宿舍ID")
    private Integer dormitoryId;

    /**
     * 班级 - 通过classId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "classId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Class clazz;

    /**
     * 专业 - 通过major_id关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "major_id", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Major major;

    /**
     * 院系 - 通过department_id关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_id", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Department department;

    /**
     * 宿舍 - 通过dormitoryId关联，避免重复映射
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dormitoryId", insertable = false, updatable = false)
    @JsonIgnore
    @TableField(exist = false)
    private Dormitory dormitory;

    public String getStudentNo() {
        return studentNo;
    }

    public void setStudentNo(String studentNo) {
        this.studentNo = studentNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getClassId() {
        return classId;
    }

    public void setClassId(Integer classId) {
        this.classId = classId;
    }

    public Integer getMajorId() {
        return majorId;
    }

    public void setMajorId(Integer majorId) {
        this.majorId = majorId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Integer collegeId) {
        this.collegeId = collegeId;
    }

    public Integer getEnrollYear() {
        return enrollYear;
    }

    public void setEnrollYear(Integer enrollYear) {
        this.enrollYear = enrollYear;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getDormitoryId() {
        return dormitoryId;
    }

    public void setDormitoryId(Integer dormitoryId) {
        this.dormitoryId = dormitoryId;
    }

    public Class getClazz() {
        return clazz;
    }

    public void setClazz(Class clazz) {
        this.clazz = clazz;
    }

    public Major getMajor() {
        return major;
    }

    public void setMajor(Major major) {
        this.major = major;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    public Dormitory getDormitory() {
        return dormitory;
    }

    public void setDormitory(Dormitory dormitory) {
        this.dormitory = dormitory;
    }
} 