-- 插入学生选课数据
USE university_management;

-- 插入学生选课数据（使用正确的student_id和schedule_id）
INSERT INTO student_course (student_id, schedule_id, score, grade_point, evaluation, create_time, update_time, is_deleted) VALUES
-- Computer Science专业学生选课 (student_id: 965-974, schedule_id: 637-641)
-- 学生965 (<PERSON>) 选课
(965, 637, 85.5, 3.5, 'Good performance in programming fundamentals', NOW(), NOW(), 0),
(965, 638, 92.0, 4.0, 'Excellent understanding of data structures', NOW(), NOW(), 0),
(965, 639, 78.5, 3.0, 'Satisfactory algorithm analysis skills', NOW(), NOW(), 0),
(965, 640, 88.0, 3.7, 'Good grasp of network concepts', NOW(), NOW(), 0),

-- 学生966 (<PERSON>) 选课
(966, 637, 90.0, 3.8, 'Strong programming skills', NOW(), NOW(), 0),
(966, 638, 87.5, 3.6, 'Good data structure implementation', NOW(), NOW(), 0),
(966, 639, 82.0, 3.2, 'Adequate algorithm understanding', NOW(), NOW(), 0),
(966, 641, 91.5, 3.9, 'Excellent database design skills', NOW(), NOW(), 0),

-- 学生967 (Wang Xiaogang) 选课
(967, 637, 76.0, 2.8, 'Basic programming competency', NOW(), NOW(), 0),
(967, 638, 83.5, 3.3, 'Good progress in data structures', NOW(), NOW(), 0),
(967, 640, 85.0, 3.4, 'Solid network fundamentals', NOW(), NOW(), 0),
(967, 641, 89.0, 3.7, 'Strong database skills', NOW(), NOW(), 0),

-- 学生968 (Zhao Xiaoli) 选课
(968, 637, 94.0, 4.0, 'Outstanding programming abilities', NOW(), NOW(), 0),
(968, 638, 96.5, 4.0, 'Exceptional data structure mastery', NOW(), NOW(), 0),
(968, 639, 91.0, 3.9, 'Excellent algorithm analysis', NOW(), NOW(), 0),
(968, 640, 87.5, 3.6, 'Good network understanding', NOW(), NOW(), 0),

-- 学生969 (Liu Xiaoqiang) 选课
(969, 637, 81.0, 3.1, 'Satisfactory programming progress', NOW(), NOW(), 0),
(969, 638, 79.5, 3.0, 'Adequate data structure knowledge', NOW(), NOW(), 0),
(969, 641, 86.0, 3.5, 'Good database fundamentals', NOW(), NOW(), 0),

-- Software Engineering专业学生选课 (student_id: 970-974)
-- 学生970 (Chen Xiaohua) 选课
(970, 637, 88.5, 3.7, 'Strong foundation in programming', NOW(), NOW(), 0),
(970, 638, 85.0, 3.4, 'Good data structure skills', NOW(), NOW(), 0),
(970, 640, 90.0, 3.8, 'Excellent network knowledge', NOW(), NOW(), 0),

-- 学生971 (Zhou Xiaofang) 选课
(971, 637, 92.5, 4.0, 'Excellent programming skills', NOW(), NOW(), 0),
(971, 638, 89.0, 3.7, 'Strong data structure understanding', NOW(), NOW(), 0),
(971, 641, 93.0, 4.0, 'Outstanding database skills', NOW(), NOW(), 0),

-- Electronic Engineering专业学生选课 (student_id: 980-984, schedule_id: 642-644)
-- 学生980 (Chang Xiaopeng) 选课
(980, 642, 87.0, 3.6, 'Good circuit analysis skills', NOW(), NOW(), 0),
(980, 643, 84.5, 3.4, 'Solid analog electronics knowledge', NOW(), NOW(), 0),
(980, 644, 89.5, 3.8, 'Strong digital electronics understanding', NOW(), NOW(), 0),

-- 学生981 (Tang Xiaolan) 选课
(981, 642, 91.0, 3.9, 'Excellent circuit fundamentals', NOW(), NOW(), 0),
(981, 643, 88.0, 3.7, 'Good analog circuit design', NOW(), NOW(), 0),
(981, 644, 92.5, 4.0, 'Outstanding digital circuit skills', NOW(), NOW(), 0),

-- Mechanical Engineering专业学生选课 (student_id: 990-994, schedule_id: 645-646)
-- 学生990 (Hua Xiaotao) 选课
(990, 645, 85.5, 3.5, 'Good mechanical drawing skills', NOW(), NOW(), 0),
(990, 646, 82.0, 3.2, 'Solid theoretical mechanics foundation', NOW(), NOW(), 0),

-- 学生991 (Miao Xiaohui) 选课
(991, 645, 89.0, 3.7, 'Excellent drawing techniques', NOW(), NOW(), 0),
(991, 646, 87.5, 3.6, 'Strong mechanics understanding', NOW(), NOW(), 0),

-- Business Administration专业学生选课 (student_id: 995-999, schedule_id: 647-648)
-- 学生995 (Mu Xiaohua) 选课
(995, 647, 88.0, 3.7, 'Good management principles understanding', NOW(), NOW(), 0),
(995, 648, 85.5, 3.5, 'Solid microeconomics knowledge', NOW(), NOW(), 0),

-- English专业学生选课 (student_id: 1005-1009, schedule_id: 649-650)
-- 学生1005 (Dan Xiaolan) 选课
(1005, 649, 92.0, 4.0, 'Excellent English comprehension', NOW(), NOW(), 0),
(1005, 650, 89.5, 3.8, 'Strong grammar skills', NOW(), NOW(), 0);
