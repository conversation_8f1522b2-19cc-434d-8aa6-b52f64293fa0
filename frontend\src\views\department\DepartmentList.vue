<template>
  <div class="department-list-container">
    <div class="page-header">
      <h2>院系管理</h2>
      <el-button type="primary" @click="handleAddDepartment">新增院系</el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="院系编号">
          <el-input v-model="queryParams.departmentNo" placeholder="请输入院系编号" clearable />
        </el-form-item>
        <el-form-item label="院系名称">
          <el-input v-model="queryParams.name" placeholder="请输入院系名称" clearable />
        </el-form-item>
        <el-form-item label="院长">
          <el-input v-model="queryParams.dean" placeholder="请输入院长姓名" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" :value="0" />
            <el-option label="停用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-container">
      <el-table
        v-loading="loading"
        :data="departmentList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="departmentNo" label="院系编号" min-width="100" />
        <el-table-column prop="name" label="院系名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="dean" label="院长" min-width="100" />
        <el-table-column prop="telephone" label="联系电话" min-width="120" />
        <el-table-column prop="email" label="电子邮箱" min-width="150" show-overflow-tooltip />
        <el-table-column prop="establishedDate" label="成立时间" min-width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
              {{ scope.row.status === 0 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:page-size="queryParams.pageSize"
          v-model:current-page="queryParams.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入API
import { searchDepartments, deleteDepartment } from '@/api/department'

export default {
  name: 'DepartmentList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const total = ref(0)
    const departmentList = ref([])
    
    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      departmentNo: '',
      name: '',
      dean: '',
      status: undefined
    })
    
    // 获取院系列表
    const getList = async () => {
      loading.value = true
      try {
        const params = {
          name: queryParams.name || null,
          code: queryParams.departmentNo || null,
          page: queryParams.pageNum - 1,
          size: queryParams.pageSize
        }

        const res = await searchDepartments(params)
        if (res) {
          departmentList.value = res.content || []
          total.value = res.totalElements || 0
        }
      } catch (error) {
        console.error('获取院系数据失败:', error)

        // 使用模拟数据
        const mockDepartments = [
          {
            id: 1,
            name: '计算机科学与技术学院',
            code: 'CS',
            dean: '张教授',
            phone: '010-12345678',
            email: '<EMAIL>',
            address: '计算机楼A座',
            establishDate: '1985-09-01',
            studentCount: 1200,
            teacherCount: 45,
            status: '正常',
            description: '专注于计算机科学与技术、软件工程、人工智能等专业的教学与科研'
          },
          {
            id: 2,
            name: '数学与统计学院',
            code: 'MATH',
            dean: '刘教授',
            phone: '010-12345679',
            email: '<EMAIL>',
            address: '数学楼B座',
            establishDate: '1978-09-01',
            studentCount: 800,
            teacherCount: 32,
            status: '正常',
            description: '涵盖数学与应用数学、统计学、信息与计算科学等专业'
          },
          {
            id: 3,
            name: '物理学院',
            code: 'PHY',
            dean: '赵教授',
            phone: '010-12345680',
            email: '<EMAIL>',
            address: '物理楼C座',
            establishDate: '1980-09-01',
            studentCount: 600,
            teacherCount: 28,
            status: '正常',
            description: '专业涵盖物理学、应用物理学、光电信息科学与工程等'
          },
          {
            id: 4,
            name: '化学学院',
            code: 'CHEM',
            dean: '周副教授',
            phone: '010-12345681',
            email: '<EMAIL>',
            address: '化学楼D座',
            establishDate: '1982-09-01',
            studentCount: 500,
            teacherCount: 25,
            status: '正常',
            description: '包含化学、应用化学、材料化学等专业方向'
          },
          {
            id: 5,
            name: '生物学院',
            code: 'BIO',
            dean: '郑教授',
            phone: '010-12345682',
            email: '<EMAIL>',
            address: '生物楼E座',
            establishDate: '1988-09-01',
            studentCount: 450,
            teacherCount: 22,
            status: '正常',
            description: '专业包括生物科学、生物技术、生物工程等'
          },
          {
            id: 6,
            name: '工程学院',
            code: 'ENG',
            dean: '韩副教授',
            phone: '010-12345683',
            email: '<EMAIL>',
            address: '工程楼F座',
            establishDate: '1990-09-01',
            studentCount: 900,
            teacherCount: 38,
            status: '正常',
            description: '涵盖机械工程、电气工程、自动化等工程类专业'
          }
        ]

        // 模拟分页数据
        const startIndex = (queryParams.pageNum - 1) * queryParams.pageSize
        const endIndex = startIndex + queryParams.pageSize
        departmentList.value = mockDepartments.slice(startIndex, endIndex)
        total.value = mockDepartments.length

        console.log('使用模拟院系数据，共', mockDepartments.length, '条记录，当前显示第', queryParams.pageNum, '页')
      } finally {
        loading.value = false
      }
    }
    
    // 处理查询按钮点击
    const handleQuery = () => {
      queryParams.pageNum = 1
      getList()
    }
    
    // 重置查询条件
    const resetQuery = () => {
      queryParams.departmentNo = ''
      queryParams.name = ''
      queryParams.dean = ''
      queryParams.status = undefined
      queryParams.pageNum = 1
      getList()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      queryParams.pageNum = val
      getList()
    }
    
    // 处理每页数量变化
    const handleSizeChange = (val) => {
      queryParams.pageSize = val
      queryParams.pageNum = 1
      getList()
    }
    
    // 查看院系详情
    const handleView = (row) => {
      ElMessage.info(`查看院系：${row.name}`)
    }
    
    // 编辑院系
    const handleEdit = (row) => {
      router.push({ name: 'DepartmentEdit', params: { id: row.id } })
    }
    
    // 添加院系
    const handleAddDepartment = () => {
      router.push({ name: 'DepartmentAdd' })
    }
    
    // 删除院系
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除院系${row.name}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 实际项目中这里需要调用API
          // await deleteDepartment(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      loading,
      departmentList,
      queryParams,
      total,
      handleQuery,
      resetQuery,
      handleCurrentChange,
      handleSizeChange,
      handleView,
      handleEdit,
      handleAddDepartment,
      handleDelete
    }
  }
}
</script>

<style scoped>
.department-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}
</style> 