import request from '@/utils/request'

const baseUrl = '/api/courses'

// 课程相关类型定义
export interface Course {
  id: number
  courseNo: string
  name: string
  credit: number
  hours: number
  departmentId: number
  teacherId: number
  semester: string
  description?: string
  status: number
  capacity?: number
  enrollCount?: number
  classroomId?: number
  timeSlot?: string
  createdTime?: string
  updatedTime?: string
}

export interface CourseQuery {
  name?: string
  department?: string
  semester?: string
  page?: number
  size?: number
}

export interface CoursePageResult {
  content: Course[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

export interface CreditStats {
  [key: string]: number
}

export interface SemesterStats {
  [key: string]: number
}

export interface DepartmentStats {
  [key: string]: number
}

// 获取所有课程信息
export function getAllCourses() {
  return request<{ data: Course[] }>({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取课程信息
export function getCoursesByPage(page = 0, size = 10, sortBy = 'id') {
  return request<{ data: CoursePageResult }>({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询课程信息
export function searchCourses({ name, department, semester, page = 0, size = 10 }: CourseQuery) {
  return request<{ data: CoursePageResult }>({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      department,
      semester,
      page,
      size
    }
  })
}

// 根据ID获取课程信息
export function getCourseById(id: number) {
  return request<{ data: Course }>({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建课程信息
export function createCourse(data: Partial<Course>) {
  return request<{ data: Course }>({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新课程信息
export function updateCourse(id: number, data: Partial<Course>) {
  return request<{ data: Course }>({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除课程信息
export function deleteCourse(id: number) {
  return request<void>({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取课程按学分分布统计
export function getCourseStatsByCredit() {
  return request<{ data: CreditStats }>({
    url: `${baseUrl}/stats/credit`,
    method: 'get'
  })
}

// 获取课程按学期分布统计
export function getCourseStatsBySemester() {
  return request<{ data: SemesterStats }>({
    url: `${baseUrl}/stats/semester`,
    method: 'get'
  })
}

// 获取课程按院系分布统计
export function getCourseStatsByDepartment() {
  return request<{ data: DepartmentStats }>({
    url: `${baseUrl}/stats/department`,
    method: 'get'
  })
} 