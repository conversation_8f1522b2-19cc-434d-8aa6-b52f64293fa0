import request from '@/utils/request'

const baseUrl = '/api/users'

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    roles: [{ id: 1, name: '超级管理员', code: 'SUPER_ADMIN' }],
    status: 1,
    createTime: '2024-01-01 10:00:00',
    lastLoginTime: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    username: 'teacher001',
    realName: '张教授',
    email: '<EMAIL>',
    phone: '13800138001',
    roles: [{ id: 3, name: '教师', code: 'TEACHER' }],
    status: 1,
    createTime: '2024-01-02 10:00:00',
    lastLoginTime: '2024-01-14 14:20:00'
  },
  {
    id: 3,
    username: 'student001',
    realName: '李小明',
    email: '<EMAIL>',
    phone: '13800138002',
    roles: [{ id: 4, name: '学生', code: 'STUDENT' }],
    status: 1,
    createTime: '2024-01-03 10:00:00',
    lastLoginTime: '2024-01-13 16:45:00'
  },
  {
    id: 4,
    username: 'teacher002',
    realName: '王老师',
    email: '<EMAIL>',
    phone: '13800138003',
    roles: [{ id: 3, name: '教师', code: 'TEACHER' }],
    status: 1,
    createTime: '2024-01-04 10:00:00',
    lastLoginTime: '2024-01-12 11:15:00'
  },
  {
    id: 5,
    username: 'student002',
    realName: '赵小红',
    email: '<EMAIL>',
    phone: '13800138004',
    roles: [{ id: 4, name: '学生', code: 'STUDENT' }],
    status: 0,
    createTime: '2024-01-05 10:00:00',
    lastLoginTime: null
  }
]

// 分页工具函数
const paginate = (data, page = 0, size = 10) => {
  const start = page * size
  const end = start + size
  return {
    content: data.slice(start, end),
    totalElements: data.length,
    totalPages: Math.ceil(data.length / size),
    size: size,
    number: page,
    first: page === 0,
    last: page >= Math.ceil(data.length / size) - 1
  }
}

// 用户登录
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

// 获取当前用户信息
export function getUserInfo() {
  return request({
    url: '/api/auth/info',
    method: 'get'
  })
}

// 获取当前用户完整信息
export function getCurrentUser() {
  return request({
    url: '/api/auth/profile',
    method: 'get'
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 获取所有用户信息
export function getAllUsers() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取用户信息
export function getUsersByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询用户信息
export function searchUsers({ username, realName, roleId, page = 0, size = 10 }) {
  // 先尝试真实API，失败则使用模拟数据
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      username,
      realName,
      roleId,
      page,
      size
    }
  }).catch(() => {
    console.log('🔄 用户搜索API不可用，使用模拟数据')

    // 过滤模拟数据
    let filteredUsers = mockUsers
    if (username) {
      filteredUsers = filteredUsers.filter(user =>
        user.username.toLowerCase().includes(username.toLowerCase())
      )
    }
    if (realName) {
      filteredUsers = filteredUsers.filter(user =>
        user.realName.includes(realName)
      )
    }
    if (roleId) {
      filteredUsers = filteredUsers.filter(user =>
        user.roles.some(role => role.id === roleId)
      )
    }

    return Promise.resolve(paginate(filteredUsers, page, size))
  })
}

// 根据ID获取用户信息
export function getUserById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建用户信息
export function createUser(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除用户信息
export function deleteUser(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: `${baseUrl}/change-password`,
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(id) {
  return request({
    url: `${baseUrl}/${id}/reset-password`,
    method: 'post'
  })
} 