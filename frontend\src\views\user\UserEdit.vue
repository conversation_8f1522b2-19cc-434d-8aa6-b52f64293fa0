<template>
  <div class="user-edit-container">
    <div class="header">
      <h2>{{ isEdit ? '编辑用户' : '添加用户' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="userForm"
      :rules="rules"
      label-width="100px"
      class="user-form"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="userForm.username" :disabled="isEdit" placeholder="请输入用户名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="realName">
            <el-input v-model="userForm.realName" placeholder="请输入真实姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="userForm.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="正常" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!isEdit">
          <el-form-item label="密码" prop="password">
            <el-input v-model="userForm.password" type="password" placeholder="请输入密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="角色" prop="roleIds">
        <el-checkbox-group v-model="userForm.roleIds">
          <el-checkbox 
            v-for="role in roleOptions" 
            :key="role.id" 
            :label="role.id">
            {{ role.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="userForm.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserById, createUser, updateUser } from '@/api/user'
import { getAllRoles } from '@/api/role'

export default {
  name: 'UserEdit',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)
    const roleOptions = ref([])

    // 判断是编辑还是新增
    const isEdit = computed(() => {
      return route.params.id !== undefined
    })

    // 表单数据
    const userForm = reactive({
      id: null,
      username: '',
      realName: '',
      password: '',
      email: '',
      phone: '',
      status: 1,
      roleIds: [],
      remark: ''
    })

    // 表单验证规则
    const rules = reactive({
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
      ],
      realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { 
          required: !isEdit.value, 
          message: '请输入密码', 
          trigger: 'blur' 
        },
        { 
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, 
          message: '密码必须包含大小写字母和数字，且不少于8位', 
          trigger: 'blur' 
        }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ],
      roleIds: [
        { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
      ]
    })

    // 获取角色列表
    const fetchRoles = async () => {
      try {
        const res = await getAllRoles()
        if (res.data) {
          roleOptions.value = res.data
        }
      } catch (error) {
        console.error('获取角色列表失败:', error)
        ElMessage.error('获取角色列表失败')
      }
    }

    // 获取用户详情
    const fetchUserDetail = async (id) => {
      loading.value = true
      try {
        const res = await getUserById(id)
        if (res.data) {
          // 将后端返回的数据填充到表单中
          const userData = res.data
          userForm.id = userData.id
          userForm.username = userData.username
          userForm.realName = userData.realName
          userForm.email = userData.email
          userForm.phone = userData.phone
          userForm.status = userData.status
          userForm.remark = userData.remark
          
          // 转换角色数据
          if (userData.roles && userData.roles.length > 0) {
            userForm.roleIds = userData.roles.map(role => role.id)
          }
        }
      } catch (error) {
        console.error('获取用户详情失败:', error)
        ElMessage.error('获取用户详情失败')
      } finally {
        loading.value = false
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            // 构建提交的数据
            const submitData = { ...userForm }
            
            // 编辑模式下，如果密码为空，则删除密码字段
            if (isEdit.value && !submitData.password) {
              delete submitData.password
            }
            
            if (isEdit.value) {
              // 编辑模式
              await updateUser(submitData.id, submitData)
              ElMessage.success('更新用户信息成功')
            } else {
              // 新增模式
              await createUser(submitData)
              ElMessage.success('添加用户成功')
            }
            goBack()
          } catch (error) {
            console.error('保存用户信息失败:', error)
            ElMessage.error('保存用户信息失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 返回上一页
    const goBack = () => {
      router.push('/users')
    }

    // 组件挂载时初始化
    onMounted(() => {
      fetchRoles()
      if (isEdit.value) {
        fetchUserDetail(route.params.id)
      }
    })

    return {
      formRef,
      loading,
      isEdit,
      userForm,
      roleOptions,
      rules,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.user-edit-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.user-form {
  max-width: 1000px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 