package com.university.management.service.impl;

import com.university.management.model.entity.Dormitory;
import com.university.management.repository.DormitoryRepository;
import com.university.management.service.DormitoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 宿舍服务实现类
 */
@Service
@Transactional
public class DormitoryServiceImpl implements DormitoryService {

    private final DormitoryRepository dormitoryRepository;

    @Autowired
    public DormitoryServiceImpl(DormitoryRepository dormitoryRepository) {
        this.dormitoryRepository = dormitoryRepository;
    }

    @Override
    public Dormitory save(Dormitory dormitory) {
        return dormitoryRepository.save(dormitory);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Dormitory> findById(Integer id) {
        return dormitoryRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Dormitory> findAll() {
        return dormitoryRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Dormitory> findAll(Pageable pageable) {
        return dormitoryRepository.findAll(pageable);
    }

    @Override
    public void deleteById(Integer id) {
        dormitoryRepository.deleteById(id);
    }
} 