<template>
  <div class="dormitory-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑宿舍' : '新增宿舍' }}</h2>
    </div>

    <el-card class="form-container">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        label-position="right">

        <el-form-item label="宿舍楼" prop="building">
          <el-input v-model="form.building" placeholder="请输入宿舍楼" />
        </el-form-item>

        <el-form-item label="房间号" prop="roomNo">
          <el-input v-model="form.roomNo" placeholder="请输入房间号" />
        </el-form-item>

        <el-form-item label="宿舍类型" prop="roomType">
          <el-select v-model="form.roomType" placeholder="请选择宿舍类型" style="width: 100%">
            <el-option label="标准间" :value="0" />
            <el-option label="单人间" :value="1" />
            <el-option label="套间" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="容量" prop="capacity">
          <el-input-number v-model="form.capacity" :min="1" :max="10" />
        </el-form-item>

        <el-form-item label="已住人数" prop="occupied">
          <el-input-number v-model="form.occupied" :min="0" :max="10" />
        </el-form-item>

        <el-form-item label="费用" prop="fee">
          <el-input-number v-model="form.fee" :min="0" :step="100" :precision="0" />
          <span class="unit">元/月</span>
        </el-form-item>

        <el-form-item label="设施" prop="facilities">
          <el-input v-model="form.facilities" type="textarea" :rows="3" placeholder="请输入宿舍设施信息" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入宿舍描述" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 引入API，实际项目中需要添加
// import { getDormitory, createDormitory, updateDormitory } from '@/api/dormitory'

export default {
  name: 'DormitoryEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const dormitoryId = computed(() => route.params.id)
    const isEdit = computed(() => !!dormitoryId.value)

    // 表单数据
    const form = reactive({
      building: '',
      roomNo: '',
      roomType: 0,
      capacity: 4,
      occupied: 0,
      fee: 800,
      facilities: '',
      description: ''
    })

    // 表单验证规则
    const rules = reactive({
      building: [{ required: true, message: '请输入宿舍楼', trigger: 'blur' }],
      roomNo: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
      roomType: [{ required: true, message: '请选择宿舍类型', trigger: 'change' }],
      capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }],
      occupied: [{ required: true, message: '请输入已住人数', trigger: 'blur' }],
      fee: [{ required: true, message: '请输入费用', trigger: 'blur' }]
    })

    // 获取宿舍详情
    const fetchDormitoryDetail = async (id) => {
      try {
        // 实际项目中这里需要调用API
        // const response = await getDormitory(id)
        // Object.assign(form, response.data)
        ElMessage.info('模拟加载宿舍数据')
        // 模拟数据
        Object.assign(form, {
          building: '1号楼',
          roomNo: '101',
          roomType: 0,
          capacity: 4,
          occupied: 4,
          fee: 800,
          facilities: '空调、热水器、饮水机、书桌、衣柜',
          description: '男生宿舍，位于1号楼1层'
        })
      } catch (error) {
        ElMessage.error('获取宿舍信息失败')
        console.error(error)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        // 确保已住人数不大于容量
        if (form.occupied > form.capacity) {
          ElMessage.warning('已住人数不能大于容量')
          return
        }
        
        // 实际项目中这里需要调用API
        if (isEdit.value) {
          // await updateDormitory(dormitoryId.value, form)
          ElMessage.success('更新宿舍成功')
        } else {
          // await createDormitory(form)
          ElMessage.success('创建宿舍成功')
        }
        
        // 返回列表页
        router.push({ name: 'DormitoryList' })
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.back()
    }

    // 组件挂载时，如果是编辑模式，获取宿舍详情
    onMounted(() => {
      if (isEdit.value) {
        fetchDormitoryDetail(dormitoryId.value)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.dormitory-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 800px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}
</style> 