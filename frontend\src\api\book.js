import request from '@/utils/request'

const baseUrl = '/api/books'

// 获取所有图书信息
export function getAllBooks() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取图书信息
export function getBooksByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询图书信息
export function searchBooks({ title, author, isbn, category, available, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      title,
      author,
      isbn,
      category,
      available,
      page,
      size
    }
  })
}

// 根据ID获取图书信息
export function getBookById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建图书信息
export function createBook(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新图书信息
export function updateBook(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除图书信息
export function deleteBook(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取图书分类统计
export function getBookStatsByCategory() {
  return request({
    url: `${baseUrl}/stats/category`,
    method: 'get'
  })
}

// 获取图书状态统计
export function getBookStatsByStatus() {
  return request({
    url: `${baseUrl}/stats/status`,
    method: 'get'
  })
}
