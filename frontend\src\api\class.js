import request from '@/utils/request'

const baseUrl = '/api/classes'

// 获取所有班级信息
export function getAllClasses() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取班级信息
export function getClassesByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询班级信息
export function searchClasses({ name, grade, majorId, advisorId, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      grade,
      majorId,
      advisorId,
      page,
      size
    }
  })
}

// 根据ID获取班级信息
export function getClassById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建班级信息
export function createClass(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新班级信息
export function updateClass(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除班级信息
export function deleteClass(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 根据专业ID获取班级列表
export function getClassesByMajor(majorId) {
  return request({
    url: `${baseUrl}/major/${majorId}`,
    method: 'get'
  })
}

// 获取班级学生列表
export function getClassStudents(classId) {
  return request({
    url: `${baseUrl}/${classId}/students`,
    method: 'get'
  })
}

// 获取班级统计信息
export function getClassStats() {
  return request({
    url: `${baseUrl}/stats`,
    method: 'get'
  })
}
