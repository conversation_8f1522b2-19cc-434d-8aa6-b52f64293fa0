<template>
  <div class="class-list-container">
    <div class="header">
      <h2><el-icon><UserFilled /></el-icon> 班级管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加班级
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="班级名称">
          <el-input v-model="searchForm.name" placeholder="请输入班级名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="年级">
          <el-select v-model="searchForm.grade" placeholder="请选择年级" clearable>
            <el-option label="2021级" value="2021"></el-option>
            <el-option label="2022级" value="2022"></el-option>
            <el-option label="2023级" value="2023"></el-option>
            <el-option label="2024级" value="2024"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业">
          <el-select v-model="searchForm.majorId" placeholder="请选择专业" clearable>
            <el-option 
              v-for="major in majorOptions" 
              :key="major.id" 
              :label="major.name" 
              :value="major.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table 
        v-loading="loading" 
        :data="classList" 
        border 
        style="width: 100%" 
        row-key="id"
        @sort-change="handleSortChange">
        <el-table-column prop="name" label="班级名称" width="150"></el-table-column>
        <el-table-column prop="grade" label="年级" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="majorId" label="专业" width="180">
          <template #default="scope">
            {{ getMajorName(scope.row.majorId) }}
          </template>
        </el-table-column>
        <el-table-column prop="teacherId" label="班主任" width="120">
          <template #default="scope">
            {{ getTeacherName(scope.row.teacherId) }}
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" label="学生人数" width="100" sortable="custom">
          <template #default="scope">
            <el-link type="primary" @click="viewStudents(scope.row)">
              {{ scope.row.studentCount || 0 }}人
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="maxStudents" label="最大人数" width="100"></el-table-column>
        <el-table-column prop="classroomId" label="教室" width="120">
          <template #default="scope">
            {{ getClassroomName(scope.row.classroomId) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isDeleted === 0 ? 'success' : 'danger'">
              {{ scope.row.isDeleted === 0 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable="custom">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" type="primary" @click="handleEdit(scope.row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="success" @click="viewStudents(scope.row)">
                <el-icon><User /></el-icon>
                学生
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <h3><el-icon><PieChart /></el-icon> 年级分布</h3>
        <div id="gradeChart" style="width: 100%; height: 300px;"></div>
      </div>
      <div class="chart-container">
        <h3><el-icon><Histogram /></el-icon> 各专业班级数量</h3>
        <div id="majorChart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete, User, UserFilled, PieChart, Histogram } from '@element-plus/icons-vue'
import { searchClasses, deleteClass, getClassStats } from '@/api/class'
import { getAllMajors } from '@/api/major'
import * as echarts from 'echarts'

export default {
  name: 'ClassList',
  components: {
    Plus, Search, Refresh, View, Edit, Delete, User, UserFilled, PieChart, Histogram
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const classList = ref([])
    const majorOptions = ref([])
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      grade: '',
      majorId: null
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 排序信息
    const sortInfo = reactive({
      prop: '',
      order: ''
    })

    // 图表实例
    const gradeChart = ref(null)
    const majorChart = ref(null)

    // 获取班级列表数据
    const fetchClassData = async () => {
      loading.value = true
      try {
        const params = {
          name: searchForm.name || null,
          grade: searchForm.grade || null,
          majorId: searchForm.majorId || null,
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        }

        const res = await searchClasses(params)
        if (res) {
          classList.value = res.content || []
          pagination.total = res.totalElements || 0
        }
      } catch (error) {
        console.error('获取班级数据失败:', error)
        if (error.message && error.message.includes('404')) {
          ElMessage.warning('班级管理功能暂未开放，请联系系统管理员')
        } else {
          ElMessage.error('获取班级数据失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 获取专业选项
    const fetchMajorOptions = async () => {
      try {
        const res = await getAllMajors()
        if (res) {
          majorOptions.value = res
        }
      } catch (error) {
        console.error('获取专业数据失败:', error)
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchClassData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        name: '',
        grade: '',
        majorId: null
      })
      pagination.currentPage = 1
      fetchClassData()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      pagination.currentPage = 1
      fetchClassData()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchClassData()
    }

    // 排序改变
    const handleSortChange = ({ prop, order }) => {
      sortInfo.prop = prop
      sortInfo.order = order
      fetchClassData()
    }

    // 添加班级
    const handleAdd = () => {
      router.push('/classes/add')
    }

    // 查看班级
    const handleView = (row) => {
      ElMessage.info(`查看班级：${row.name}`)
    }

    // 编辑班级
    const handleEdit = (row) => {
      router.push(`/classes/edit/${row.id}`)
    }

    // 删除班级
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除班级 ${row.name} 吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        await deleteClass(row.id)
        ElMessage.success('删除成功')
        fetchClassData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除班级失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 查看学生
    const viewStudents = (row) => {
      router.push(`/students?classId=${row.id}`)
    }

    // 根据专业ID获取专业名称
    const getMajorName = (majorId) => {
      if (!majorId) return '未设置'
      const major = majorOptions.value.find(major => major.id === majorId)
      return major ? major.name : `专业ID: ${majorId}`
    }

    // 根据教师ID获取教师名称
    const getTeacherName = (teacherId) => {
      if (!teacherId) return '未设置'
      // 这里可以添加教师数据获取逻辑
      return `教师ID: ${teacherId}`
    }

    // 根据教室ID获取教室名称
    const getClassroomName = (classroomId) => {
      if (!classroomId) return '未设置'
      // 这里可以添加教室数据获取逻辑
      return `教室ID: ${classroomId}`
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    }

    // 初始化图表
    const initCharts = async () => {
      try {
        const statsRes = await getClassStats()
        if (statsRes) {
          // 年级分布图表
          const gradeChartInstance = echarts.init(document.getElementById('gradeChart'))
          gradeChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [
              {
                name: '年级分布',
                type: 'pie',
                radius: '50%',
                data: statsRes.gradeStats || [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
          gradeChart.value = gradeChartInstance

          // 专业班级数量图表
          const majorChartInstance = echarts.init(document.getElementById('majorChart'))
          majorChartInstance.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: statsRes.majorStats?.map(item => item.name) || []
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '班级数量',
                type: 'bar',
                data: statsRes.majorStats?.map(item => item.count) || [],
                itemStyle: {
                  color: '#67C23A'
                }
              }
            ]
          })
          majorChart.value = majorChartInstance
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 窗口大小改变时重新调整图表
    const resizeCharts = () => {
      if (gradeChart.value) {
        gradeChart.value.resize()
      }
      if (majorChart.value) {
        majorChart.value.resize()
      }
    }

    onMounted(() => {
      fetchClassData()
      fetchMajorOptions()
      setTimeout(() => {
        initCharts()
      }, 100)
      window.addEventListener('resize', resizeCharts)
    })

    onUnmounted(() => {
      if (gradeChart.value) {
        gradeChart.value.dispose()
      }
      if (majorChart.value) {
        majorChart.value.dispose()
      }
      window.removeEventListener('resize', resizeCharts)
    })

    return {
      loading,
      classList,
      majorOptions,
      searchForm,
      pagination,
      fetchClassData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleView,
      handleEdit,
      handleDelete,
      viewStudents,
      getMajorName,
      getTeacherName,
      getClassroomName,
      formatDate
    }
  }
}
</script>

<style scoped>
.class-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  margin: 0;
}
</style>
