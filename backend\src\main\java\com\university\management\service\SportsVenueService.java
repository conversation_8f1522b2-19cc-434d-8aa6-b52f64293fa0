package com.university.management.service;

import com.university.management.model.entity.SportsVenue;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 体育场馆服务接口
 */
public interface SportsVenueService {

    /**
     * 创建体育场馆
     *
     * @param venue 体育场馆信息
     * @return 创建后的体育场馆
     */
    SportsVenue createVenue(SportsVenue venue);

    /**
     * 更新体育场馆
     *
     * @param id 体育场馆ID
     * @param venueDetails 体育场馆详情
     * @return 更新后的体育场馆
     */
    SportsVenue updateVenue(Integer id, SportsVenue venueDetails);

    /**
     * 删除体育场馆
     *
     * @param id 体育场馆ID
     */
    void deleteVenue(Integer id);

    /**
     * 根据ID查询体育场馆
     *
     * @param id 体育场馆ID
     * @return 体育场馆信息
     */
    Optional<SportsVenue> findById(Integer id);

    /**
     * 根据名称查询体育场馆
     *
     * @param name 体育场馆名称
     * @return 体育场馆信息
     */
    Optional<SportsVenue> findByName(String name);

    /**
     * 查询所有体育场馆
     *
     * @return 体育场馆列表
     */
    List<SportsVenue> findAll();

    /**
     * 分页查询体育场馆
     *
     * @param pageable 分页参数
     * @return 体育场馆分页结果
     */
    Page<SportsVenue> findAll(Pageable pageable);

    /**
     * 条件查询体育场馆
     *
     * @param venueName 场馆名称
     * @param venueType 场馆类型
     * @param location 场馆位置
     * @param isAvailable 是否可用
     * @param pageable 分页参数
     * @return 体育场馆分页结果
     */
    Page<SportsVenue> findByConditions(String venueName, Integer venueType, String location, Boolean isAvailable, Pageable pageable);

    /**
     * 根据类型查询体育场馆
     *
     * @param type 场馆类型
     * @return 体育场馆列表
     */
    List<SportsVenue> findByType(Integer type);

    /**
     * 根据位置查询体育场馆
     *
     * @param location 场馆位置
     * @return 体育场馆列表
     */
    List<SportsVenue> findByLocation(String location);

    /**
     * 获取体育场馆类型分布统计
     *
     * @return 类型分布统计
     */
    Map<Integer, Long> getVenueStatsByType();

    /**
     * 统计可用体育场馆数量
     *
     * @return 可用体育场馆数量
     */
    Long countAvailableVenues();
} 