-- 数据库初始化数据脚本

-- 使用数据库
USE university_management;

-- 初始化院系数据
INSERT IGNORE INTO department (department_no, name, description, dean, telephone, email, address, established_date, website, sort, status)
VALUES
('D001', '计算机科学与技术学院', '计算机科学与技术学院是一所专注于计算机科学教育和研究的学院。', '张教授', '0571-88888001', '<EMAIL>', '大学校区1号楼', '1980-09-01', 'http://cs.university.edu', 1, 0),
('D002', '电子信息工程学院', '电子信息工程学院主要研究电子与信息领域的技术与应用。', '李教授', '0571-88888002', '<EMAIL>', '大学校区2号楼', '1985-09-01', 'http://ee.university.edu', 2, 0),
('D003', '机械工程学院', '机械工程学院专注于机械设计、制造及其自动化技术研究。', '王教授', '0571-88888003', '<EMAIL>', '大学校区3号楼', '1978-09-01', 'http://me.university.edu', 3, 0),
('D004', '经济管理学院', '经济管理学院致力于培养经济管理领域的高级人才。', '赵教授', '0571-88888004', '<EMAIL>', '大学校区4号楼', '1990-09-01', 'http://em.university.edu', 4, 0),
('D005', '外国语学院', '外国语学院提供多种语言的教育和研究。', '刘教授', '0571-88888005', '<EMAIL>', '大学校区5号楼', '1995-09-01', 'http://fl.university.edu', 5, 0);

-- 初始化专业数据
INSERT IGNORE INTO major (major_no, name, department_id, description, length, degree)
VALUES
('M001', '计算机科学与技术', 1, '计算机科学与技术专业培养具有扎实计算机理论基础的复合型人才。', '4年', '工学学士'),
('M002', '软件工程', 1, '软件工程专业培养软件设计、开发和测试等方面的专业人才。', '4年', '工学学士'),
('M003', '网络工程', 1, '网络工程专业培养计算机网络设计、建设与维护的专业人才。', '4年', '工学学士'),
('M004', '电子信息工程', 2, '电子信息工程专业培养电子设备与信息系统的设计与开发人才。', '4年', '工学学士'),
('M005', '通信工程', 2, '通信工程专业培养现代通信系统设计与维护的专业人才。', '4年', '工学学士'),
('M006', '机械设计制造及其自动化', 3, '机械设计制造及其自动化专业培养机械领域的专业人才。', '4年', '工学学士'),
('M007', '工业设计', 3, '工业设计专业培养产品设计与开发的专业人才。', '4年', '工学学士'),
('M008', '工商管理', 4, '工商管理专业培养企业管理与经营的专业人才。', '4年', '管理学学士'),
('M009', '市场营销', 4, '市场营销专业培养市场分析与营销策略的专业人才。', '4年', '管理学学士'),
('M010', '英语', 5, '英语专业培养英语语言文学与翻译的专业人才。', '4年', '文学学士');

-- 初始化班级数据
INSERT IGNORE INTO class (class_no, name, major_id, department_id, grade, class_size)
VALUES
('C2020001', '计算机2020级1班', 1, 1, 2020, 30),
('C2020002', '软件2020级1班', 2, 1, 2020, 30),
('C2020003', '网络2020级1班', 3, 1, 2020, 30),
('C2021001', '计算机2021级1班', 1, 1, 2021, 30),
('C2021002', '软件2021级1班', 2, 1, 2021, 30),
('C2021003', '电子2021级1班', 4, 2, 2021, 30),
('C2021004', '通信2021级1班', 5, 2, 2021, 30),
('C2022001', '机械2022级1班', 6, 3, 2022, 30),
('C2022002', '工设2022级1班', 7, 3, 2022, 30),
('C2022003', '工管2022级1班', 8, 4, 2022, 30);

-- 初始化教师数据
INSERT IGNORE INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, id_card, college_id, department_id, hire_date, status)
VALUES
('T001', '张三', 1, '1975-06-15', 48, 3, '13900001111', '<EMAIL>', '330102197506150011', 1, 1, '2000-07-01', 0),
('T002', '李四', 1, '1980-03-20', 43, 2, '13900002222', '<EMAIL>', '330102198003200022', 1, 1, '2005-07-01', 0),
('T003', '王五', 1, '1982-09-10', 41, 1, '13900003333', '<EMAIL>', '330102198209100033', 1, 1, '2010-07-01', 0),
('T004', '赵六', 0, '1985-12-05', 38, 1, '13900004444', '<EMAIL>', '330102198512050044', 1, 2, '2012-07-01', 0),
('T005', '钱七', 0, '1988-05-25', 35, 0, '13900005555', '<EMAIL>', '330102198805250055', 1, 2, '2015-07-01', 0),
('T006', '孙八', 1, '1979-11-30', 44, 2, '13900006666', '<EMAIL>', '330102197911300066', 1, 3, '2007-07-01', 0),
('T007', '周九', 0, '1983-08-18', 40, 1, '13900007777', '<EMAIL>', '330102198308180077', 1, 4, '2011-07-01', 0),
('T008', '吴十', 1, '1986-02-14', 37, 1, '13900008888', '<EMAIL>', '330102198602140088', 1, 5, '2013-07-01', 0);

-- 初始化课程数据
INSERT IGNORE INTO course (course_no, name, credit, hours, type, department_id, description, status)
VALUES
('C001', 'Java程序设计', 3.0, 48, 0, 1, 'Java语言基础、面向对象编程及应用开发。', 0),
('C002', '数据结构', 4.0, 64, 0, 1, '基本数据结构与算法的原理与实现。', 0),
('C003', '操作系统', 3.5, 56, 0, 1, '操作系统原理与设计。', 0),
('C004', '计算机网络', 3.0, 48, 0, 1, '计算机网络体系结构与协议。', 0),
('C005', '数据库系统', 3.5, 56, 0, 1, '关系数据库理论与应用。', 0),
('C006', '电路分析', 4.0, 64, 0, 2, '电路分析基础理论与方法。', 0),
('C007', '信号与系统', 3.5, 56, 0, 2, '信号处理与系统分析。', 0),
('C008', '机械设计基础', 4.0, 64, 0, 3, '机械设计的基本理论与方法。', 0),
('C009', '管理学原理', 3.0, 48, 0, 4, '管理学基本理论与应用。', 0),
('C010', '大学英语', 2.0, 32, 1, 5, '英语语言基础课程。', 0);

-- 初始化教室数据
INSERT IGNORE INTO classroom (building, room_no, name, floor, type, capacity, status)
VALUES
('第一教学楼', '101', '101教室', 1, 0, 60, 0),
('第一教学楼', '102', '102教室', 1, 0, 60, 0),
('第一教学楼', '201', '201教室', 2, 1, 80, 0),
('第一教学楼', '202', '202教室', 2, 1, 80, 0),
('第二教学楼', '101', '101教室', 1, 0, 60, 0),
('第二教学楼', '102', '102教室', 1, 0, 60, 0),
('第二教学楼', '201', '201教室', 2, 2, 50, 0),
('第二教学楼', '202', '202教室', 2, 2, 50, 0);

-- 初始化宿舍数据
INSERT IGNORE INTO dormitory (dormitory_no, building, floor, room_no, type, capacity, occupied, fee, status)
VALUES
('D1-101', '1号宿舍楼', 1, '101', 0, 4, 2, 800.0, 0),
('D1-102', '1号宿舍楼', 1, '102', 0, 4, 3, 800.0, 0),
('D1-201', '1号宿舍楼', 2, '201', 0, 4, 4, 800.0, 2),
('D1-202', '1号宿舍楼', 2, '202', 0, 4, 0, 800.0, 0),
('D2-101', '2号宿舍楼', 1, '101', 1, 4, 2, 800.0, 0),
('D2-102', '2号宿舍楼', 1, '102', 1, 4, 3, 800.0, 0),
('D2-201', '2号宿舍楼', 2, '201', 1, 4, 4, 800.0, 2),
('D2-202', '2号宿舍楼', 2, '202', 1, 4, 0, 800.0, 0);

-- 初始化学生数据
INSERT IGNORE INTO student (student_no, name, gender, birthday, age, id_card, phone, email, address, class_id, major_id, college_id, department_id, enroll_year, status, dormitory_id)
VALUES
('S20200101', '张小明', 1, '2002-03-12', 21, '330102200203120011', '13800001111', '<EMAIL>', '杭州市西湖区', 1, 1, 1, 1, 2020, 0, 1),
('S20200102', '李小红', 0, '2002-05-20', 21, '330102200205200022', '13800002222', '<EMAIL>', '杭州市拱墅区', 1, 1, 1, 1, 2020, 0, 5),
('S20200201', '王小刚', 1, '2002-07-15', 21, '330102200207150033', '13800003333', '<EMAIL>', '杭州市滨江区', 2, 2, 1, 1, 2020, 0, 1),
('S20200202', '赵小芳', 0, '2002-09-25', 21, '330102200209250044', '13800004444', '<EMAIL>', '杭州市上城区', 2, 2, 1, 1, 2020, 0, 5),
('S20210101', '钱小伟', 1, '2003-02-18', 20, '330102200302180055', '13800005555', '<EMAIL>', '杭州市下城区', 4, 1, 1, 1, 2021, 0, 2),
('S20210102', '孙小燕', 0, '2003-04-30', 20, '330102200304300066', '13800006666', '<EMAIL>', '杭州市江干区', 4, 1, 1, 1, 2021, 0, 6),
('S20210301', '周小强', 1, '2003-06-22', 20, '330102200306220077', '13800007777', '<EMAIL>', '杭州市余杭区', 6, 4, 1, 2, 2021, 0, 2),
('S20210302', '吴小梅', 0, '2003-08-11', 20, '330102200308110088', '13800008888', '<EMAIL>', '杭州市萧山区', 6, 4, 1, 2, 2021, 0, 6);

-- 初始化图书数据
INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, total, available, location, price, status)
VALUES
('9787302224730', 'Java编程思想', 'Bruce Eckel', '清华大学出版社', '2007-06-01', 1, 10, 8, 'A区-01-01', 108.00, 0),
('9787115279460', 'C++程序设计', 'Stanley B. Lippman', '人民邮电出版社', '2013-07-01', 1, 8, 6, 'A区-01-02', 89.00, 0),
('9787115435590', 'Python编程：从入门到实践', 'Eric Matthes', '人民邮电出版社', '2016-07-01', 1, 12, 10, 'A区-01-03', 79.00, 0),
('9787111213826', '算法导论', 'Thomas H. Cormen', '机械工业出版社', '2006-09-01', 1, 6, 4, 'A区-02-01', 128.00, 0),
('9787111407010', '数据结构与算法分析', 'Mark Allen Weiss', '机械工业出版社', '2012-08-01', 1, 8, 7, 'A区-02-02', 85.00, 0),
('9787111526285', '深入理解计算机系统', 'Randal E. Bryant', '机械工业出版社', '2016-01-01', 1, 5, 3, 'A区-03-01', 139.00, 0),
('9787111321330', '操作系统概念', 'Abraham Silberschatz', '机械工业出版社', '2010-03-01', 1, 7, 5, 'A区-03-02', 98.00, 0),
('9787115299222', '数据库系统概念', 'Abraham Silberschatz', '人民邮电出版社', '2012-10-01', 1, 9, 7, 'A区-04-01', 99.00, 0),
('9787121315800', '大学物理学', '赵近芳', '电子工业出版社', '2017-08-01', 2, 15, 12, 'B区-01-01', 59.00, 0),
('9787040396638', '高等数学', '同济大学数学系', '高等教育出版社', '2014-07-01', 4, 20, 15, 'B区-02-01', 42.00, 0);

-- 初始化体育场馆数据
INSERT IGNORE INTO sports_venue (name, type, location, capacity, opening_hours, status)
VALUES
('主体育馆', 0, '校区中心', 2000, '08:00-22:00', 0),
('足球场', 1, '校区北侧', 500, '08:00-21:00', 0),
('篮球场', 1, '校区东侧', 300, '08:00-21:00', 0),
('网球场', 1, '校区西侧', 100, '08:00-20:00', 0),
('游泳馆', 0, '校区南侧', 200, '09:00-21:00', 0);

-- 初始化系统角色
INSERT IGNORE INTO sys_role (role_name, role_code, description, status)
VALUES
('管理员', 'ROLE_ADMIN', '系统最高权限，可以管理所有功能', 0),
('院系管理员', 'ROLE_COLLEGE_ADMIN', '院系管理员，可以管理本院系的数据', 0),
('教师', 'ROLE_TEACHER', '教师角色，可以管理课程、成绩等数据', 0),
('学生', 'ROLE_STUDENT', '学生角色，可以查看选课、成绩等数据', 0);

-- 初始化系统用户
INSERT IGNORE INTO sys_user (username, password, real_name, user_type, related_id, phone, email, status)
VALUES
('admin', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '管理员', 0, NULL, '13900000000', '<EMAIL>', 0),
('teacher1', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '张三', 2, 1, '13900001111', '<EMAIL>', 0),
('teacher2', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '李四', 2, 2, '13900002222', '<EMAIL>', 0),
('student1', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '张小明', 1, 1, '13800001111', '<EMAIL>', 0),
('student2', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '李小红', 1, 2, '13800002222', '<EMAIL>', 0);

-- 初始化用户角色关联
INSERT INTO sys_user_role (user_id, role_id)
VALUES
(1, 1),
(2, 3),
(3, 3),
(4, 4),
(5, 4);

-- 初始化系统菜单 (简化版本，避免外键约束问题)
INSERT IGNORE INTO sys_menu (parent_id, name, path, component, icon, sort, visible, type, permission, status)
VALUES
-- 顶级菜单
(0, '系统管理', '/system', 'Layout', 'el-icon-setting', 1, 0, 0, '', 0),
(0, '院系管理', '/department', 'Layout', 'el-icon-office-building', 2, 0, 0, '', 0),
(0, '人员管理', '/personnel', 'Layout', 'el-icon-user', 3, 0, 0, '', 0),
(0, '学术管理', '/academic', 'Layout', 'el-icon-reading', 4, 0, 0, '', 0),
(0, '资源管理', '/resources', 'Layout', 'el-icon-office-building', 5, 0, 0, '', 0),
-- 二级菜单 (使用固定的parent_id，如果不存在会被忽略)
(1, '用户管理', '/system/user', 'system/user/index', 'el-icon-user', 1, 0, 1, 'system:user:list', 0),
(1, '角色管理', '/system/role', 'system/role/index', 'el-icon-s-custom', 2, 0, 1, 'system:role:list', 0),
(2, '院系列表', '/department/list', 'department/list', 'el-icon-school', 1, 0, 1, 'department:list', 0),
(3, '教师管理', '/personnel/teacher', 'personnel/teacher/index', 'el-icon-s-custom', 1, 0, 1, 'teacher:list', 0),
(3, '学生管理', '/personnel/student', 'personnel/student/index', 'el-icon-user', 2, 0, 1, 'student:list', 0);


-- 初始化角色菜单关联
INSERT INTO sys_role_menu (role_id, menu_id)
VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10), 
(1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17), (1, 18), (1, 19), (1, 20), (1, 21),
(2, 5), (2, 6), (2, 7), (2, 8), (2, 9), (2, 10), (2, 11), (2, 12), (2, 13), (2, 14), (2, 15), (2, 16),
(3, 12), (3, 13), (3, 14), (3, 15), (3, 16),
(4, 12), (4, 15);

-- 初始化排课数据
INSERT INTO schedule (course_id, teacher_id, classroom_id, semester, day_of_week, start_time, end_time, start_week, end_week)
VALUES
(1, 1, 3, '2023-2024-1', 1, '08:00', '09:40', 1, 16),
(2, 2, 3, '2023-2024-1', 2, '08:00', '09:40', 1, 16),
(3, 3, 4, '2023-2024-1', 3, '08:00', '09:40', 1, 16),
(4, 1, 4, '2023-2024-1', 4, '08:00', '09:40', 1, 16),
(5, 2, 3, '2023-2024-1', 5, '08:00', '09:40', 1, 16),
(6, 4, 5, '2023-2024-1', 1, '10:00', '11:40', 1, 16),
(7, 5, 5, '2023-2024-1', 2, '10:00', '11:40', 1, 16),
(8, 6, 6, '2023-2024-1', 3, '10:00', '11:40', 1, 16),
(9, 7, 6, '2023-2024-1', 4, '10:00', '11:40', 1, 16),
(10, 8, 1, '2023-2024-1', 5, '10:00', '11:40', 1, 16);

-- 初始化选课数据
INSERT INTO student_course (student_id, schedule_id, score, grade_point)
VALUES
(1, 1, 85.5, 3.5),
(1, 2, 92.0, 4.0),
(1, 10, 88.0, 3.7),
(2, 1, 78.0, 3.0),
(2, 2, 86.5, 3.6),
(2, 10, 90.0, 4.0),
(3, 1, 82.0, 3.3),
(3, 2, 75.5, 2.7),
(3, 10, 84.0, 3.4),
(4, 1, 93.0, 4.0),
(4, 2, 89.0, 3.8),
(4, 10, 76.5, 2.8);

-- 初始化借阅记录
INSERT INTO borrow_record (student_id, book_id, borrow_date, expected_return_date, actual_return_date, status)
VALUES
(1, 1, '2023-09-01', '2023-09-15', '2023-09-10', 1),
(1, 3, '2023-09-05', '2023-09-19', NULL, 0),
(2, 2, '2023-09-02', '2023-09-16', '2023-09-16', 1),
(2, 4, '2023-09-07', '2023-09-21', NULL, 0),
(3, 5, '2023-09-03', '2023-09-17', '2023-09-12', 1),
(4, 6, '2023-09-04', '2023-09-18', NULL, 0);

-- 初始化场馆预约
INSERT INTO venue_booking (venue_id, user_id, user_type, booking_date, start_time, end_time, purpose, status)
VALUES
(1, 1, 1, '2023-09-20', '14:00', '16:00', '学生活动', 1),
(2, 2, 1, '2023-09-21', '16:00', '18:00', '班级足球赛', 1),
(3, 3, 1, '2023-09-22', '14:00', '16:00', '篮球训练', 1),
(1, 1, 2, '2023-09-23', '10:00', '12:00', '教师活动', 1),
(4, 2, 2, '2023-09-24', '14:00', '16:00', '网球课', 1); 