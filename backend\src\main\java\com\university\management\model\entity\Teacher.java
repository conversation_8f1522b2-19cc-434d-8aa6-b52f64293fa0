package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 教师实体类
 */
@Entity
@Table(name = "teacher")
@TableName("teacher")
@ApiModel(value = "教师实体", description = "教师信息")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Teacher extends BaseEntity {

    /**
     * 教师编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("教师编号")
    private String teacherNo;

    /**
     * 姓名
     */
    @Column(nullable = false, length = 20)
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别（0-女，1-男）
     */
    @Column
    @ApiModelProperty("性别（0-女，1-男）")
    private Integer gender;

    /**
     * 出生日期
     */
    @Column
    @ApiModelProperty("出生日期")
    private LocalDate birthday;

    /**
     * 年龄
     */
    @Column
    @ApiModelProperty("年龄")
    private Integer age;

    /**
     * 身份证号
     */
    @Column(length = 18)
    @ApiModelProperty("身份证号")
    private String idCard;

    /**
     * 手机号
     */
    @Column(length = 11)
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 邮箱
     */
    @Column(length = 50)
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 家庭住址
     */
    @Column(length = 200)
    @ApiModelProperty("家庭住址")
    private String address;

    /**
     * 院系ID
     */
    @Column(name = "department_id")
    @ApiModelProperty("院系ID")
    private Integer departmentId;

    /**
     * 学院ID
     */
    @Column(name = "college_id")
    @ApiModelProperty("学院ID")
    private Integer collegeId;

    /**
     * 职称（0-助教，1-讲师，2-副教授，3-教授）
     */
    @Column
    @ApiModelProperty("职称（0-助教，1-讲师，2-副教授，3-教授）")
    private Integer title;

    /**
     * 学历（0-学士，1-硕士，2-博士）
     */
    @Column
    @ApiModelProperty("学历（0-学士，1-硕士，2-博士）")
    private Integer education;

    /**
     * 入职日期
     */
    @Column
    @ApiModelProperty("入职日期")
    private LocalDate hireDate;

    /**
     * 状态（0-在职，1-离职，2-退休）
     */
    @Column
    @ApiModelProperty("状态（0-在职，1-离职，2-退休）")
    private Integer status;

    /**
     * 院系
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_id", insertable = false, updatable = false)
    @TableField(exist = false)
    @JsonIgnore
    private Department department;

    /**
     * 班级列表
     */
    @OneToMany(mappedBy = "teacher", fetch = FetchType.LAZY)
    @TableField(exist = false)
    @JsonIgnore
    private List<Class> classes = new ArrayList<>();

    /**
     * 课程列表
     */
    @OneToMany(mappedBy = "teacher", fetch = FetchType.LAZY)
    @TableField(exist = false)
    @JsonIgnore
    private List<Course> courses = new ArrayList<>();

    // Getter和Setter方法
    public String getTeacherNo() {
        return teacherNo;
    }

    public void setTeacherNo(String teacherNo) {
        this.teacherNo = teacherNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Integer collegeId) {
        this.collegeId = collegeId;
    }

    public Integer getTitle() {
        return title;
    }

    public void setTitle(Integer title) {
        this.title = title;
    }

    public Integer getEducation() {
        return education;
    }

    public void setEducation(Integer education) {
        this.education = education;
    }

    public LocalDate getHireDate() {
        return hireDate;
    }

    public void setHireDate(LocalDate hireDate) {
        this.hireDate = hireDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    public List<Class> getClasses() {
        return classes;
    }

    public void setClasses(List<Class> classes) {
        this.classes = classes;
    }

    public List<Course> getCourses() {
        return courses;
    }

    public void setCourses(List<Course> courses) {
        this.courses = courses;
    }
}