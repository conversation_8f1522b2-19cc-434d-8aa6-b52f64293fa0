<template>
  <div class="role-list-container">
    <div class="header">
      <h2>角色管理</h2>
      <el-button type="primary" @click="handleAdd">添加角色</el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="角色名称">
          <el-input v-model="searchForm.name" placeholder="请输入角色名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="角色编码">
          <el-input v-model="searchForm.code" placeholder="请输入角色编码" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table 
      v-loading="loading" 
      :data="roleList" 
      border 
      style="width: 100%" 
      row-key="id"
      @sort-change="handleSortChange">
      <el-table-column prop="name" label="角色名称" width="150"></el-table-column>
      <el-table-column prop="code" label="角色编码" width="150"></el-table-column>
      <el-table-column prop="sort" label="排序" width="80" sortable></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag 
            :type="scope.row.status === 1 ? 'success' : 'danger'" 
            effect="dark"
            size="small">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" sortable>
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150"></el-table-column>
      <el-table-column label="操作" fixed="right" width="280">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="success" @click="handlePermission(scope.row)">分配权限</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)">
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRolesByPage, deleteRole, updateRole } from '@/api/role'

export default {
  name: 'RoleList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const roleList = ref([])

    // 分页设置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortBy: 'id'
    })

    // 搜索表单
    const searchForm = reactive({
      name: '',
      code: ''
    })

    // 获取角色列表数据
    const fetchRoleData = async () => {
      loading.value = true
      try {
        console.log('📡 发送真实API请求: GET /api/roles/page')
        const res = await getRolesByPage(
          pagination.currentPage - 1,  // Spring Data JPA 分页从0开始
          pagination.pageSize,
          pagination.sortBy
        )

        console.log('📊 角色API响应数据:', res)
        if (res && res.data) {
          roleList.value = res.data.content || res.data
          pagination.total = res.data.totalElements || res.data.length || 0
          console.log('✅ 成功获取角色数据，共', pagination.total, '条记录')
          console.log('📋 角色列表数据:', roleList.value)
        } else {
          console.log('⚠️ 角色API返回空数据，使用模拟数据')
          throw new Error('API返回空数据')
        }
      } catch (error) {
        console.error('❌ 角色API请求失败，使用模拟数据:', error)

        // 使用模拟角色数据
        const mockRoles = [
          {
            id: 1,
            name: '超级管理员',
            code: 'SUPER_ADMIN',
            sort: 1,
            status: 1,
            createTime: '2023-01-01 10:00:00',
            remark: '系统最高权限管理员，拥有所有功能的访问权限'
          },
          {
            id: 2,
            name: '系统管理员',
            code: 'ADMIN',
            sort: 2,
            status: 1,
            createTime: '2023-01-01 10:05:00',
            remark: '系统管理员，负责用户管理、系统配置等功能'
          },
          {
            id: 3,
            name: '教师',
            code: 'TEACHER',
            sort: 3,
            status: 1,
            createTime: '2023-01-01 10:10:00',
            remark: '教师角色，可以管理课程、学生成绩、教学资源等'
          },
          {
            id: 4,
            name: '学生',
            code: 'STUDENT',
            sort: 4,
            status: 1,
            createTime: '2023-01-01 10:15:00',
            remark: '学生角色，可以查看课程信息、成绩、选课等'
          },
          {
            id: 5,
            name: '访客',
            code: 'GUEST',
            sort: 5,
            status: 0,
            createTime: '2023-01-01 10:20:00',
            remark: '访客角色，只能查看公开信息，权限受限'
          },
          {
            id: 6,
            name: '教务管理员',
            code: 'ACADEMIC_ADMIN',
            sort: 6,
            status: 1,
            createTime: '2023-02-01 14:30:00',
            remark: '教务管理员，负责课程安排、考试管理、学籍管理等'
          }
        ]

        // 模拟分页数据
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize
        const endIndex = startIndex + pagination.pageSize
        roleList.value = mockRoles.slice(startIndex, endIndex)
        pagination.total = mockRoles.length

        console.log('🔄 使用模拟角色数据，共', mockRoles.length, '条记录，当前显示第', pagination.currentPage, '页')
        ElMessage.warning('角色API不可用，使用模拟数据')
      } finally {
        loading.value = false
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchRoleData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      pagination.currentPage = 1
      fetchRoleData()
    }

    // 处理排序变化
    const handleSortChange = (column) => {
      if (column.prop && column.order) {
        pagination.sortBy = column.prop + (column.order === 'ascending' ? ',asc' : ',desc')
      } else {
        pagination.sortBy = 'id'
      }
      fetchRoleData()
    }

    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      fetchRoleData()
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchRoleData()
    }

    // 添加角色
    const handleAdd = () => {
      router.push('/roles/add')
    }

    // 编辑角色
    const handleEdit = (row) => {
      router.push(`/roles/edit/${row.id}`)
    }

    // 分配权限
    const handlePermission = (row) => {
      router.push(`/roles/${row.id}/permissions`)
    }

    // 切换角色状态
    const handleToggleStatus = (row) => {
      const statusText = row.status === 1 ? '禁用' : '启用'
      ElMessageBox.confirm(`确定要${statusText}角色 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const updatedRole = { ...row, status: row.status === 1 ? 0 : 1 }
          await updateRole(row.id, updatedRole)
          ElMessage.success(`${statusText}成功`)
          fetchRoleData()
        } catch (error) {
          console.error(`${statusText}角色失败:`, error)
          ElMessage.error(`${statusText}角色失败`)
        }
      }).catch(() => {
        // 取消操作，不做任何处理
      })
    }

    // 删除角色
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除角色 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteRole(row.id)
          ElMessage.success('删除成功')
          fetchRoleData()
        } catch (error) {
          console.error('删除角色失败:', error)
          ElMessage.error('删除角色失败')
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    }

    // 日期格式化
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }

    // 组件挂载后初始化
    onMounted(() => {
      fetchRoleData()
    })

    return {
      loading,
      roleList,
      pagination,
      searchForm,
      formatDate,
      fetchRoleData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleEdit,
      handlePermission,
      handleToggleStatus,
      handleDelete
    }
  }
}
</script>

<style scoped>
.role-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style> 