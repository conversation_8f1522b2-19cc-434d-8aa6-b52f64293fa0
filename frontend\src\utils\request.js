import axios from 'axios'
import { ElMessage } from 'element-plus'
import { mockConfig, isMockEnabled } from '@/config/mock.js'
import { mockApi } from '@/mock/api.js'

/**
 * 📡 HTTP请求工具 - 真实数据模式
 *
 * 注意：模拟数据功能已被完全禁用
 * 所有API请求都将发送到真实的后端服务
 * 后端地址：http://localhost:8083
 */

// 模拟数据路由匹配
const mockRoutes = {
  // 学生管理
  'GET /api/students': () => mockApi.students.getAll(),
  'GET /api/students/page': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.students.getByPage(
      parseInt(params.get('page')) || 0,
      parseInt(params.get('size')) || 10
    )
  },
  'GET /api/students/search': (config) => {
    const urlParts = config.url.split('?')
    const params = urlParts.length > 1 ? new URLSearchParams(urlParts[1]) : new URLSearchParams()
    return mockApi.students.search({
      name: params.get('name'),
      major: params.get('major'),
      grade: params.get('grade'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },
  'GET /api/students/:id': (config) => {
    const id = config.url.match(/\/api\/students\/(\d+)/)?.[1]
    return mockApi.students.getById(id)
  },
  'POST /api/students': (config) => mockApi.students.create(JSON.parse(config.data)),
  'PUT /api/students/:id': (config) => {
    const id = config.url.match(/\/api\/students\/(\d+)/)?.[1]
    return mockApi.students.update(id, JSON.parse(config.data))
  },
  'DELETE /api/students/:id': (config) => {
    const id = config.url.match(/\/api\/students\/(\d+)/)?.[1]
    return mockApi.students.delete(id)
  },

  // 教师管理
  'GET /api/teachers': () => mockApi.teachers.getAll(),
  'GET /api/teachers/page': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.teachers.getByPage(
      parseInt(params.get('page')) || 0,
      parseInt(params.get('size')) || 10
    )
  },
  'GET /api/teachers/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.teachers.search({
      name: params.get('name'),
      department: params.get('department'),
      title: params.get('title'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 课程管理
  'GET /api/courses': () => mockApi.courses.getAll(),
  'GET /api/courses/page': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.courses.getByPage(
      parseInt(params.get('page')) || 0,
      parseInt(params.get('size')) || 10
    )
  },
  'GET /api/courses/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.courses.search({
      name: params.get('name'),
      department: params.get('department'),
      type: params.get('type'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 院系管理
  'GET /api/departments': () => mockApi.departments.getAll(),
  'GET /api/departments/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.departments.search({
      name: params.get('name'),
      code: params.get('code'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 专业管理
  'GET /api/majors': () => mockApi.majors.getAll(),
  'GET /api/majors/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.majors.search({
      name: params.get('name'),
      code: params.get('code'),
      departmentId: params.get('departmentId'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 班级管理
  'GET /api/classes': () => mockApi.classes.getAll(),
  'GET /api/classes/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.classes.search({
      name: params.get('name'),
      grade: params.get('grade'),
      majorId: params.get('majorId'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 图书管理
  'GET /api/books': () => mockApi.books.getAll(),
  'GET /api/books/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.books.search({
      title: params.get('title'),
      author: params.get('author'),
      category: params.get('category'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 借阅记录
  'GET /api/borrowing-records': () => mockApi.borrowingRecords.getAll(),
  'GET /api/borrowing-records/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.borrowingRecords.search({
      studentName: params.get('studentName'),
      bookName: params.get('bookName'),
      status: params.get('status'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 教室管理
  'GET /api/classrooms': () => mockApi.classrooms.getAll(),
  'GET /api/classrooms/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.classrooms.search({
      roomNo: params.get('roomNo'),
      building: params.get('building'),
      type: params.get('type'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 宿舍管理
  'GET /api/dormitories': () => mockApi.dormitories.getAll(),
  'GET /api/dormitories/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.dormitories.search({
      dormitoryNo: params.get('dormitoryNo'),
      building: params.get('building'),
      type: params.get('type'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  },

  // 体育场馆
  'GET /api/sports-venues': () => mockApi.sportsVenues.getAll(),
  'GET /api/sports-venues/search': (config) => {
    const params = new URLSearchParams(config.url.split('?')[1])
    return mockApi.sportsVenues.search({
      name: params.get('name'),
      type: params.get('type'),
      location: params.get('location'),
      page: parseInt(params.get('page')) || 0,
      size: parseInt(params.get('size')) || 10
    })
  }
}

// 匹配模拟路由
const matchMockRoute = (method, url) => {
  const key = `${method.toUpperCase()} ${url.split('?')[0]}`

  // 精确匹配
  if (mockRoutes[key]) {
    return mockRoutes[key]
  }

  // 参数匹配（如 /api/students/:id）
  for (const route in mockRoutes) {
    const [routeMethod, routePath] = route.split(' ')
    if (routeMethod === method.toUpperCase()) {
      const routeRegex = routePath.replace(/:(\w+)/g, '(\\d+)')
      const regex = new RegExp(`^${routeRegex}$`)
      if (regex.test(url.split('?')[0])) {
        return mockRoutes[route]
      }
    }
  }

  return null
}

// 创建axios实例
const service = axios.create({
  baseURL: '', // API的基础URL已在代理中设置
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  async config => {
    // 强制禁用模拟数据检查，确保所有请求都发送到真实API
    // 模拟数据逻辑已被完全禁用

    if (mockConfig.showTips && import.meta.env.DEV) {
      console.log(`📡 发送真实API请求: ${config.method.toUpperCase()} ${config.url}`)
    }

    // 在请求发送前做一些处理
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    // 请求错误处理
    console.log(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果响应的状态码为2xx系列，走这里的代码
    const res = response.data

    // 处理模拟数据响应
    if (res && typeof res === 'object' && 'success' in res && 'data' in res) {
      if (res.success === false) {
        ElMessage({
          message: res.message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(res.message || 'Error'))
      }
      return res.data
    }

    // 这里可以根据后端返回的状态码进行不同的处理
    // 支持两种响应格式：{ success: boolean } 和 { code: string }
    const isSuccess = res.success === true || res.code === '000000'

    if (!isSuccess) {
      ElMessage({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 这里可以处理一些特定状态码的错误
      // 例如：如果返回401，则可能是没有认证或token过期了
      if (res.code === '000401') {
        // 重定向到登录页面
        localStorage.removeItem('token')
        window.location.href = '/login'
      }

      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res.data
    }
  },
  error => {
    // 模拟响应处理逻辑已被移除，只处理真实API错误

    // 如果响应的状态码不是2xx系列，走这里的代码
    console.log('API请求错误:', error)
    let message = error.message || '请求失败'

    // 尝试从错误响应中获取更详细的错误信息
    if (error.response && error.response.data) {
      const errorData = error.response.data
      if (errorData.message) {
        message = errorData.message
      }
    }

    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service 