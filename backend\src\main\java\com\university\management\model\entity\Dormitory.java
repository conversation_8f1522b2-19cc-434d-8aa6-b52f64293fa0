package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 宿舍实体类
 */
@Data
@Entity
@Table(name = "dormitory")
@TableName("dormitory")
@ApiModel(value = "宿舍实体", description = "宿舍信息")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Dormitory extends BaseEntity {

    /**
     * 宿舍编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("宿舍编号")
    private String dormitoryNo;

    /**
     * 宿舍楼
     */
    @Column(length = 50)
    @ApiModelProperty("宿舍楼")
    private String building;

    /**
     * 楼层
     */
    @ApiModelProperty("楼层")
    private Integer floor;

    /**
     * 房间号
     */
    @Column(length = 10)
    @ApiModelProperty("房间号")
    private String roomNo;

    /**
     * 类型(0-男生宿舍，1-女生宿舍)
     */
    @ApiModelProperty("类型(0-男生宿舍，1-女生宿舍)")
    private Integer type;

    /**
     * 可住人数
     */
    @ApiModelProperty("可住人数")
    private Integer capacity;

    /**
     * 已住人数
     */
    @ApiModelProperty("已住人数")
    private Integer occupied;

    /**
     * 管理员ID
     */
    @ApiModelProperty("管理员ID")
    private Integer managerId;

    /**
     * 宿舍费用
     */
    @ApiModelProperty("宿舍费用")
    private Double fee;

    /**
     * 设备描述
     */
    @Column(length = 500)
    @ApiModelProperty("设备描述")
    private String equipment;

    /**
     * 宿舍状态(0-正常，1-维修中，2-已废弃)
     */
    @ApiModelProperty("宿舍状态(0-正常，1-维修中，2-已废弃)")
    private Integer status;
} 