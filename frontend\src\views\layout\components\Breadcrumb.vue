<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span
          v-if="index === levelList.length - 1 || item.redirect === 'noRedirect'"
          class="no-redirect"
        >{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'Breadcrumb',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const levelList = ref([])

    // 生成面包屑导航
    const getBreadcrumb = () => {
      // 只显示带有meta.title的路由
      let matched = route.matched.filter(item => item.meta && item.meta.title)
      
      // 如果第一个不是首页，则添加首页到面包屑
      const first = matched[0]
      if (first && first.path !== '/dashboard') {
        matched = [
          {
            path: '/dashboard',
            meta: { title: '首页' }
          }
        ].concat(matched)
      }
      
      levelList.value = matched
    }

    // 处理链接点击
    const handleLink = (item) => {
      const { redirect, path } = item
      if (redirect) {
        router.push(redirect)
        return
      }
      router.push(path)
    }

    // 监听路由变化时更新面包屑
    watch(
      () => route.path,
      () => {
        getBreadcrumb()
      },
      { immediate: true }
    )

    return {
      levelList,
      handleLink
    }
  }
}
</script>

<style scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
}

.app-breadcrumb .no-redirect {
  color: #97a8be;
  cursor: text;
}

/* 面包屑过渡动画 */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
</style> 