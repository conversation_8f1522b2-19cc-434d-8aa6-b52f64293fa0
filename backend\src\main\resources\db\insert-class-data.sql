-- 插入班级数据
USE university_management;

-- 插入班级数据（使用正确的major_id, department_id, teacher_id）
INSERT INTO class (class_no, name, major_id, department_id, grade, class_size, teacher_id, create_time, update_time, is_deleted) VALUES
-- Computer Science College Classes (department_id = 529)
-- Computer Science and Technology Major (major_id = 1058)
('CS2020-1', 'Computer Science 2020 Class 1', 1058, 529, 2020, 25, 889, NOW(), NOW(), 0),
('CS2020-2', 'Computer Science 2020 Class 2', 1058, 529, 2020, 25, 890, NOW(), NOW(), 0),
('CS2021-1', 'Computer Science 2021 Class 1', 1058, 529, 2021, 30, 891, NOW(), NOW(), 0),

-- Software Engineering Major (major_id = 1059)
('SE2020-1', 'Software Engineering 2020 Class 1', 1059, 529, 2020, 28, 892, NOW(), NOW(), 0),
('SE2021-1', 'Software Engineering 2021 Class 1', 1059, 529, 2021, 32, 893, NOW(), NOW(), 0),

-- Network Engineering Major (major_id = 1060)
('NE2020-1', 'Network Engineering 2020 Class 1', 1060, 529, 2020, 26, 894, NOW(), NOW(), 0),
('NE2021-1', 'Network Engineering 2021 Class 1', 1060, 529, 2021, 28, 895, NOW(), NOW(), 0),

-- AI Major (major_id = 1061)
('AI2021-1', 'Artificial Intelligence 2021 Class 1', 1061, 529, 2021, 30, 896, NOW(), NOW(), 0),

-- Data Science Major (major_id = 1062)
('DS2021-1', 'Data Science 2021 Class 1', 1062, 529, 2021, 25, 889, NOW(), NOW(), 0),

-- Electronic Engineering College Classes (department_id = 530)
-- Electronic Information Engineering Major (major_id = 1063)
('EE2021-1', 'Electronic Engineering 2021 Class 1', 1063, 530, 2021, 30, 897, NOW(), NOW(), 0),

-- Communication Engineering Major (major_id = 1064)
('TC2021-1', 'Communication Engineering 2021 Class 1', 1064, 530, 2021, 28, 898, NOW(), NOW(), 0),

-- Automation Major (major_id = 1065)
('AU2021-1', 'Automation 2021 Class 1', 1065, 530, 2021, 26, 899, NOW(), NOW(), 0),

-- Mechanical Engineering College Classes (department_id = 531)
-- Mechanical Design Manufacturing and Automation Major (major_id = 1066)
('ME2021-1', 'Mechanical Engineering 2021 Class 1', 1066, 531, 2021, 32, 902, NOW(), NOW(), 0),

-- Mechatronics Engineering Major (major_id = 1067)
('MT2021-1', 'Mechatronics Engineering 2021 Class 1', 1067, 531, 2021, 28, 903, NOW(), NOW(), 0),

-- Vehicle Engineering Major (major_id = 1068)
('VE2021-1', 'Vehicle Engineering 2021 Class 1', 1068, 531, 2021, 25, 904, NOW(), NOW(), 0),

-- Economics and Management College Classes (department_id = 532)
-- Business Administration Major (major_id = 1069)
('BM2021-1', 'Business Administration 2021 Class 1', 1069, 532, 2021, 35, 905, NOW(), NOW(), 0),

-- Accounting Major (major_id = 1070)
('AC2021-1', 'Accounting 2021 Class 1', 1070, 532, 2021, 40, 906, NOW(), NOW(), 0),

-- Marketing Major (major_id = 1071)
('MK2021-1', 'Marketing 2021 Class 1', 1071, 532, 2021, 30, 907, NOW(), NOW(), 0),

-- International Economics and Trade Major (major_id = 1072)
('IT2021-1', 'International Trade 2021 Class 1', 1072, 532, 2021, 28, 908, NOW(), NOW(), 0),

-- Foreign Languages College Classes (department_id = 533)
-- English Major (major_id = 1073)
('EN2021-1', 'English 2021 Class 1', 1073, 533, 2021, 25, 909, NOW(), NOW(), 0),

-- Japanese Major (major_id = 1074)
('JP2021-1', 'Japanese 2021 Class 1', 1074, 533, 2021, 20, 910, NOW(), NOW(), 0),

-- Art and Design College Classes (department_id = 534)
-- Visual Communication Design Major (major_id = 1075)
('VD2021-1', 'Visual Design 2021 Class 1', 1075, 534, 2021, 22, 913, NOW(), NOW(), 0),

-- Environmental Design Major (major_id = 1076)
('ED2021-1', 'Environmental Design 2021 Class 1', 1076, 534, 2021, 20, 914, NOW(), NOW(), 0);
