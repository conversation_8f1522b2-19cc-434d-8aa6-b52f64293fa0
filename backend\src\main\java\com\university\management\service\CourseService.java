package com.university.management.service;

import com.university.management.model.entity.Course;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface CourseService {
    
    Course createCourse(Course course);
    
    Course updateCourse(Integer id, Course courseDetails);

    void deleteCourse(Integer id);

    Optional<Course> findById(Integer id);
    
    Optional<Course> findByCourseNo(String courseCode);
    
    List<Course> findAll();
    
    Page<Course> findAll(Pageable pageable);
    
    Page<Course> findByConditions(String name, String department, Integer credits, Pageable pageable);
    
    List<Course> findByTeacherId(Integer teacherId);
    
    boolean existsByCourseNo(String courseCode);
    
    Map<String, Long> getCourseStatsByDepartment();

    Map<Integer, Long> getCourseStatsByCredits();

    Map<String, Long> getCourseStatsBySemester();
}