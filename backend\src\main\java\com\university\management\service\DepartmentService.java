package com.university.management.service;

import com.university.management.model.entity.Department;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 部门服务接口
 */
public interface DepartmentService {

    /**
     * 创建部门
     *
     * @param department 部门信息
     * @return 创建后的部门
     */
    Department createDepartment(Department department);

    /**
     * 更新部门
     *
     * @param id 部门ID
     * @param departmentDetails 部门详情
     * @return 更新后的部门
     */
    Department updateDepartment(Integer id, Department departmentDetails);

    /**
     * 删除部门
     *
     * @param id 部门ID
     */
    void deleteDepartment(Integer id);

    /**
     * 根据ID查询部门
     *
     * @param id 部门ID
     * @return 部门信息
     */
    Optional<Department> findById(Integer id);

    /**
     * 根据部门编号查询部门
     *
     * @param departmentNo 部门编号
     * @return 部门信息
     */
    Optional<Department> findByDepartmentNo(String departmentNo);

    /**
     * 查询所有部门
     *
     * @return 部门列表
     */
    List<Department> findAll();

    /**
     * 分页查询部门
     *
     * @param pageable 分页参数
     * @return 部门分页结果
     */
    Page<Department> findAll(Pageable pageable);

    /**
     * 条件查询部门
     *
     * @param name 部门名称
     * @param dean 院长姓名
     * @param pageable 分页参数
     * @return 部门分页结果
     */
    Page<Department> findByConditions(String name, String dean, Pageable pageable);
} 