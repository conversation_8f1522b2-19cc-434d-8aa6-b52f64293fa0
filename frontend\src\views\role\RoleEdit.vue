<template>
  <div class="role-edit-container">
    <div class="header">
      <h2>{{ isEdit ? '编辑角色' : '添加角色' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="roleForm"
      :rules="rules"
      label-width="100px"
      class="role-form"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="roleForm.name" placeholder="请输入角色名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色编码" prop="code">
            <el-input v-model="roleForm.code" :disabled="isEdit" placeholder="请输入角色编码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="roleForm.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="roleForm.sort" :min="0" :max="999" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="roleForm.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getRoleById, createRole, updateRole } from '@/api/role'

export default {
  name: 'RoleEdit',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)

    // 判断是编辑还是新增
    const isEdit = computed(() => {
      return route.params.id !== undefined
    })

    // 表单数据
    const roleForm = reactive({
      id: null,
      name: '',
      code: '',
      status: 1,
      sort: 0,
      remark: ''
    })

    // 表单验证规则
    const rules = reactive({
      name: [
        { required: true, message: '请输入角色名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入角色编码', trigger: 'blur' },
        { pattern: /^[A-Z_]+$/, message: '角色编码只能是大写字母和下划线', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ],
      sort: [
        { required: true, message: '请输入排序', trigger: 'blur' }
      ]
    })

    // 获取角色详情
    const fetchRoleDetail = async (id) => {
      loading.value = true
      try {
        const res = await getRoleById(id)
        if (res.data) {
          // 将后端返回的数据填充到表单中
          Object.assign(roleForm, res.data)
        }
      } catch (error) {
        console.error('获取角色详情失败:', error)
        ElMessage.error('获取角色详情失败')
      } finally {
        loading.value = false
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            if (isEdit.value) {
              // 编辑模式
              await updateRole(roleForm.id, roleForm)
              ElMessage.success('更新角色信息成功')
            } else {
              // 新增模式
              await createRole(roleForm)
              ElMessage.success('添加角色成功')
            }
            goBack()
          } catch (error) {
            console.error('保存角色信息失败:', error)
            ElMessage.error('保存角色信息失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 返回上一页
    const goBack = () => {
      router.push('/roles')
    }

    // 组件挂载时，如果是编辑模式则获取角色详情
    onMounted(() => {
      if (isEdit.value) {
        fetchRoleDetail(route.params.id)
      }
    })

    return {
      formRef,
      loading,
      isEdit,
      roleForm,
      rules,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.role-edit-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.role-form {
  max-width: 1000px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 