import request from '@/utils/request'

const baseUrl = '/api/borrowing-records'

// 获取所有借阅记录
export function getAllBorrowingRecords() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取借阅记录
export function getBorrowingRecordsByPage(page = 0, size = 10, sortBy = 'borrowDate') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询借阅记录
export function searchBorrowingRecords({ studentId, bookId, status, borrowDateStart, borrowDateEnd, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      studentId,
      bookId,
      status,
      borrowDateStart,
      borrowDateEnd,
      page,
      size
    }
  })
}

// 根据ID获取借阅记录
export function getBorrowingRecordById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建借阅记录（借书）
export function createBorrowingRecord(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新借阅记录
export function updateBorrowingRecord(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 归还图书
export function returnBook(id, data) {
  return request({
    url: `${baseUrl}/${id}/return`,
    method: 'put',
    data
  })
}

// 续借图书
export function renewBook(id, data) {
  return request({
    url: `${baseUrl}/${id}/renew`,
    method: 'put',
    data
  })
}

// 删除借阅记录
export function deleteBorrowingRecord(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 根据学生ID获取借阅记录
export function getBorrowingRecordsByStudent(studentId) {
  return request({
    url: `${baseUrl}/student/${studentId}`,
    method: 'get'
  })
}

// 根据图书ID获取借阅记录
export function getBorrowingRecordsByBook(bookId) {
  return request({
    url: `${baseUrl}/book/${bookId}`,
    method: 'get'
  })
}

// 获取借阅统计信息
export function getBorrowingStats() {
  return request({
    url: `${baseUrl}/stats`,
    method: 'get'
  })
}

// 获取逾期记录
export function getOverdueRecords() {
  return request({
    url: `${baseUrl}/overdue`,
    method: 'get'
  })
}
