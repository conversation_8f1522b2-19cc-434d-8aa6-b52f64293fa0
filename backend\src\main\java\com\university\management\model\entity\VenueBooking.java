package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;

/**
 * 场馆预约实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "VenueBooking", description = "场馆预约信息")
@Entity
@TableName("venue_booking")
public class VenueBooking extends BaseEntity {

    @ApiModelProperty(value = "场馆ID")
    @Column(name = "venue_id", nullable = false)
    private Integer venueId;

    @ApiModelProperty(value = "用户ID")
    @Column(nullable = false)
    private Integer userId;

    @ApiModelProperty(value = "用户类型(0-学生，1-教师)")
    private Integer userType;

    @ApiModelProperty(value = "预约日期")
    @Column(nullable = false)
    private LocalDate bookingDate;

    @ApiModelProperty(value = "开始时间")
    @Column(nullable = false, length = 10)
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @Column(nullable = false, length = 10)
    private String endTime;

    @ApiModelProperty(value = "用途")
    @Column(length = 200)
    private String purpose;

    @ApiModelProperty(value = "状态(0-待审核，1-已批准，2-已拒绝，3-已取消)")
    private Integer status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "venue_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private SportsVenue venue;
} 