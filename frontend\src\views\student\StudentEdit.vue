<template>
  <div class="student-edit-container">
    <div class="header">
      <h2>{{ isEdit ? '编辑学生' : '添加学生' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="studentForm"
      :rules="rules"
      label-width="100px"
      class="student-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学号" prop="studentNumber">
            <el-input v-model="studentForm.studentNumber" placeholder="请输入学号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="studentForm.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="studentForm.gender">
              <el-radio label="MALE">男</el-radio>
              <el-radio label="FEMALE">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年龄" prop="age">
            <el-input-number v-model="studentForm.age" :min="16" :max="50" placeholder="请输入年龄"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出生日期" prop="birthdate">
            <el-date-picker
              v-model="studentForm.birthdate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入学日期" prop="enrollmentDate">
            <el-date-picker
              v-model="studentForm.enrollmentDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年级" prop="grade">
            <el-select v-model="studentForm.grade" placeholder="请选择年级" style="width: 100%">
              <el-option
                v-for="year in currentYear - 2016 + 1"
                :key="year + 2016"
                :label="year + 2016"
                :value="year + 2016"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业" prop="major">
            <el-input v-model="studentForm.major" placeholder="请输入专业"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="studentForm.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="studentForm.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="地址" prop="address">
        <el-input
          v-model="studentForm.address"
          type="textarea"
          :rows="3"
          placeholder="请输入地址"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getStudentById, createStudent, updateStudent } from '@/api/student'

export default {
  name: 'StudentEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const currentYear = new Date().getFullYear()
    const isEdit = computed(() => route.params.id !== undefined)

    // 表单数据
    const studentForm = reactive({
      id: null,
      studentNumber: '',
      name: '',
      gender: 'MALE',
      age: null,
      birthdate: '',
      grade: null,
      major: '',
      phone: '',
      email: '',
      address: '',
      enrollmentDate: ''
    })

    // 表单验证规则
    const rules = reactive({
      studentNumber: [
        { required: true, message: '请输入学号', trigger: 'blur' },
        { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      age: [
        { required: true, message: '请输入年龄', trigger: 'blur' },
        { type: 'number', message: '年龄必须为数字', trigger: 'blur' }
      ],
      birthdate: [
        { required: true, message: '请选择出生日期', trigger: 'change' }
      ],
      grade: [
        { required: true, message: '请选择年级', trigger: 'change' }
      ],
      major: [
        { required: true, message: '请输入专业', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      enrollmentDate: [
        { required: true, message: '请选择入学日期', trigger: 'change' }
      ]
    })

    // 获取学生详情
    const getStudentDetail = async (id) => {
      try {
        const res = await getStudentById(id)
        if (res.data) {
          Object.assign(studentForm, res.data)
        }
      } catch (error) {
        console.error('获取学生详情失败:', error)
        ElMessage.error('获取学生详情失败')
        goBack()
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      await formRef.value.validate(async (valid, fields) => {
        if (valid) {
          try {
            if (isEdit.value) {
              await updateStudent(studentForm.id, studentForm)
              ElMessage.success('更新学生信息成功')
            } else {
              await createStudent(studentForm)
              ElMessage.success('添加学生成功')
            }
            goBack()
          } catch (error) {
            console.error(isEdit.value ? '更新学生信息失败:' : '添加学生失败:', error)
            ElMessage.error(isEdit.value ? '更新学生信息失败' : '添加学生失败')
          }
        } else {
          console.log('表单验证失败:', fields)
        }
      })
    }

    // 返回列表页
    const goBack = () => {
      router.push('/students')
    }

    onMounted(() => {
      if (isEdit.value && route.params.id) {
        getStudentDetail(route.params.id)
      }
    })

    return {
      formRef,
      studentForm,
      rules,
      currentYear,
      isEdit,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.student-edit-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.student-form {
  max-width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  padding: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 