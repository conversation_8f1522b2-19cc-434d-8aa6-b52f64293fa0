package com.university.management.controller;

import com.university.management.model.entity.Department;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * 部门控制器
 */
@Api(tags = "部门管理")
@RestController
@RequestMapping("/api/departments")
public class DepartmentController {

    private final DepartmentService departmentService;

    @Autowired
    public DepartmentController(DepartmentService departmentService) {
        this.departmentService = departmentService;
    }

    @ApiOperation("获取所有部门")
    @GetMapping
    public ApiResponse<List<Department>> getAllDepartments() {
        return ApiResponse.success(departmentService.findAll());
    }

    @ApiOperation("分页获取部门")
    @GetMapping("/page")
    public ApiResponse<Page<Department>> getDepartmentsPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "ASC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success(departmentService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询部门")
    @GetMapping("/search")
    public ApiResponse<Page<Department>> searchDepartments(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String dean,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(departmentService.findByConditions(name, dean, pageRequest));
    }

    @ApiOperation("获取部门详情")
    @GetMapping("/{id}")
    public ApiResponse<Department> getDepartmentById(@PathVariable Integer id) {
        Optional<Department> department = departmentService.findById(id);
        return department.map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.errorGeneric(404, "部门不存在"));
    }

    @ApiOperation("创建部门")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Department> createDepartment(@Valid @RequestBody Department department) {
        return ApiResponse.success("创建成功", departmentService.createDepartment(department));
    }

    @ApiOperation("更新部门")
    @PutMapping("/{id}")
    public ApiResponse<Department> updateDepartment(@PathVariable Integer id, @Valid @RequestBody Department departmentDetails) {
        return ApiResponse.success("更新成功", departmentService.updateDepartment(id, departmentDetails));
    }

    @ApiOperation("删除部门")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDepartment(@PathVariable Integer id) {
        departmentService.deleteDepartment(id);
        return ApiResponse.success();
    }
} 