package com.university.management.controller;

import com.university.management.model.entity.SportsVenue;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.SportsVenueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 体育场馆控制器
 */
@Api(tags = "体育场馆管理")
@RestController
@RequestMapping("/api/sports-venues")
public class SportsVenueController {

    private final SportsVenueService sportsVenueService;

    @Autowired
    public SportsVenueController(SportsVenueService sportsVenueService) {
        this.sportsVenueService = sportsVenueService;
    }

    @ApiOperation("获取所有体育场馆")
    @GetMapping
    public ApiResponse<List<SportsVenue>> getAllVenues() {
        return ApiResponse.success(sportsVenueService.findAll());
    }

    @ApiOperation("分页获取体育场馆")
    @GetMapping("/page")
    public ApiResponse<Page<SportsVenue>> getVenuesPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "ASC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success(sportsVenueService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询体育场馆")
    @GetMapping("/search")
    public ApiResponse<Page<SportsVenue>> searchVenues(
            @RequestParam(required = false) String venueName,
            @RequestParam(required = false) Integer venueType,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) Boolean isAvailable,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(sportsVenueService.findByConditions(venueName, venueType, location, isAvailable, pageRequest));
    }

    @ApiOperation("获取体育场馆详情")
    @GetMapping("/{id}")
    public ApiResponse<SportsVenue> getVenueById(@PathVariable Integer id) {
        Optional<SportsVenue> venue = sportsVenueService.findById(id);
        return venue.map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.errorGeneric(404, "体育场馆不存在"));
    }

    @ApiOperation("创建体育场馆")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<SportsVenue> createVenue(@Valid @RequestBody SportsVenue venue) {
        return ApiResponse.success("创建成功", sportsVenueService.createVenue(venue));
    }

    @ApiOperation("更新体育场馆")
    @PutMapping("/{id}")
    public ApiResponse<SportsVenue> updateVenue(@PathVariable Integer id, @Valid @RequestBody SportsVenue venueDetails) {
        return ApiResponse.success("更新成功", sportsVenueService.updateVenue(id, venueDetails));
    }

    @ApiOperation("删除体育场馆")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteVenue(@PathVariable Integer id) {
        sportsVenueService.deleteVenue(id);
        return ApiResponse.success();
    }

    @ApiOperation("获取体育场馆类型分布统计")
    @GetMapping("/stats/type")
    public ApiResponse<Map<Integer, Long>> getVenueStatsByType() {
        return ApiResponse.success(sportsVenueService.getVenueStatsByType());
    }

    @ApiOperation("获取可用体育场馆数量")
    @GetMapping("/stats/available")
    public ApiResponse<Long> getAvailableVenuesCount() {
        return ApiResponse.success(sportsVenueService.countAvailableVenues());
    }
} 