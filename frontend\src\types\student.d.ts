/**
 * 学生类型定义
 */
export interface Student {
  id: number;
  studentNo: string;
  name: string;
  gender: number;
  age?: number;
  birthday?: string;
  idCard?: string;
  phone?: string;
  email?: string;
  address?: string;
  classId?: number;
  majorId?: number;
  collegeId?: number;
  enrollYear?: number;
  status: number;
  dormitoryId?: number;
  createdTime?: string;
  updatedTime?: string;
}

/**
 * 学生查询参数
 */
export interface StudentQueryParams {
  name?: string;
  major?: string;
  grade?: number;
  page: number;
  size: number;
  sortBy?: string;
}

/**
 * 学生分页数据
 */
export interface StudentPageResult {
  content: Student[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

/**
 * 统计数据格式
 */
export interface MajorStats {
  [key: string]: number;
}

export interface GradeStats {
  [key: number]: number;
}

/**
 * 学生状态类型
 */
export enum StudentStatus {
  STUDYING = 0,  // 在读
  SUSPENDED = 1, // 休学
  DROPOUT = 2,   // 退学
  GRADUATED = 3  // 毕业
}

/**
 * 性别类型
 */
export enum Gender {
  FEMALE = 0,
  MALE = 1
} 