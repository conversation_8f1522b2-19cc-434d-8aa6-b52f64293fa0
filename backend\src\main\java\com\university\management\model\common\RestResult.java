package com.university.management.model.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * REST API 响应结果
 *
 * @param <T> 数据类型
 */
@ApiModel(value = "RestResult", description = "REST API响应结果")
public class RestResult<T> {

    @ApiModelProperty(value = "状态码")
    private String code;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "数据")
    private T data;

    /**
     * 无参构造函数
     */
    public RestResult() {
    }

    /**
     * 全参构造函数
     */
    public RestResult(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 构造函数
     *
     * @param code 状态码
     * @param message 消息
     */
    public RestResult(String code, String message) {
        this.code = code;
        this.message = message;
        this.data = null;
    }

    /**
     * 成功结果
     *
     * @param data 数据
     * @return RestResult
     */
    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, data);
    }

    /**
     * 成功结果
     *
     * @return RestResult
     */
    public static <T> RestResult<T> success() {
        return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, null);
    }

    /**
     * 失败结果
     *
     * @param code 状态码
     * @param message 消息
     * @return RestResult
     */
    public static <T> RestResult<T> error(String code, String message) {
        return new RestResult<>(code, message, null);
    }

    /**
     * 失败结果
     *
     * @param message 消息
     * @return RestResult
     */
    public static <T> RestResult<T> error(String message) {
        return new RestResult<>(ResultCodeConstant.CODE_000001, message, null);
    }
    
    // 手动添加getter和setter方法
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
} 