import request from '@/utils/request'

const baseUrl = '/api/courses'

// 获取所有课程信息
export function getAllCourses() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取课程信息
export function getCoursesByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询课程信息
export function searchCourses({ name, department, semester, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      department,
      semester,
      page,
      size
    }
  })
}

// 根据ID获取课程信息
export function getCourseById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建课程信息
export function createCourse(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新课程信息
export function updateCourse(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除课程信息
export function deleteCourse(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取课程按学分分布统计
export function getCourseStatsByCredit() {
  return request({
    url: `${baseUrl}/stats/credits`,
    method: 'get'
  })
}

// 获取课程按学期分布统计
export function getCourseStatsBySemester() {
  return request({
    url: `${baseUrl}/stats/semester`,
    method: 'get'
  })
}

// 获取课程按院系分布统计
export function getCourseStatsByDepartment() {
  return request({
    url: `${baseUrl}/stats/department`,
    method: 'get'
  })
} 