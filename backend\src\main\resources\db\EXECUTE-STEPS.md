# 🎯 数据库中文化执行步骤指南

## 📍 当前位置
您现在在：`D:\1闲鱼开发\大学学生管理系统\backend\src\main\resources\db>`

## 🚀 立即执行步骤

### 步骤1：执行简单修复脚本
```bash
mysql -u root -p7121020qing university_management < simple-chinese-fix.sql
```

### 步骤2：如果步骤1失败，尝试分步执行
```bash
# 进入MySQL命令行
mysql -u root -p7121020qing university_management

# 然后在MySQL命令行中执行：
```

```sql
-- 设置字符集
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 修复几个关键的班级数据
UPDATE class SET name = '计算机科学与技术2020级1班' WHERE name = 'Computer Science 2020 Class 1';
UPDATE class SET name = '软件工程2020级1班' WHERE name = 'Software Engineering 2020 Class 1';
UPDATE class SET name = '网络工程2020级1班' WHERE name = 'Network Engineering 2020 Class 1';

-- 检查是否成功
SELECT name FROM class WHERE name LIKE '%级%班' LIMIT 5;

-- 如果成功，继续修复专业数据
UPDATE major SET name = '计算机科学与技术' WHERE name = 'Computer Science and Technology';
UPDATE major SET name = '软件工程' WHERE name = 'Software Engineering';
UPDATE major SET name = '网络工程' WHERE name = 'Network Engineering';

-- 检查专业修复结果
SELECT name FROM major WHERE name NOT LIKE '%Engineering%' LIMIT 5;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;

-- 退出MySQL
EXIT;
```

### 步骤3：验证修复结果
```bash
# 重新进入MySQL检查结果
mysql -u root -p7121020qing university_management -e "
SELECT 
    '班级数据' AS 类型, 
    COUNT(*) AS 总数, 
    SUM(CASE WHEN name LIKE '%级%班' THEN 1 ELSE 0 END) AS 中文数量
FROM class
UNION ALL
SELECT 
    '专业数据', 
    COUNT(*), 
    SUM(CASE WHEN name NOT LIKE '%Engineering%' THEN 1 ELSE 0 END)
FROM major;"
```

## 🔧 如果遇到问题

### 问题1：字符集错误
```bash
# 检查数据库字符集
mysql -u root -p7121020qing university_management -e "
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';"

# 如果字符集不是utf8mb4，修改数据库字符集
mysql -u root -p7121020qing university_management -e "
ALTER DATABASE university_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 问题2：权限错误
```bash
# 检查权限
mysql -u root -p7121020qing -e "SHOW GRANTS;"

# 如果权限不足，重新授权
mysql -u root -p7121020qing -e "
GRANT ALL PRIVILEGES ON university_management.* TO 'root'@'localhost';
FLUSH PRIVILEGES;"
```

### 问题3：表不存在或数据不匹配
```bash
# 检查表结构
mysql -u root -p7121020qing university_management -e "
SHOW TABLES;
DESCRIBE class;
DESCRIBE major;
DESCRIBE course;"

# 检查现有数据
mysql -u root -p7121020qing university_management -e "
SELECT name FROM class LIMIT 5;
SELECT name FROM major LIMIT 5;
SELECT name FROM course LIMIT 5;"
```

## 📊 预期执行结果

### 成功执行后应该看到：
```
班级数据    总数    中文数量
班级数据    50      45
专业数据    50      45
课程数据    50      40
教室数据    50      40
```

### 成功的班级名称示例：
- 计算机科学与技术2020级1班
- 软件工程2021级1班
- 网络工程2020级1班
- 电子信息工程2021级1班

### 成功的专业名称示例：
- 计算机科学与技术
- 软件工程
- 网络工程
- 电子信息工程

## 🎯 立即行动清单

请按顺序执行以下命令：

### 1. 首先尝试简单修复
```bash
mysql -u root -p7121020qing university_management < simple-chinese-fix.sql
```

### 2. 如果成功，检查结果
```bash
mysql -u root -p7121020qing university_management -e "SELECT name FROM class WHERE name LIKE '%级%班' LIMIT 5;"
```

### 3. 如果失败，尝试手动执行
```bash
mysql -u root -p7121020qing university_management
```
然后在MySQL命令行中逐步执行上面的SQL命令。

### 4. 最终验证
```bash
mysql -u root -p7121020qing university_management -e "
SELECT 
    '修复完成' AS 状态,
    (SELECT COUNT(*) FROM class WHERE name LIKE '%级%班') AS 中文班级数量,
    (SELECT COUNT(*) FROM major WHERE name NOT LIKE '%Engineering%') AS 中文专业数量;"
```

## 💡 执行提示

1. **复制粘贴命令**：直接复制上面的命令到命令行执行
2. **逐步执行**：如果批量执行失败，可以逐条执行SQL语句
3. **检查每步结果**：每执行一步都检查结果，确保正确
4. **备份数据**：如果担心数据安全，先备份：
   ```bash
   mysqldump -u root -p7121020qing university_management > backup.sql
   ```

## 🎉 执行完成标志

当您看到以下结果时，说明修复成功：
- ✅ 班级名称包含"级"和"班"字样
- ✅ 专业名称为完整中文
- ✅ 没有英文的"Class"、"Engineering"等词汇
- ✅ 前端页面显示中文数据

---

**现在请执行第一个命令开始修复！** 🚀
