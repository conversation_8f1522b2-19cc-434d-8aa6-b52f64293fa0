-- 完整数据初始化脚本 - 第二部分
-- 图书、教室、宿舍、体育场馆等资源数据

USE university_management;

-- 8. 插入图书数据
INSERT IGNORE INTO book (isbn, title, author, publisher, publish_date, category, price, total, available, location, description, status) VALUES
-- 计算机类图书
('9787302123456', 'Java程序设计教程', '张伟', '清华大学出版社', '2023-01-01', 1, 59.80, 15, 12, 'A区1层001', 'Java编程语言的系统教程', 0),
('9787111234567', '数据结构与算法分析', '李娜', '机械工业出版社', '2023-02-01', 1, 68.00, 20, 18, 'A区1层002', '数据结构和算法的经典教材', 0),
('9787121345678', '计算机网络原理', '王强', '电子工业出版社', '2023-03-01', 1, 72.50, 12, 10, 'A区1层003', '计算机网络技术详解', 0),
('9787040456789', '操作系统概念', '刘敏', '高等教育出版社', '2023-04-01', 1, 85.00, 18, 15, 'A区1层004', '操作系统原理和实现', 0),
('9787115567890', '数据库系统概论', '陈杰', '人民邮电出版社', '2023-05-01', 1, 76.00, 16, 14, 'A区1层005', '数据库理论与实践', 0),
('9787302678901', '软件工程导论', '赵丽', '清华大学出版社', '2023-06-01', 1, 64.50, 14, 11, 'A区1层006', '软件开发方法论', 0),
('9787111789012', '人工智能基础', '孙明', '机械工业出版社', '2023-07-01', 1, 89.00, 10, 8, 'A区1层007', 'AI技术入门指南', 0),
('9787121890123', '机器学习实战', '周芳', '电子工业出版社', '2023-08-01', 1, 95.50, 8, 6, 'A区1层008', '机器学习算法与应用', 0),
('9787115901234', 'Python编程从入门到实践', '吴涛', '人民邮电出版社', '2023-09-01', 1, 79.00, 22, 19, 'A区1层009', 'Python编程实用教程', 0),
('9787302012345', 'Web前端开发技术', '郑华', '清华大学出版社', '2023-10-01', 1, 71.80, 13, 11, 'A区1层010', '前端开发技术详解', 0),

-- 电子信息类图书
('9787040123457', '电路分析基础', '田军', '高等教育出版社', '2023-01-15', 1, 82.00, 16, 14, 'A区2层001', '电路理论基础教程', 0),
('9787302234568', '模拟电子技术', '邓红', '清华大学出版社', '2023-02-15', 1, 75.50, 14, 12, 'A区2层002', '模拟电路设计与分析', 0),
('9787111345679', '数字电子技术', '韩斌', '机械工业出版社', '2023-03-15', 1, 78.00, 12, 10, 'A区2层003', '数字电路原理与应用', 0),
('9787121456780', '信号与系统', '曹敏', '电子工业出版社', '2023-04-15', 1, 86.50, 15, 13, 'A区2层004', '信号处理理论基础', 0),
('9787115567891', '通信原理', '蒋涛', '人民邮电出版社', '2023-05-15', 1, 92.00, 11, 9, 'A区2层005', '现代通信系统原理', 0),

-- 机械工程类图书
('9787040678902', '机械制图', '方军', '高等教育出版社', '2023-01-20', 1, 65.00, 20, 18, 'B区1层001', '机械图样绘制规范', 0),
('9787302789013', '理论力学', '石红', '清华大学出版社', '2023-02-20', 1, 88.50, 18, 16, 'B区1层002', '力学基础理论教程', 0),
('9787111890124', '材料力学', '袁斌', '机械工业出版社', '2023-03-20', 1, 91.00, 16, 14, 'B区1层003', '材料力学性能分析', 0),
('9787121901235', '机械设计', '方军', '电子工业出版社', '2023-04-20', 1, 96.50, 14, 12, 'B区1层004', '机械零件设计手册', 0),

-- 经济管理类图书
('9787115012346', '管理学原理', '龙丽', '人民邮电出版社', '2023-01-25', 4, 58.00, 25, 22, 'C区1层001', '现代管理理论基础', 0),
('9787040123458', '微观经济学', '段军', '高等教育出版社', '2023-02-25', 4, 72.50, 20, 18, 'C区1层002', '微观经济理论分析', 0),
('9787302234569', '宏观经济学', '史敏', '清华大学出版社', '2023-03-25', 4, 76.00, 18, 16, 'C区1层003', '宏观经济政策分析', 0),
('9787111345680', '会计学原理', '顾涛', '机械工业出版社', '2023-04-25', 4, 69.50, 22, 20, 'C区1层004', '会计基础理论与实务', 0),
('9787121456781', '财务管理', '史敏', '电子工业出版社', '2023-05-25', 4, 81.00, 16, 14, 'C区1层005', '企业财务决策分析', 0),

-- 外语类图书
('9787560567892', '综合英语教程', '夏华', '外语教学与研究出版社', '2023-01-30', 0, 45.50, 30, 27, 'D区1层001', '英语综合技能训练', 0),
('9787100678903', '英语语法大全', '侯斌', '商务印书馆', '2023-02-28', 0, 52.00, 25, 23, 'D区1层002', '英语语法系统讲解', 0),
('9787107789014', '标准日本语', '谭丽', '人民教育出版社', '2023-03-30', 0, 48.80, 20, 18, 'D区1层003', '日语入门教程', 0),

-- 艺术设计类图书
('9787550890125', '设计基础', '潘军', '中国美术学院出版社', '2023-01-10', 3, 128.00, 12, 10, 'E区1层001', '设计理论与实践基础', 0),
('9787302901236', '色彩构成', '崔敏', '清华大学出版社', '2023-02-10', 3, 98.50, 15, 13, 'E区1层002', '色彩理论与应用', 0);

-- 9. 插入教室数据
INSERT IGNORE INTO classroom (room_no, name, building, floor, capacity, type, facilities, status, description) VALUES
-- A楼教室（计算机学院）
('A101', 'A101多媒体教室', 'A楼', 1, 60, '多媒体教室', '投影仪,音响,空调,网络', 0, '配备现代化多媒体设备的标准教室'),
('A102', 'A102多媒体教室', 'A楼', 1, 60, '多媒体教室', '投影仪,音响,空调,网络', 0, '配备现代化多媒体设备的标准教室'),
('A103', 'A103多媒体教室', 'A楼', 1, 60, '多媒体教室', '投影仪,音响,空调,网络', 0, '配备现代化多媒体设备的标准教室'),
('A201', 'A201计算机实验室', 'A楼', 2, 50, '实验室', '计算机,投影仪,空调,网络', 0, '计算机编程实验专用教室'),
('A202', 'A202计算机实验室', 'A楼', 2, 50, '实验室', '计算机,投影仪,空调,网络', 0, '计算机编程实验专用教室'),
('A203', 'A203网络实验室', 'A楼', 2, 40, '实验室', '网络设备,计算机,投影仪,空调', 0, '网络工程实验专用教室'),
('A301', 'A301软件工程实验室', 'A楼', 3, 45, '实验室', '高配置计算机,投影仪,空调,网络', 0, '软件开发实验专用教室'),
('A302', 'A302人工智能实验室', 'A楼', 3, 40, '实验室', 'GPU服务器,计算机,投影仪,空调', 0, '人工智能算法实验室'),

-- B楼教室（电子信息学院）
('B101', 'B101多媒体教室', 'B楼', 1, 65, '多媒体教室', '投影仪,音响,空调,网络', 0, '电子信息学院标准教室'),
('B102', 'B102多媒体教室', 'B楼', 1, 65, '多媒体教室', '投影仪,音响,空调,网络', 0, '电子信息学院标准教室'),
('B201', 'B201电路实验室', 'B楼', 2, 30, '实验室', '实验台,示波器,信号发生器,万用表', 0, '电路分析实验专用教室'),
('B202', 'B202电子技术实验室', 'B楼', 2, 30, '实验室', '实验台,示波器,信号发生器,万用表', 0, '模拟数字电路实验室'),
('B203', 'B203通信实验室', 'B楼', 2, 25, '实验室', '通信设备,频谱分析仪,计算机', 0, '通信原理实验专用教室'),
('B301', 'B301自动化实验室', 'B楼', 3, 35, '实验室', 'PLC设备,工控机,传感器,执行器', 0, '自动控制实验专用教室'),

-- C楼教室（机械工程学院）
('C101', 'C101多媒体教室', 'C楼', 1, 70, '多媒体教室', '投影仪,音响,空调,网络', 0, '机械工程学院大型教室'),
('C102', 'C102多媒体教室', 'C楼', 1, 70, '多媒体教室', '投影仪,音响,空调,网络', 0, '机械工程学院大型教室'),
('C201', 'C201机械制图室', 'C楼', 2, 50, '制图室', '制图桌,投影仪,模型展示柜', 0, '机械制图专用教室'),
('C202', 'C202CAD实验室', 'C楼', 2, 40, '实验室', '高配置计算机,CAD软件,投影仪', 0, '计算机辅助设计实验室'),
('C301', 'C301材料力学实验室', 'C楼', 3, 25, '实验室', '万能试验机,硬度计,冲击试验机', 0, '材料力学性能测试实验室'),

-- D楼教室（经济管理学院）
('D101', 'D101阶梯教室', 'D楼', 1, 120, '阶梯教室', '投影仪,音响,空调,网络,话筒', 0, '大型阶梯教室，适合大班授课'),
('D102', 'D102阶梯教室', 'D楼', 1, 120, '阶梯教室', '投影仪,音响,空调,网络,话筒', 0, '大型阶梯教室，适合大班授课'),
('D201', 'D201多媒体教室', 'D楼', 2, 80, '多媒体教室', '投影仪,音响,空调,网络', 0, '经济管理学院标准教室'),
('D202', 'D202多媒体教室', 'D楼', 2, 80, '多媒体教室', '投影仪,音响,空调,网络', 0, '经济管理学院标准教室'),
('D301', 'D301案例讨论室', 'D楼', 3, 40, '讨论室', '圆桌,投影仪,白板,空调', 0, '小组讨论和案例分析专用教室'),
('D302', 'D302模拟实训室', 'D楼', 3, 35, '实训室', '计算机,投影仪,模拟软件,空调', 0, '经济管理模拟实训教室'),

-- E楼教室（外国语学院）
('E101', 'E101语音教室', 'E楼', 1, 50, '语音教室', '语音设备,耳机,投影仪,空调', 0, '外语听说训练专用教室'),
('E102', 'E102语音教室', 'E楼', 1, 50, '语音教室', '语音设备,耳机,投影仪,空调', 0, '外语听说训练专用教室'),
('E201', 'E201多媒体教室', 'E楼', 2, 45, '多媒体教室', '投影仪,音响,空调,网络', 0, '外国语学院标准教室'),
('E202', 'E202多媒体教室', 'E楼', 2, 45, '多媒体教室', '投影仪,音响,空调,网络', 0, '外国语学院标准教室'),

-- F楼教室（艺术设计学院）
('F101', 'F101设计工作室', 'F楼', 1, 30, '工作室', '绘图桌,投影仪,展示墙,空调', 0, '艺术设计创作工作室'),
('F102', 'F102设计工作室', 'F楼', 1, 30, '工作室', '绘图桌,投影仪,展示墙,空调', 0, '艺术设计创作工作室'),
('F201', 'F201数字媒体实验室', 'F楼', 2, 25, '实验室', '高配置计算机,设计软件,投影仪', 0, '数字媒体设计实验室'),
('F202', 'F202模型制作室', 'F楼', 2, 20, '制作室', '3D打印机,激光切割机,工具台', 0, '设计模型制作专用教室');

-- 10. 插入宿舍数据
INSERT IGNORE INTO dormitory (building_no, room_no, floor, capacity, current_count, type, facilities, fee, status, description) VALUES
-- 1号宿舍楼（男生宿舍）
('1号楼', '101', 1, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '102', 1, 4, 3, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '103', 1, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '104', 1, 4, 2, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '201', 2, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '202', 2, 4, 3, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '203', 2, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '204', 2, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '301', 3, 4, 3, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),
('1号楼', '302', 3, 4, 4, '男生宿舍', '床铺,书桌,衣柜,空调,热水器,网络', 1200.00, 0, '标准四人间男生宿舍'),

-- 2号宿舍楼（女生宿舍）
('2号楼', '101', 1, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '102', 1, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '103', 1, 4, 3, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '104', 1, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '201', 2, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '202', 2, 4, 3, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '203', 2, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),
('2号楼', '204', 2, 4, 4, '女生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,吹风机', 1200.00, 0, '标准四人间女生宿舍'),

-- 3号宿舍楼（研究生宿舍）
('3号楼', '101', 1, 2, 2, '研究生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,独立卫浴', 1800.00, 0, '标准双人间研究生宿舍'),
('3号楼', '102', 1, 2, 1, '研究生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,独立卫浴', 1800.00, 0, '标准双人间研究生宿舍'),
('3号楼', '103', 1, 2, 2, '研究生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,独立卫浴', 1800.00, 0, '标准双人间研究生宿舍'),
('3号楼', '201', 2, 2, 2, '研究生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,独立卫浴', 1800.00, 0, '标准双人间研究生宿舍'),
('3号楼', '202', 2, 2, 2, '研究生宿舍', '床铺,书桌,衣柜,空调,热水器,网络,独立卫浴', 1800.00, 0, '标准双人间研究生宿舍'),

-- 11. 插入体育场馆数据
INSERT IGNORE INTO sports_venue (name, type, location, capacity, facilities, open_time, fee, status, description) VALUES
('体育馆', '室内场馆', '体育中心', 2000, '篮球场,排球场,羽毛球场,看台,更衣室,淋浴间', '06:00-22:00', 0.00, 0, '综合性室内体育馆，可举办各类体育赛事'),
('游泳馆', '室内场馆', '体育中心', 300, '标准游泳池,儿童池,更衣室,淋浴间,救生设备', '06:00-21:30', 15.00, 0, '标准50米游泳池，配备专业救生员'),
('田径场', '室外场地', '运动场', 1500, '400米跑道,足球场,跳远沙坑,铅球场,看台', '06:00-21:00', 0.00, 0, '标准400米田径场，中央为足球场'),
('网球场', '室外场地', '网球中心', 100, '标准网球场,更衣室,休息区', '06:00-21:00', 20.00, 0, '4片标准网球场地'),
('篮球场', '室外场地', '篮球场区', 200, '标准篮球场,照明设备', '06:00-22:00', 0.00, 0, '8片室外标准篮球场'),
('足球场', '室外场地', '足球场区', 500, '标准足球场,更衣室,看台', '06:00-21:00', 0.00, 0, '标准11人制足球场'),
('乒乓球馆', '室内场馆', '乒乓球中心', 150, '乒乓球台,照明,通风,更衣室', '08:00-21:00', 5.00, 0, '20张标准乒乓球台'),
('羽毛球馆', '室内场馆', '羽毛球中心', 120, '羽毛球场,照明,通风,更衣室', '08:00-21:00', 10.00, 0, '12片标准羽毛球场地'),
('健身房', '室内场馆', '健身中心', 80, '跑步机,力量器械,自由重量区,更衣室', '06:00-22:00', 25.00, 0, '现代化健身设备，专业教练指导'),
('武术馆', '室内场馆', '武术中心', 100, '练功房,镜墙,音响,更衣室', '08:00-20:00', 8.00, 0, '传统武术和现代搏击训练场所');

-- 12. 插入借阅记录数据
INSERT IGNORE INTO borrow_record (student_id, book_id, borrow_date, due_date, return_date, status, fine_amount, remarks) VALUES
-- 已归还的借阅记录
(1, 1, '2024-01-15', '2024-02-15', '2024-02-10', 1, 0.00, '按时归还'),
(2, 2, '2024-01-20', '2024-02-20', '2024-02-18', 1, 0.00, '按时归还'),
(3, 3, '2024-02-01', '2024-03-01', '2024-03-05', 1, 2.00, '逾期4天归还'),
(4, 4, '2024-02-10', '2024-03-10', '2024-03-08', 1, 0.00, '按时归还'),
(5, 5, '2024-02-15', '2024-03-15', '2024-03-12', 1, 0.00, '按时归还'),
(6, 6, '2024-03-01', '2024-04-01', '2024-03-28', 1, 0.00, '按时归还'),
(7, 7, '2024-03-10', '2024-04-10', '2024-04-15', 1, 2.50, '逾期5天归还'),
(8, 8, '2024-03-15', '2024-04-15', '2024-04-12', 1, 0.00, '按时归还'),

-- 当前借阅中的记录
(9, 9, '2024-06-01', '2024-07-01', NULL, 0, 0.00, '正在借阅中'),
(10, 10, '2024-06-05', '2024-07-05', NULL, 0, 0.00, '正在借阅中'),
(11, 11, '2024-06-10', '2024-07-10', NULL, 0, 0.00, '正在借阅中'),
(12, 12, '2024-06-15', '2024-07-15', NULL, 0, 0.00, '正在借阅中'),
(13, 13, '2024-06-20', '2024-07-20', NULL, 0, 0.00, '正在借阅中'),
(14, 14, '2024-06-25', '2024-07-25', NULL, 0, 0.00, '正在借阅中'),
(15, 15, '2024-06-30', '2024-07-30', NULL, 0, 0.00, '正在借阅中'),

-- 逾期未还的记录
(16, 16, '2024-05-01', '2024-06-01', NULL, 2, 15.00, '逾期未还，已产生罚金'),
(17, 17, '2024-05-10', '2024-06-10', NULL, 2, 18.50, '逾期未还，已产生罚金'),
(18, 18, '2024-05-15', '2024-06-15', NULL, 2, 16.00, '逾期未还，已产生罚金');
