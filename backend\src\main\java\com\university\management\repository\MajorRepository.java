package com.university.management.repository;

import com.university.management.model.entity.Major;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 专业数据访问接口
 */
@Repository
public interface MajorRepository extends JpaRepository<Major, Integer> {
    
    /**
     * 根据专业编号查询专业
     */
    Optional<Major> findByMajorNo(String majorNo);
    
    /**
     * 根据专业名称查询专业
     */
    Optional<Major> findByName(String name);
    
    /**
     * 根据院系ID查询专业列表
     */
    List<Major> findByDepartmentId(Integer departmentId);
    
    /**
     * 根据院系ID分页查询专业
     */
    Page<Major> findByDepartmentId(Integer departmentId, Pageable pageable);
    
    /**
     * 根据专业名称模糊查询
     */
    Page<Major> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 根据专业名称和院系ID查询
     */
    Page<Major> findByNameContainingAndDepartmentId(String name, Integer departmentId, Pageable pageable);
}
