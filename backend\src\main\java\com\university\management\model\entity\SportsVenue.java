package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 体育场馆实体类
 */
@Entity
@Table(name = "sports_venue")
@TableName("sports_venue")
@ApiModel(value = "体育场馆实体", description = "体育场馆信息")
public class SportsVenue extends BaseEntity {

    /**
     * 场馆编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("场馆编号")
    private String venueNo;

    /**
     * 场馆名称
     */
    @Column(nullable = false, length = 100)
    @ApiModelProperty("场馆名称")
    private String name;

    /**
     * 场馆类型(0-田径场，1-游泳馆，2-篮球场，3-足球场，4-羽毛球场，5-乒乓球场，6-网球场，7-健身房，8-其他)
     */
    @Column
    @ApiModelProperty("场馆类型(0-田径场，1-游泳馆，2-篮球场，3-足球场，4-羽毛球场，5-乒乓球场，6-网球场，7-健身房，8-其他)")
    private Integer type;

    /**
     * 场地面积(平方米)
     */
    @Column
    @ApiModelProperty("场地面积(平方米)")
    private Double area;

    /**
     * 位置
     */
    @Column(length = 200)
    @ApiModelProperty("位置")
    private String location;

    /**
     * 容纳人数
     */
    @Column
    @ApiModelProperty("容纳人数")
    private Integer capacity;

    /**
     * 开放时间
     */
    @Column(length = 100)
    @ApiModelProperty("开放时间")
    private String openingHours;

    /**
     * 费用(元/小时)
     */
    @Column
    @ApiModelProperty("费用(元/小时)")
    private Double fee;

    /**
     * 管理员ID
     */
    @Column
    @ApiModelProperty("管理员ID")
    private Integer managerId;

    /**
     * 设备描述
     */
    @Column(length = 500)
    @ApiModelProperty("设备描述")
    private String equipment;

    /**
     * 场馆描述
     */
    @Column(length = 500)
    @ApiModelProperty("场馆描述")
    private String description;

    /**
     * 场馆状态(0-正常开放，1-维修中，2-已关闭)
     */
    @Column
    @ApiModelProperty("场馆状态(0-正常开放，1-维修中，2-已关闭)")
    private Integer status;

    // Getter和Setter方法
    public String getVenueNo() {
        return venueNo;
    }

    public void setVenueNo(String venueNo) {
        this.venueNo = venueNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public String getOpeningHours() {
        return openingHours;
    }

    public void setOpeningHours(String openingHours) {
        this.openingHours = openingHours;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Integer getManagerId() {
        return managerId;
    }

    public void setManagerId(Integer managerId) {
        this.managerId = managerId;
    }

    public String getEquipment() {
        return equipment;
    }

    public void setEquipment(String equipment) {
        this.equipment = equipment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}