package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.Student;
import com.university.management.repository.StudentRepository;
import com.university.management.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 学生服务实现类
 * 提供学生信息的增删改查等功能
 */
@Service
public class StudentServiceImpl implements StudentService {

    private final StudentRepository studentRepository;

    @Autowired
    public StudentServiceImpl(StudentRepository studentRepository) {
        this.studentRepository = studentRepository;
    }

    /**
     * 创建新学生
     * 
     * @param student 学生信息
     * @return 创建成功的学生信息
     */
    @Override
    @Transactional
    @CacheEvict(value = {"studentCache", "statsCache"}, allEntries = true)
    public Student createStudent(Student student) {
        // 校验学生信息
        validateStudent(student);
        return studentRepository.save(student);
    }

    /**
     * 更新学生信息
     * 
     * @param id 学生ID
     * @param studentDetails 更新的学生信息
     * @return 更新后的学生信息
     * @throws ResourceNotFoundException 如果指定ID的学生不存在
     */
    @Override
    @Transactional
    @CachePut(value = "studentCache", key = "#id")
    @CacheEvict(value = "statsCache", allEntries = true)
    public Student updateStudent(Integer id, Student studentDetails) {
        Student student = studentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Student not found with id: " + id));

        // 校验学生信息
        validateStudent(studentDetails);

        student.setName(studentDetails.getName());
        student.setStudentNo(studentDetails.getStudentNo());
        student.setGender(studentDetails.getGender());
        student.setAge(studentDetails.getAge());
        student.setBirthday(studentDetails.getBirthday());
        student.setClassId(studentDetails.getClassId());
        student.setMajorId(studentDetails.getMajorId());
        student.setPhone(studentDetails.getPhone());
        student.setEmail(studentDetails.getEmail());
        student.setAddress(studentDetails.getAddress());
        student.setEnrollYear(studentDetails.getEnrollYear());
        student.setCollegeId(studentDetails.getCollegeId());
        student.setStatus(studentDetails.getStatus());
        student.setDormitoryId(studentDetails.getDormitoryId());
        student.setIdCard(studentDetails.getIdCard());
        
        return studentRepository.save(student);
    }

    /**
     * 删除学生信息
     * 
     * @param id 学生ID
     * @throws ResourceNotFoundException 如果指定ID的学生不存在
     */
    @Override
    @Transactional
    @CacheEvict(value = {"studentCache", "statsCache"}, allEntries = true)
    public void deleteStudent(Integer id) {
        Student student = studentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Student not found with id: " + id));
        studentRepository.delete(student);
    }

    /**
     * 根据ID查找学生
     * 
     * @param id 学生ID
     * @return 学生信息，如果不存在则返回空
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "studentCache", key = "#id", unless = "#result == null")
    public Optional<Student> findById(Integer id) {
        return studentRepository.findById(id);
    }

    /**
     * 根据学号查找学生
     * 
     * @param studentNumber 学号
     * @return 学生信息，如果不存在则返回空
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "studentCache", key = "'studentNo:' + #studentNumber", unless = "#result == null")
    public Optional<Student> findByStudentNo(String studentNumber) {
        return studentRepository.findByStudentNo(studentNumber);
    }

    /**
     * 查询所有学生
     * 
     * @return 学生列表
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "studentCache", key = "'all'")
    public List<Student> findAll() {
        return studentRepository.findAll();
    }

    /**
     * 分页查询学生
     * 
     * @param pageable 分页参数
     * @return 分页学生列表
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "studentCache", key = "'page:' + #pageable.pageNumber + ':' + #pageable.pageSize + ':' + #pageable.sort")
    public Page<Student> findAll(Pageable pageable) {
        return studentRepository.findAll(pageable);
    }

    /**
     * 根据条件分页查询学生
     * 
     * @param name 姓名
     * @param major 专业
     * @param grade 年级
     * @param pageable 分页参数
     * @return 分页学生列表
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "studentCache", key = "'search:' + #name + ':' + #major + ':' + #grade + ':' + #pageable.pageNumber + ':' + #pageable.pageSize")
    public Page<Student> findByConditions(String name, String major, Integer grade, Pageable pageable) {
        // 使用majorId代替major，enrollYear代替grade
        return studentRepository.findByConditions(name, major, grade, pageable);
    }

    /**
     * 检查学号是否存在
     * 
     * @param studentNumber 学号
     * @return 是否存在
     */
    @Override
    @Transactional(readOnly = true)
    public boolean existsByStudentNo(String studentNumber) {
        return studentRepository.existsByStudentNo(studentNumber);
    }

    /**
     * 统计指定年级的学生数量
     * 
     * @param grade 年级
     * @return 学生数量
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "statsCache", key = "'countByGrade:' + #grade")
    public long countByGrade(Integer grade) {
        // 使用enrollYear代替grade
        return studentRepository.countByEnrollYear(grade);
    }

    /**
     * 获取学生专业分布统计
     *
     * @return 专业分布统计
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "statsCache", key = "'statsByMajor'")
    public Map<String, Long> getStudentStatsByMajor() {
        Map<String, Long> stats = new HashMap<>();
        List<Student> students = studentRepository.findAll();

        // 使用专业名称进行分组，而不是majorId
        stats = students.stream()
                .filter(student -> student.getMajor() != null) // 过滤掉没有专业信息的学生
                .collect(Collectors.groupingBy(
                    student -> student.getMajor().getName() != null ? student.getMajor().getName() : "未知专业",
                    Collectors.counting()
                ));

        return stats;
    }

    /**
     * 获取学生年级分布统计
     * 
     * @return 年级分布统计
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "statsCache", key = "'statsByGrade'")
    public Map<Integer, Long> getStudentStatsByGrade() {
        Map<Integer, Long> stats = new HashMap<>();
        List<Student> students = studentRepository.findAll();
        
        // 使用enrollYear进行分组
        stats = students.stream()
                .collect(Collectors.groupingBy(Student::getEnrollYear, Collectors.counting()));
        
        return stats;
    }
    
    /**
     * 校验学生信息
     * 
     * @param student 待校验的学生信息
     */
    private void validateStudent(Student student) {
        // 校验学生基本信息
        if (student.getStudentNo() == null || student.getStudentNo().trim().isEmpty()) {
            throw new IllegalArgumentException("学号不能为空");
        }
        if (student.getName() == null || student.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("姓名不能为空");
        }
        // 其他校验规则可以根据需求添加
    }
} 