-- 插入借阅记录数据
USE university_management;

-- 插入借阅记录数据
INSERT INTO borrow_record (borrow_no, student_id, book_id, borrow_date, expected_return_date, actual_return_date, status, fine, renew_count) VALUES
-- 已归还的借阅记录 (使用正确的student_id和book_id)
('BR001', 965, 811, '2024-01-15', '2024-02-15', '2024-02-10', 1, 0.00, 0),
('BR002', 966, 812, '2024-01-20', '2024-02-20', '2024-02-18', 1, 0.00, 0),
('BR003', 967, 813, '2024-02-01', '2024-03-01', '2024-02-28', 1, 0.00, 0),
('BR004', 968, 814, '2024-02-05', '2024-03-05', '2024-03-08', 1, 5.00, 0),
('BR005', 969, 815, '2024-02-10', '2024-03-10', '2024-03-09', 1, 0.00, 0),

-- 当前借阅中的记录
('BR006', 970, 816, '2024-06-01', '2024-07-01', NULL, 0, 0.00, 0),
('BR007', 971, 817, '2024-06-05', '2024-07-05', NULL, 0, 0.00, 0),
('BR008', 972, 818, '2024-06-10', '2024-07-10', NULL, 0, 0.00, 0),
('BR009', 973, 819, '2024-06-15', '2024-07-15', NULL, 0, 0.00, 0),
('BR010', 974, 820, '2024-06-20', '2024-07-20', NULL, 0, 0.00, 0),

-- 逾期未还的记录
('BR011', 975, 821, '2024-05-01', '2024-06-01', NULL, 2, 15.00, 0),
('BR012', 976, 822, '2024-05-10', '2024-06-10', NULL, 2, 8.00, 0),

-- 更多借阅记录
('BR013', 977, 823, '2024-03-15', '2024-04-15', '2024-04-12', 1, 0.00, 0),
('BR014', 978, 824, '2024-03-20', '2024-04-20', '2024-04-25', 1, 10.00, 0),
('BR015', 979, 825, '2024-04-01', '2024-05-01', '2024-04-30', 1, 0.00, 0);
