<template>
  <div class="course-edit-container">
    <div class="header">
      <h2>{{ isEdit ? '编辑课程' : '添加课程' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="courseForm"
      :rules="rules"
      label-width="100px"
      class="course-form"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程名称" prop="name">
            <el-input v-model="courseForm.name" placeholder="请输入课程名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程代码" prop="courseCode">
            <el-input v-model="courseForm.courseCode" placeholder="请输入课程代码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开课部门" prop="department">
            <el-input v-model="courseForm.department" placeholder="请输入开课部门"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程类型" prop="courseType">
            <el-select v-model="courseForm.courseType" placeholder="请选择课程类型" style="width: 100%">
              <el-option label="必修" value="必修"></el-option>
              <el-option label="选修" value="选修"></el-option>
              <el-option label="公共课" value="公共课"></el-option>
              <el-option label="专业课" value="专业课"></el-option>
              <el-option label="实验课" value="实验课"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="学分" prop="credit">
            <el-input-number v-model="courseForm.credit" :min="0.5" :max="10" :step="0.5" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学时" prop="hours">
            <el-input-number v-model="courseForm.hours" :min="8" :max="100" :step="2" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学期" prop="semester">
            <el-select v-model="courseForm.semester" placeholder="请选择学期" style="width: 100%">
              <el-option label="春季学期" value="春季学期"></el-option>
              <el-option label="秋季学期" value="秋季学期"></el-option>
              <el-option label="夏季学期" value="夏季学期"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="授课教师" prop="teacherName">
            <el-input v-model="courseForm.teacherName" placeholder="请输入授课教师"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大人数" prop="maxStudents">
            <el-input-number v-model="courseForm.maxStudents" :min="1" :max="200" style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上课地点" prop="classroom">
            <el-input v-model="courseForm.classroom" placeholder="请输入上课地点"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上课时间" prop="scheduleTime">
            <el-input v-model="courseForm.scheduleTime" placeholder="请输入上课时间，如：周一 1-2节"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="先修课程" prop="prerequisite">
        <el-input v-model="courseForm.prerequisite" placeholder="请输入先修课程，多个课程用逗号分隔"></el-input>
      </el-form-item>

      <el-form-item label="课程介绍" prop="description">
        <el-input
          v-model="courseForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入课程介绍"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCourseById, createCourse, updateCourse } from '@/api/course'

export default {
  name: 'CourseEdit',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)

    // 判断是编辑还是新增
    const isEdit = computed(() => {
      return route.params.id !== undefined
    })

    // 表单数据
    const courseForm = reactive({
      id: null,
      name: '',
      courseCode: '',
      department: '',
      courseType: '',
      credit: 3,
      hours: 48,
      semester: '',
      teacherName: '',
      maxStudents: 50,
      currentStudents: 0,
      classroom: '',
      scheduleTime: '',
      prerequisite: '',
      description: ''
    })

    // 表单验证规则
    const rules = reactive({
      name: [
        { required: true, message: '请输入课程名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      courseCode: [
        { required: true, message: '请输入课程代码', trigger: 'blur' },
        { pattern: /^[A-Z0-9]{2,12}$/, message: '课程代码格式不正确（2-12位大写字母和数字）', trigger: 'blur' }
      ],
      department: [
        { required: true, message: '请输入开课部门', trigger: 'blur' }
      ],
      courseType: [
        { required: true, message: '请选择课程类型', trigger: 'change' }
      ],
      credit: [
        { required: true, message: '请输入学分', trigger: 'blur' }
      ],
      hours: [
        { required: true, message: '请输入学时', trigger: 'blur' }
      ],
      semester: [
        { required: true, message: '请选择学期', trigger: 'change' }
      ],
      teacherName: [
        { required: true, message: '请输入授课教师', trigger: 'blur' }
      ],
      maxStudents: [
        { required: true, message: '请输入最大人数', trigger: 'blur' }
      ],
      classroom: [
        { required: true, message: '请输入上课地点', trigger: 'blur' }
      ],
      scheduleTime: [
        { required: true, message: '请输入上课时间', trigger: 'blur' }
      ]
    })

    // 获取课程详情
    const fetchCourseDetail = async (id) => {
      loading.value = true
      try {
        const res = await getCourseById(id)
        if (res.data) {
          // 将后端返回的数据填充到表单中
          Object.assign(courseForm, res.data)
        }
      } catch (error) {
        console.error('获取课程详情失败:', error)
        ElMessage.error('获取课程详情失败')
      } finally {
        loading.value = false
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            if (isEdit.value) {
              // 编辑模式
              await updateCourse(courseForm.id, courseForm)
              ElMessage.success('更新课程信息成功')
            } else {
              // 新增模式
              await createCourse(courseForm)
              ElMessage.success('添加课程成功')
            }
            goBack()
          } catch (error) {
            console.error('保存课程信息失败:', error)
            ElMessage.error('保存课程信息失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 返回上一页
    const goBack = () => {
      router.push('/courses')
    }

    // 组件挂载时，如果是编辑模式则获取课程详情
    onMounted(() => {
      if (isEdit.value) {
        fetchCourseDetail(route.params.id)
      }
    })

    return {
      formRef,
      loading,
      isEdit,
      courseForm,
      rules,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.course-edit-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.course-form {
  max-width: 1000px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 