import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/user'

interface UserState {
  token: string
  userInfo: Record<string, any>
  roles: string[]
  permissions: string[]
}

interface UserInfo {
  userInfo: Record<string, any>
  roles: string[]
  permissions: string[]
}

interface LoginForm {
  username: string
  password: string
}

interface LoginResponse {
  data: {
    token: string
  }
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: localStorage.getItem('token') || '',
    userInfo: {},
    roles: [],
    permissions: []
  }),

  getters: {
    isLoggedIn: (state: UserState): boolean => !!state.token
  },

  actions: {
    // 登录
    async loginAction(loginForm: LoginForm): Promise<LoginResponse> {
      try {
        const response = await login(loginForm)
        const { token } = response.data
        this.token = token
        localStorage.setItem('token', token)
        return response
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    },

    // 获取用户信息
    async getUserInfoAction(): Promise<UserInfo> {
      try {
        const response = await getUserInfo()
        const { userInfo, roles, permissions } = response.data
        this.userInfo = userInfo
        this.roles = roles
        this.permissions = permissions
        return { userInfo, roles, permissions }
      } catch (error) {
        console.error('Get user info failed:', error)
        throw error
      }
    },

    // 登出
    async logoutAction(): Promise<boolean> {
      try {
        await logout()
        this.resetUserState()
        return true
      } catch (error) {
        console.error('Logout failed:', error)
        throw error
      }
    },

    // 重置用户状态
    resetUserState(): void {
      this.token = ''
      this.userInfo = {}
      this.roles = []
      this.permissions = []
      localStorage.removeItem('token')
    }
  }
}) 