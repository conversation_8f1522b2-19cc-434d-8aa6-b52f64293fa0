-- 基本真实数据插入脚本
-- 只插入核心的学生、教师、课程数据，替换模拟数据

USE university_management;

-- 插入更多学生数据
INSERT IGNORE INTO student (student_no, name, gender, birthday, age, phone, email, major_id, class_id, department_id, college_id, enroll_year, status) VALUES
-- 2020级学生
('S2020001', '张小明', 1, '2002-03-15', 22, '13800001001', '<EMAIL>', 1, 1, 1, 1, 2020, 0),
('S2020002', '李小红', 0, '2002-05-20', 22, '13800001002', '<EMAIL>', 1, 1, 1, 1, 2020, 0),
('S2020003', '王小刚', 1, '2002-07-10', 22, '13800001003', '<EMAIL>', 1, 1, 1, 1, 2020, 0),
('S2020004', '赵小丽', 0, '2002-09-25', 22, '13800001004', '<EMAIL>', 1, 1, 1, 1, 2020, 0),
('S2020005', '刘小强', 1, '2002-11-30', 22, '13800001005', '<EMAIL>', 1, 1, 1, 1, 2020, 0),
('S2020006', '陈小华', 1, '2002-01-12', 22, '13800001006', '<EMAIL>', 2, 2, 1, 1, 2020, 0),
('S2020007', '周小芳', 0, '2002-04-18', 22, '13800001007', '<EMAIL>', 2, 2, 1, 1, 2020, 0),
('S2020008', '吴小军', 1, '2002-06-22', 22, '13800001008', '<EMAIL>', 2, 2, 1, 1, 2020, 0),
('S2020009', '郑小燕', 0, '2002-08-14', 22, '13800001009', '<EMAIL>', 2, 2, 1, 1, 2020, 0),
('S2020010', '孙小龙', 1, '2002-02-28', 22, '13800001010', '<EMAIL>', 3, 3, 1, 1, 2020, 0),
('S2020011', '马小梅', 0, '2002-10-05', 22, '13800001011', '<EMAIL>', 3, 3, 1, 1, 2020, 0),
('S2020012', '林小伟', 1, '2002-01-15', 22, '13800001012', '<EMAIL>', 3, 3, 1, 1, 2020, 0),
('S2020013', '何小静', 0, '2002-03-20', 22, '13800001013', '<EMAIL>', 4, 4, 1, 1, 2020, 0),
('S2020014', '高小峰', 1, '2002-05-10', 22, '13800001014', '<EMAIL>', 4, 4, 1, 1, 2020, 0),
('S2020015', '许小娟', 0, '2002-07-25', 22, '13800001015', '<EMAIL>', 5, 5, 1, 1, 2020, 0),

-- 2021级学生
('S2021001', '田小勇', 1, '2003-02-14', 21, '13800002001', '<EMAIL>', 1, 6, 1, 1, 2021, 0),
('S2021002', '邓小玲', 0, '2003-04-18', 21, '13800002002', '<EMAIL>', 1, 6, 1, 1, 2021, 0),
('S2021003', '韩小斌', 1, '2003-06-22', 21, '13800002003', '<EMAIL>', 1, 6, 1, 1, 2021, 0),
('S2021004', '曹小敏', 0, '2003-08-30', 21, '13800002004', '<EMAIL>', 1, 7, 1, 1, 2021, 0),
('S2021005', '蒋小涛', 1, '2003-01-10', 21, '13800002005', '<EMAIL>', 2, 8, 1, 1, 2021, 0),
('S2021006', '薛小慧', 0, '2003-03-15', 21, '13800002006', '<EMAIL>', 2, 8, 1, 1, 2021, 0),
('S2021007', '雷小鹏', 1, '2003-05-20', 21, '13800002007', '<EMAIL>', 2, 8, 1, 1, 2021, 0),
('S2021008', '方小兰', 0, '2003-07-25', 21, '13800002008', '<EMAIL>', 3, 9, 1, 1, 2021, 0),
('S2021009', '石小军', 1, '2003-09-30', 21, '13800002009', '<EMAIL>', 3, 9, 1, 1, 2021, 0),
('S2021010', '袁小丽', 0, '2003-11-12', 21, '13800002010', '<EMAIL>', 4, 10, 1, 1, 2021, 0),
('S2021011', '龙小峰', 1, '2003-01-08', 21, '13800002011', '<EMAIL>', 4, 10, 1, 1, 2021, 0),
('S2021012', '段小梅', 0, '2003-03-22', 21, '13800002012', '<EMAIL>', 5, 11, 1, 1, 2021, 0),

-- 2022级学生
('S2022001', '史小强', 1, '2004-05-16', 20, '13800003001', '<EMAIL>', 1, 12, 1, 1, 2022, 0),
('S2022002', '顾小华', 0, '2004-02-12', 20, '13800003002', '<EMAIL>', 1, 12, 1, 1, 2022, 0),
('S2022003', '夏小军', 1, '2004-04-28', 20, '13800003003', '<EMAIL>', 2, 13, 1, 1, 2022, 0),
('S2022004', '侯小丽', 0, '2004-06-15', 20, '13800003004', '<EMAIL>', 2, 13, 1, 1, 2022, 0),
('S2022005', '谭小斌', 1, '2004-08-20', 20, '13800003005', '<EMAIL>', 3, 14, 1, 1, 2022, 0),
('S2022006', '潘小红', 0, '2004-10-05', 20, '13800003006', '<EMAIL>', 3, 14, 1, 1, 2022, 0),
('S2022007', '崔小伟', 1, '2004-12-18', 20, '13800003007', '<EMAIL>', 4, 15, 1, 1, 2022, 0),
('S2022008', '秦小敏', 0, '2004-02-25', 20, '13800003008', '<EMAIL>', 4, 15, 1, 1, 2022, 0),
('S2022009', '尹小涛', 1, '2004-04-10', 20, '13800003009', '<EMAIL>', 5, 16, 1, 1, 2022, 0),
('S2022010', '江小慧', 0, '2004-06-28', 20, '13800003010', '<EMAIL>', 5, 16, 1, 1, 2022, 0),

-- 其他学院学生
('S2021101', '常小鹏', 1, '2003-08-14', 21, '13800101001', '<EMAIL>', 6, 17, 2, 2, 2021, 0),
('S2021102', '汤小兰', 0, '2003-01-20', 21, '13800101002', '<EMAIL>', 6, 17, 2, 2, 2021, 0),
('S2021103', '易小军', 1, '2003-03-15', 21, '13800101003', '<EMAIL>', 7, 18, 2, 2, 2021, 0),
('S2021104', '罗小丽', 0, '2003-05-22', 21, '13800101004', '<EMAIL>', 7, 18, 2, 2, 2021, 0),
('S2021105', '金小华', 1, '2003-02-08', 21, '13800101005', '<EMAIL>', 8, 19, 2, 2, 2021, 0),
('S2021106', '戴小梅', 0, '2003-04-25', 21, '13800101006', '<EMAIL>', 8, 19, 2, 2, 2021, 0),

-- 机械工程学院学生
('S2021201', '贺小强', 1, '2003-06-12', 21, '13800201001', '<EMAIL>', 9, 20, 3, 3, 2021, 0),
('S2021202', '傅小红', 0, '2003-07-30', 21, '13800201002', '<EMAIL>', 9, 20, 3, 3, 2021, 0),
('S2021203', '卢小伟', 1, '2003-09-18', 21, '13800201003', '<EMAIL>', 10, 21, 3, 3, 2021, 0),
('S2021204', '水小敏', 0, '2003-11-05', 21, '13800201004', '<EMAIL>', 10, 21, 3, 3, 2021, 0),

-- 经济管理学院学生
('S2021301', '花小涛', 1, '2003-01-28', 21, '13800301001', '<EMAIL>', 12, 22, 4, 4, 2021, 0),
('S2021302', '苗小慧', 0, '2003-03-12', 21, '13800301002', '<EMAIL>', 12, 22, 4, 4, 2021, 0),
('S2021303', '岑小鹏', 1, '2003-05-28', 21, '13800301003', '<EMAIL>', 13, 23, 4, 4, 2021, 0),
('S2021304', '温小丽', 0, '2003-07-15', 21, '13800301004', '<EMAIL>', 13, 23, 4, 4, 2021, 0),

-- 外国语学院学生
('S2021401', '邱小强', 1, '2003-02-20', 21, '13800401001', '<EMAIL>', 16, 24, 5, 5, 2021, 0),
('S2021402', '穆小华', 0, '2003-04-08', 21, '13800401002', '<EMAIL>', 16, 24, 5, 5, 2021, 0),
('S2021403', '卞小军', 1, '2003-06-25', 21, '13800401003', '<EMAIL>', 17, 25, 5, 5, 2021, 0);

-- 插入更多教师数据
INSERT IGNORE INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, department_id, hire_date, status) VALUES
('T001011', '马丽', 0, '1987-02-28', 37, 1, '13900001011', '<EMAIL>', 1, '2013-09-01', 0),
('T001012', '林峰', 1, '1981-10-12', 43, 2, '13900001012', '<EMAIL>', 1, '2007-09-01', 0),
('T001013', '何静', 0, '1988-03-08', 36, 1, '13900001013', '<EMAIL>', 1, '2015-09-01', 0),
('T001014', '高勇', 1, '1985-11-22', 39, 1, '13900001014', '<EMAIL>', 1, '2012-09-01', 0),
('T001015', '许梅', 0, '1983-07-15', 41, 2, '13900001015', '<EMAIL>', 1, '2009-09-01', 0),

('T002006', '薛华', 1, '1978-04-12', 46, 2, '13900002006', '<EMAIL>', 2, '2003-09-01', 0),
('T002007', '雷丽', 0, '1985-10-28', 39, 1, '13900002007', '<EMAIL>', 2, '2012-09-01', 0),

('T003004', '温小军', 1, '1979-06-18', 45, 2, '13900003004', '<EMAIL>', 3, '2005-09-01', 0),
('T003005', '邱小红', 0, '1986-11-25', 38, 1, '13900003005', '<EMAIL>', 3, '2013-09-01', 0),

('T004005', '穆小强', 1, '1980-08-22', 44, 2, '13900004005', '<EMAIL>', 4, '2006-09-01', 0),
('T004006', '卞小丽', 0, '1987-12-15', 37, 1, '13900004006', '<EMAIL>', 4, '2014-09-01', 0),

('T005004', '柏小涛', 1, '1982-03-30', 42, 1, '13900005004', '<EMAIL>', 5, '2009-09-01', 0),
('T005005', '全小慧', 0, '1988-09-12', 36, 1, '13900005005', '<EMAIL>', 5, '2015-09-01', 0),

('T006003', '景小鹏', 1, '1984-05-28', 40, 1, '13900006003', '<EMAIL>', 6, '2011-09-01', 0),
('T006004', '单小兰', 0, '1989-01-18', 35, 1, '13900006004', '<EMAIL>', 6, '2016-09-01', 0);

-- 插入更多课程数据
INSERT IGNORE INTO course (course_no, name, credit, hours, type, department_id, description, status) VALUES
('CS201', 'C++程序设计', 3.0, 48, 0, 1, 'C++面向对象编程语言', 0),
('CS202', '离散数学', 3.0, 48, 0, 1, '计算机科学的数学基础', 0),
('CS203', '计算机图形学', 3.0, 48, 1, 1, '计算机图形生成和处理技术', 0),
('CS204', '计算机视觉', 2.0, 32, 1, 1, '图像识别和计算机视觉算法', 0),
('CS205', '自然语言处理', 2.0, 32, 1, 1, '文本处理和语言理解技术', 0),
('CS206', '分布式系统', 3.0, 48, 1, 1, '分布式计算系统设计', 0),
('CS207', '信息安全', 3.0, 48, 0, 1, '网络安全和信息保护技术', 0),
('CS208', '嵌入式系统', 3.0, 48, 1, 1, '嵌入式硬件和软件开发', 0),

('EE201', '电磁场理论', 3.0, 48, 0, 2, '电磁场的基本理论和应用', 0),
('EE202', '微波技术', 2.0, 32, 1, 2, '微波电路和系统设计', 0),
('EE203', '光纤通信', 3.0, 48, 1, 2, '光纤通信系统原理', 0),
('EE204', '移动通信', 3.0, 48, 0, 2, '移动通信网络技术', 0),
('EE205', '数字图像处理', 2.0, 32, 1, 2, '数字图像处理算法', 0),

('ME201', '流体力学', 3.0, 48, 0, 3, '流体运动的基本规律', 0),
('ME202', '热力学', 3.0, 48, 0, 3, '热能转换的基本原理', 0),
('ME203', '机械振动', 2.0, 32, 1, 3, '机械系统的振动分析', 0),
('ME204', '液压传动', 2.0, 32, 1, 3, '液压系统设计和应用', 0),

('EM201', '运营管理', 3.0, 48, 0, 4, '企业运营管理理论和方法', 0),
('EM202', '人力资源管理', 3.0, 48, 0, 4, '人力资源开发和管理', 0),
('EM203', '国际贸易实务', 3.0, 48, 0, 4, '国际贸易操作实务', 0),
('EM204', '投资学', 2.0, 32, 1, 4, '投资理论和实践', 0),

('FL201', '英美文学', 3.0, 48, 0, 5, '英美文学作品赏析', 0),
('FL202', '翻译理论与实践', 2.0, 32, 0, 5, '翻译技巧和方法', 0),
('FL203', '日本文化', 2.0, 32, 1, 5, '日本社会文化概况', 0),

('AD201', '平面设计', 3.0, 48, 0, 6, '平面设计原理和技法', 0),
('AD202', '室内设计', 3.0, 48, 0, 6, '室内空间设计方法', 0),
('AD203', '产品设计', 2.0, 32, 1, 6, '工业产品设计流程', 0);
