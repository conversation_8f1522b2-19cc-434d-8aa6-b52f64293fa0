-- 清理并重新插入正确的数据
USE university_management;

-- 清理所有数据
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM borrow_record WHERE id > 0;
DELETE FROM student_course WHERE id > 0;
DELETE FROM venue_booking WHERE id > 0;
DELETE FROM schedule WHERE id > 0;
DELETE FROM student WHERE id > 0;
DELETE FROM teacher WHERE id > 0;
DELETE FROM course WHERE id > 0;
DELETE FROM book WHERE id > 0;
DELETE FROM classroom WHERE id > 0;
DELETE FROM dormitory WHERE id > 0;
DELETE FROM sports_venue WHERE id > 0;
DELETE FROM class WHERE id > 0;
DELETE FROM major WHERE id > 0;
DELETE FROM department WHERE id > 0;
SET FOREIGN_KEY_CHECKS = 1;

-- 重新插入院系数据
INSERT INTO department (department_no, name, description, dean, telephone, email, address, established_date, website, sort, status) VALUES
('CS01', 'Computer Science College', 'Computer Science, Software Engineering, Network Engineering', '<PERSON>', '0571-88888001', '<EMAIL>', 'Computer Building A', '1985-09-01', 'http://cs.university.edu', 1, 0),
('EE01', 'Electronic Engineering College', 'Electronic Information Engineering, Communication Engineering, Automation', 'Tian Jun', '0571-********', '<EMAIL>', 'Electronic Building B', '1988-09-01', 'http://ee.university.edu', 2, 0),
('ME01', 'Mechanical Engineering College', 'Mechanical Design and Manufacturing, Mechatronics, Vehicle Engineering', 'Fang Jun', '0571-********', '<EMAIL>', 'Mechanical Building C', '1990-09-01', 'http://me.university.edu', 3, 0),
('EM01', 'Economics and Management College', 'Business Administration, Accounting, Marketing, International Trade', 'Long Li', '0571-********', '<EMAIL>', 'Management Building D', '1992-09-01', 'http://em.university.edu', 4, 0),
('FL01', 'Foreign Languages College', 'English, Japanese, German, French', 'Xia Hua', '0571-********', '<EMAIL>', 'Language Building E', '1995-09-01', 'http://fl.university.edu', 5, 0),
('AD01', 'Art and Design College', 'Visual Communication Design, Environmental Design, Product Design', 'Pan Jun', '0571-********', '<EMAIL>', 'Art Building F', '1998-09-01', 'http://ad.university.edu', 6, 0);

-- 插入专业数据（使用正确的department_id）
INSERT INTO major (major_no, name, department_id, description, length, degree) VALUES
-- Computer Science College majors (department_id = 529)
('CS0101', 'Computer Science and Technology', 529, 'Training senior professionals in computer science and technology', '4 years', 'B.Eng'),
('SE0101', 'Software Engineering', 529, 'Training professionals in software development and engineering management', '4 years', 'B.Eng'),
('NE0101', 'Network Engineering', 529, 'Training professionals in network system design and management', '4 years', 'B.Eng'),
('AI0101', 'Artificial Intelligence', 529, 'Training professionals in AI technology applications', '4 years', 'B.Eng'),
('DS0101', 'Data Science and Big Data Technology', 529, 'Training professionals in big data analysis and processing', '4 years', 'B.Eng'),

-- Electronic Engineering College majors (department_id = 530)
('EE0101', 'Electronic Information Engineering', 530, 'Training professionals in electronic information system design and applications', '4 years', 'B.Eng'),
('TC0101', 'Communication Engineering', 530, 'Training professionals in communication systems and network technology', '4 years', 'B.Eng'),
('AU0101', 'Automation', 530, 'Training professionals in automatic control system design', '4 years', 'B.Eng'),

-- Mechanical Engineering College majors (department_id = 531)
('ME0101', 'Mechanical Design Manufacturing and Automation', 531, 'Training professionals in mechanical design and manufacturing', '4 years', 'B.Eng'),
('ME0201', 'Mechatronics Engineering', 531, 'Training professionals in mechatronics technology', '4 years', 'B.Eng'),
('VE0101', 'Vehicle Engineering', 531, 'Training professionals in automotive design and manufacturing', '4 years', 'B.Eng'),

-- Economics and Management College majors (department_id = 532)
('BM0101', 'Business Administration', 532, 'Training professionals in business management', '4 years', 'B.Mgmt'),
('AC0101', 'Accounting', 532, 'Training professionals in financial accounting', '4 years', 'B.Mgmt'),
('MK0101', 'Marketing', 532, 'Training professionals in marketing', '4 years', 'B.Mgmt'),
('IT0101', 'International Economics and Trade', 532, 'Training professionals in international trade', '4 years', 'B.Econ'),

-- Foreign Languages College majors (department_id = 533)
('EN0101', 'English', 533, 'Training professionals in English language and literature', '4 years', 'B.A'),
('JP0101', 'Japanese', 533, 'Training professionals in Japanese language and literature', '4 years', 'B.A'),

-- Art and Design College majors (department_id = 534)
('VD0101', 'Visual Communication Design', 534, 'Training professionals in visual design', '4 years', 'B.A'),
('ED0101', 'Environmental Design', 534, 'Training professionals in environmental art design', '4 years', 'B.A');

-- 插入班级数据
INSERT INTO class (class_no, name, major_id, department_id, grade, teacher_id, class_size) VALUES
-- 2020 Grade Classes
('CS2020-1', 'Computer Science 2020 Class 1', 1, 523, 2020, NULL, 45),
('CS2020-2', 'Computer Science 2020 Class 2', 1, 523, 2020, NULL, 43),
('SE2020-1', 'Software Engineering 2020 Class 1', 2, 523, 2020, NULL, 48),
('SE2020-2', 'Software Engineering 2020 Class 2', 2, 523, 2020, NULL, 46),
('NE2020-1', 'Network Engineering 2020 Class 1', 3, 523, 2020, NULL, 42),

-- 2021 Grade Classes
('CS2021-1', 'Computer Science 2021 Class 1', 1, 523, 2021, NULL, 47),
('CS2021-2', 'Computer Science 2021 Class 2', 1, 523, 2021, NULL, 45),
('SE2021-1', 'Software Engineering 2021 Class 1', 2, 523, 2021, NULL, 49),
('NE2021-1', 'Network Engineering 2021 Class 1', 3, 523, 2021, NULL, 44),
('AI2021-1', 'Artificial Intelligence 2021 Class 1', 4, 523, 2021, NULL, 38),

-- 2022 Grade Classes
('CS2022-1', 'Computer Science 2022 Class 1', 1, 523, 2022, NULL, 46),
('SE2022-1', 'Software Engineering 2022 Class 1', 2, 523, 2022, NULL, 47),
('NE2022-1', 'Network Engineering 2022 Class 1', 3, 523, 2022, NULL, 43),
('AI2022-1', 'Artificial Intelligence 2022 Class 1', 4, 523, 2022, NULL, 40),
('DS2022-1', 'Data Science 2022 Class 1', 5, 523, 2022, NULL, 35),

-- 2023 Grade Classes
('CS2023-1', 'Computer Science 2023 Class 1', 1, 523, 2023, NULL, 48),
('SE2023-1', 'Software Engineering 2023 Class 1', 2, 523, 2023, NULL, 50),
('NE2023-1', 'Network Engineering 2023 Class 1', 3, 523, 2023, NULL, 45),
('AI2023-1', 'Artificial Intelligence 2023 Class 1', 4, 523, 2023, NULL, 42),
('DS2023-1', 'Data Science 2023 Class 1', 5, 523, 2023, NULL, 38),

-- Other College Classes
('EE2021-1', 'Electronic Information 2021 Class 1', 6, 524, 2021, NULL, 44),
('TC2021-1', 'Communication Engineering 2021 Class 1', 7, 524, 2021, NULL, 41),
('AU2021-1', 'Automation 2021 Class 1', 8, 524, 2021, NULL, 43),
('ME2021-1', 'Mechanical Design 2021 Class 1', 9, 525, 2021, NULL, 46),
('BM2021-1', 'Business Administration 2021 Class 1', 12, 526, 2021, NULL, 52),
('AC2021-1', 'Accounting 2021 Class 1', 13, 526, 2021, NULL, 48),
('EN2021-1', 'English 2021 Class 1', 16, 527, 2021, NULL, 35);

-- 插入教师数据
INSERT INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, department_id, hire_date, status) VALUES
-- Computer Science College Teachers
('T001001', 'Zhang Wei', 1, '1975-03-15', 49, 3, '***********', '<EMAIL>', 523, '2000-09-01', 0),
('T001002', 'Li Na', 0, '1980-07-22', 44, 2, '***********', '<EMAIL>', 523, '2005-09-01', 0),
('T001003', 'Wang Qiang', 1, '1978-11-08', 46, 2, '***********', '<EMAIL>', 523, '2003-09-01', 0),
('T001004', 'Liu Min', 0, '1985-05-12', 39, 1, '***********', '<EMAIL>', 523, '2010-09-01', 0),
('T001005', 'Chen Jie', 1, '1982-09-25', 42, 1, '***********', '<EMAIL>', 523, '2008-09-01', 0),

-- Electronic Engineering College Teachers
('T002001', 'Tian Jun', 1, '1976-05-10', 48, 3, '***********', '<EMAIL>', 524, '2001-09-01', 0),
('T002002', 'Deng Hong', 0, '1982-09-18', 42, 2, '***********', '<EMAIL>', 524, '2008-09-01', 0),
('T002003', 'Han Bin', 1, '1984-01-25', 40, 1, '13900002003', '<EMAIL>', 524, '2011-09-01', 0),

-- Mechanical Engineering College Teachers
('T003001', 'Fang Jun', 1, '1974-02-20', 50, 3, '13900003001', '<EMAIL>', 525, '1999-09-01', 0),
('T003002', 'Shi Hong', 0, '1981-06-14', 43, 2, '13900003002', '<EMAIL>', 525, '2007-09-01', 0),

-- Economics and Management College Teachers
('T004001', 'Long Li', 0, '1979-03-25', 45, 2, '13900004001', '<EMAIL>', 526, '2004-09-01', 0),
('T004002', 'Duan Jun', 1, '1983-07-08', 41, 1, '13900004002', '<EMAIL>', 526, '2010-09-01', 0),

-- Foreign Languages College Teachers
('T005001', 'Xia Hua', 0, '1980-01-12', 44, 2, '13900005001', '<EMAIL>', 527, '2006-09-01', 0),
('T005002', 'Hou Bin', 1, '1984-08-05', 40, 1, '13900005002', '<EMAIL>', 527, '2011-09-01', 0),

-- Art and Design College Teachers
('T006001', 'Pan Jun', 1, '1981-04-28', 43, 2, '13900006001', '<EMAIL>', 528, '2007-09-01', 0),
('T006002', 'Cui Min', 0, '1987-10-15', 37, 1, '13900006002', '<EMAIL>', 528, '2014-09-01', 0);

-- 插入课程数据
INSERT INTO course (course_no, name, credit, hours, type, department_id, description, status) VALUES
('CS101', 'Programming Fundamentals', 3.0, 48, 0, 523, 'Basic course in computer programming', 0),
('CS102', 'Data Structures', 4.0, 64, 0, 523, 'Learning principles and applications of various data structures', 0),
('CS103', 'Algorithm Analysis', 3.0, 48, 0, 523, 'Algorithm design and analysis methods', 0),
('CS104', 'Computer Networks', 3.0, 48, 0, 523, 'Computer network principles and technology', 0),
('CS105', 'Database Systems', 3.0, 48, 0, 523, 'Database theory and practice', 0),
('CS201', 'C++ Programming', 3.0, 48, 0, 523, 'C++ object-oriented programming language', 0),
('CS202', 'Discrete Mathematics', 3.0, 48, 0, 523, 'Mathematical foundations of computer science', 0),
('CS203', 'Computer Graphics', 3.0, 48, 1, 523, 'Computer graphics generation and processing technology', 0),

('EE101', 'Circuit Analysis', 3.0, 48, 0, 524, 'Circuit theory fundamentals', 0),
('EE102', 'Analog Electronics', 3.0, 48, 0, 524, 'Analog circuit design and analysis', 0),
('EE103', 'Digital Electronics', 3.0, 48, 0, 524, 'Digital circuit principles and applications', 0),

('ME101', 'Mechanical Drawing', 3.0, 48, 0, 525, 'Mechanical drawing standards and specifications', 0),
('ME102', 'Theoretical Mechanics', 3.0, 48, 0, 525, 'Fundamental theories of mechanics', 0),

('EM101', 'Management Principles', 3.0, 48, 0, 526, 'Modern management theory fundamentals', 0),
('EM102', 'Microeconomics', 3.0, 48, 0, 526, 'Microeconomic theory analysis', 0),

('FL101', 'Comprehensive English', 3.0, 48, 0, 527, 'English comprehensive skills training', 0),
('FL102', 'English Grammar', 2.0, 32, 0, 527, 'Systematic explanation of English grammar', 0),

('AD101', 'Design Fundamentals', 3.0, 48, 0, 528, 'Design theory and practice fundamentals', 0),
('AD102', 'Color Composition', 2.0, 32, 0, 528, 'Color theory and applications', 0);
