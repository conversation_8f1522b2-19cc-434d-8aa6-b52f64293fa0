package com.university.management.controller;

import com.university.management.model.entity.BorrowRecord;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.BorrowRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 借阅记录控制器
 */
@Api(tags = "借阅记录管理")
@RestController
@RequestMapping("/api/borrowing-records")
public class BorrowRecordController {

    private final BorrowRecordService borrowRecordService;

    @Autowired
    public BorrowRecordController(BorrowRecordService borrowRecordService) {
        this.borrowRecordService = borrowRecordService;
    }

    @ApiOperation("获取所有借阅记录")
    @GetMapping
    public ApiResponse<List<BorrowRecord>> getAllBorrowRecords() {
        return ApiResponse.success("获取借阅记录列表成功", borrowRecordService.findAll());
    }

    @ApiOperation("分页获取借阅记录")
    @GetMapping("/page")
    public ApiResponse<Page<BorrowRecord>> getBorrowRecordsPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "DESC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success("获取借阅记录分页列表成功", borrowRecordService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询借阅记录")
    @GetMapping("/search")
    public ApiResponse<Page<BorrowRecord>> searchBorrowRecords(
            @RequestParam(required = false) Integer studentId,
            @RequestParam(required = false) Integer bookId,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success("条件查询借阅记录成功", 
                borrowRecordService.findByConditions(studentId, bookId, status, pageRequest));
    }

    @ApiOperation("获取借阅记录详情")
    @GetMapping("/{id}")
    public ApiResponse<BorrowRecord> getBorrowRecordById(@PathVariable Integer id) {
        Optional<BorrowRecord> borrowRecord = borrowRecordService.findById(id);
        return borrowRecord.map(record -> ApiResponse.success("获取借阅记录详情成功", record))
                .orElseGet(() -> ApiResponse.errorGeneric(404, "借阅记录不存在"));
    }

    @ApiOperation("创建借阅记录")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<BorrowRecord> createBorrowRecord(@Valid @RequestBody BorrowRecord borrowRecord) {
        return ApiResponse.success("创建借阅记录成功", borrowRecordService.createBorrowRecord(borrowRecord));
    }

    @ApiOperation("更新借阅记录")
    @PutMapping("/{id}")
    public ApiResponse<BorrowRecord> updateBorrowRecord(@PathVariable Integer id, @Valid @RequestBody BorrowRecord borrowRecordDetails) {
        return ApiResponse.success("更新借阅记录成功", borrowRecordService.updateBorrowRecord(id, borrowRecordDetails));
    }

    @ApiOperation("删除借阅记录")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteBorrowRecord(@PathVariable Integer id) {
        borrowRecordService.deleteBorrowRecord(id);
        return ApiResponse.success();
    }

    @ApiOperation("归还图书")
    @PutMapping("/{id}/return")
    public ApiResponse<BorrowRecord> returnBook(@PathVariable Integer id) {
        return ApiResponse.success("归还图书成功", borrowRecordService.returnBook(id));
    }

    @ApiOperation("获取借阅统计信息")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getBorrowingStats() {
        return ApiResponse.success("获取借阅统计信息成功", borrowRecordService.getBorrowingStats());
    }
}