package com.university.management.repository;

import com.university.management.model.entity.Class;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 班级数据访问接口
 */
@Repository
public interface ClassRepository extends JpaRepository<Class, Integer> {
    
    /**
     * 根据班级编号查询班级
     */
    Optional<Class> findByClassNo(String classNo);
    
    /**
     * 根据班级名称查询班级
     */
    Optional<Class> findByName(String name);
    
    /**
     * 根据专业ID查询班级列表
     */
    List<Class> findByMajorId(Integer majorId);
    
    /**
     * 根据专业ID分页查询班级
     */
    Page<Class> findByMajorId(Integer majorId, Pageable pageable);
    
    /**
     * 根据年级查询班级列表
     */
    List<Class> findByGrade(Integer grade);
    
    /**
     * 根据年级分页查询班级
     */
    Page<Class> findByGrade(Integer grade, Pageable pageable);
    
    /**
     * 根据班级名称模糊查询
     */
    Page<Class> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 根据班级名称和专业ID查询
     */
    Page<Class> findByNameContainingAndMajorId(String name, Integer majorId, Pageable pageable);
    
    /**
     * 根据班级名称和年级查询
     */
    Page<Class> findByNameContainingAndGrade(String name, Integer grade, Pageable pageable);
    
    /**
     * 根据专业ID和年级查询
     */
    Page<Class> findByMajorIdAndGrade(Integer majorId, Integer grade, Pageable pageable);
    
    /**
     * 根据班级名称、专业ID和年级查询
     */
    Page<Class> findByNameContainingAndMajorIdAndGrade(String name, Integer majorId, Integer grade, Pageable pageable);
}
