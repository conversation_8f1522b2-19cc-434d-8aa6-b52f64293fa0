<template>
  <div class="classroom-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑教室' : '新增教室' }}</h2>
    </div>

    <el-card class="form-container">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        label-position="right">

        <el-form-item label="教学楼" prop="building">
          <el-input v-model="form.building" placeholder="请输入教学楼" />
        </el-form-item>

        <el-form-item label="教室编号" prop="roomNo">
          <el-input v-model="form.roomNo" placeholder="请输入教室编号" />
        </el-form-item>

        <el-form-item label="教室类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择教室类型" style="width: 100%">
            <el-option label="普通教室" :value="0" />
            <el-option label="多媒体教室" :value="1" />
            <el-option label="实验室" :value="2" />
            <el-option label="会议室" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="容量" prop="capacity">
          <el-input-number v-model="form.capacity" :min="1" :max="500" />
        </el-form-item>

        <el-form-item label="设备信息" prop="equipment">
          <el-input v-model="form.equipment" type="textarea" :rows="3" placeholder="请输入设备信息" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">正常</el-radio>
            <el-radio :label="1">维修中</el-radio>
            <el-radio :label="2">已停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 引入API，实际项目中需要添加
// import { getClassroom, createClassroom, updateClassroom } from '@/api/classroom'

export default {
  name: 'ClassroomEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const classroomId = computed(() => route.params.id)
    const isEdit = computed(() => !!classroomId.value)

    // 表单数据
    const form = reactive({
      building: '',
      roomNo: '',
      type: 0,
      capacity: 50,
      equipment: '',
      status: 0,
      remark: ''
    })

    // 表单验证规则
    const rules = reactive({
      building: [{ required: true, message: '请输入教学楼', trigger: 'blur' }],
      roomNo: [{ required: true, message: '请输入教室编号', trigger: 'blur' }],
      type: [{ required: true, message: '请选择教室类型', trigger: 'change' }],
      capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }]
    })

    // 获取教室详情
    const fetchClassroomDetail = async (id) => {
      try {
        // 实际项目中这里需要调用API
        // const response = await getClassroom(id)
        // Object.assign(form, response.data)
        ElMessage.info('模拟加载教室数据')
        // 模拟数据
        Object.assign(form, {
          building: '教学楼A',
          roomNo: 'A101',
          type: 1,
          capacity: 60,
          equipment: '电脑、投影仪、音响设备',
          status: 0,
          remark: '多媒体教室'
        })
      } catch (error) {
        ElMessage.error('获取教室信息失败')
        console.error(error)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        // 实际项目中这里需要调用API
        if (isEdit.value) {
          // await updateClassroom(classroomId.value, form)
          ElMessage.success('更新教室成功')
        } else {
          // await createClassroom(form)
          ElMessage.success('创建教室成功')
        }
        
        // 返回列表页
        router.push({ name: 'ClassroomList' })
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.back()
    }

    // 组件挂载时，如果是编辑模式，获取教室详情
    onMounted(() => {
      if (isEdit.value) {
        fetchClassroomDetail(classroomId.value)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.classroom-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 800px;
}
</style> 