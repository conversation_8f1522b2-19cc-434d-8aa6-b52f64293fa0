package com.university.management.controller;

import com.university.management.model.vo.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.info.BuildProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统健康检查控制器
 * 用于检查系统状态和连接性
 */
@Api(tags = "系统健康检查")
@RestController
@RequestMapping("/api")
public class HealthController {

    @Autowired
    private DataSource dataSource;

    @Autowired(required = false)
    private BuildProperties buildProperties;

    @ApiOperation("系统健康检查")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 基本信息
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("service", "大学学生管理系统");
            health.put("version", buildProperties != null ? buildProperties.getVersion() : "1.0.0");
            
            // 数据库连接检查
            Map<String, Object> database = new HashMap<>();
            try (Connection connection = dataSource.getConnection()) {
                database.put("status", "UP");
                database.put("database", connection.getMetaData().getDatabaseProductName());
                database.put("url", connection.getMetaData().getURL());
            } catch (Exception e) {
                database.put("status", "DOWN");
                database.put("error", e.getMessage());
            }
            health.put("database", database);
            
            // 系统信息
            Map<String, Object> system = new HashMap<>();
            system.put("javaVersion", System.getProperty("java.version"));
            system.put("osName", System.getProperty("os.name"));
            system.put("osVersion", System.getProperty("os.version"));
            system.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + " MB");
            system.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + " MB");
            health.put("system", system);
            
            return ApiResponse.success("系统健康检查成功", health);
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ApiResponse.errorGeneric("系统健康检查失败");
        }
    }

    @ApiOperation("简单健康检查")
    @GetMapping("/ping")
    public ApiResponse<String> ping() {
        return ApiResponse.success("pong");
    }

    @ApiOperation("系统信息")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("name", "大学学生管理系统");
        info.put("description", "基于Spring Boot的大学学生管理系统");
        info.put("version", buildProperties != null ? buildProperties.getVersion() : "1.0.0");
        info.put("timestamp", LocalDateTime.now());
        
        // 功能模块
        Map<String, String> modules = new HashMap<>();
        modules.put("学生管理", "/api/students");
        modules.put("教师管理", "/api/teachers");
        modules.put("课程管理", "/api/courses");
        modules.put("院系管理", "/api/departments");
        modules.put("专业管理", "/api/majors");
        modules.put("班级管理", "/api/classes");
        modules.put("图书管理", "/api/books");
        modules.put("宿舍管理", "/api/dormitories");
        modules.put("体育场馆", "/api/sports-venues");
        modules.put("用户管理", "/api/users");
        info.put("modules", modules);
        
        return ApiResponse.success("获取系统信息成功", info);
    }

    @ApiOperation("数据统计概览")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> stats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 这里可以添加各种统计信息
            // 由于需要注入各种Service，这里先返回基本信息
            stats.put("timestamp", LocalDateTime.now());
            stats.put("message", "数据统计功能正常");
            
            return ApiResponse.success("获取统计信息成功", stats);
            
        } catch (Exception e) {
            return ApiResponse.errorGeneric(500, "获取统计信息失败");
        }
    }
}
