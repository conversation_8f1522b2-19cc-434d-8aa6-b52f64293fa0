import request from '@/utils/request'

const baseUrl = '/api/dormitories'

// 获取所有宿舍信息
export function getAllDormitories() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取宿舍信息
export function getDormitoriesByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询宿舍信息
export function searchDormitories({ building, roomNo, type, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      building,
      roomNo,
      type,
      page,
      size
    }
  })
}

// 根据ID获取宿舍信息
export function getDormitoryById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建宿舍信息
export function createDormitory(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新宿舍信息
export function updateDormitory(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除宿舍信息
export function deleteDormitory(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}
