-- 简单的中文化修复脚本
-- 分步执行，避免字符集排序规则冲突

USE university_management;

-- 设置字符集
SET NAMES utf8mb4;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 修复班级数据 - 分步执行
-- 计算机科学与技术班级
UPDATE class SET name = '计算机科学与技术2020级1班' WHERE name = 'Computer Science 2020 Class 1';
UPDATE class SET name = '计算机科学与技术2020级2班' WHERE name = 'Computer Science 2020 Class 2';
UPDATE class SET name = '计算机科学与技术2021级1班' WHERE name = 'Computer Science 2021 Class 1';
UPDATE class SET name = '计算机科学与技术2021级2班' WHERE name = 'Computer Science 2021 Class 2';
UPDATE class SET name = '计算机科学与技术2022级1班' WHERE name = 'Computer Science 2022 Class 1';
UPDATE class SET name = '计算机科学与技术2023级1班' WHERE name = 'Computer Science 2023 Class 1';

-- 软件工程班级
UPDATE class SET name = '软件工程2020级1班' WHERE name = 'Software Engineering 2020 Class 1';
UPDATE class SET name = '软件工程2021级1班' WHERE name = 'Software Engineering 2021 Class 1';
UPDATE class SET name = '软件工程2022级1班' WHERE name = 'Software Engineering 2022 Class 1';
UPDATE class SET name = '软件工程2023级1班' WHERE name = 'Software Engineering 2023 Class 1';

-- 网络工程班级
UPDATE class SET name = '网络工程2020级1班' WHERE name = 'Network Engineering 2020 Class 1';
UPDATE class SET name = '网络工程2021级1班' WHERE name = 'Network Engineering 2021 Class 1';
UPDATE class SET name = '网络工程2022级1班' WHERE name = 'Network Engineering 2022 Class 1';
UPDATE class SET name = '网络工程2023级1班' WHERE name = 'Network Engineering 2023 Class 1';

-- 人工智能班级
UPDATE class SET name = '人工智能2021级1班' WHERE name = 'Artificial Intelligence 2021 Class 1';
UPDATE class SET name = '人工智能2022级1班' WHERE name = 'Artificial Intelligence 2022 Class 1';
UPDATE class SET name = '人工智能2023级1班' WHERE name = 'Artificial Intelligence 2023 Class 1';

-- 数据科学班级
UPDATE class SET name = '数据科学与大数据技术2021级1班' WHERE name = 'Data Science 2021 Class 1';
UPDATE class SET name = '数据科学与大数据技术2022级1班' WHERE name = 'Data Science 2022 Class 1';
UPDATE class SET name = '数据科学与大数据技术2023级1班' WHERE name = 'Data Science 2023 Class 1';

-- 电子信息工程班级
UPDATE class SET name = '电子信息工程2020级1班' WHERE name = 'Electronic Information Engineering 2020 Class 1';
UPDATE class SET name = '电子信息工程2021级1班' WHERE name = 'Electronic Information Engineering 2021 Class 1';
UPDATE class SET name = '电子信息工程2022级1班' WHERE name = 'Electronic Information Engineering 2022 Class 1';
UPDATE class SET name = '电子信息工程2023级1班' WHERE name = 'Electronic Information Engineering 2023 Class 1';

-- 通信工程班级
UPDATE class SET name = '通信工程2020级1班' WHERE name = 'Communication Engineering 2020 Class 1';
UPDATE class SET name = '通信工程2021级1班' WHERE name = 'Communication Engineering 2021 Class 1';
UPDATE class SET name = '通信工程2022级1班' WHERE name = 'Communication Engineering 2022 Class 1';
UPDATE class SET name = '通信工程2023级1班' WHERE name = 'Communication Engineering 2023 Class 1';

-- 机械设计制造及其自动化班级
UPDATE class SET name = '机械设计制造及其自动化2020级1班' WHERE name = 'Mechanical Design Manufacturing and Automation 2020 Class 1';
UPDATE class SET name = '机械设计制造及其自动化2021级1班' WHERE name = 'Mechanical Design Manufacturing and Automation 2021 Class 1';
UPDATE class SET name = '机械设计制造及其自动化2022级1班' WHERE name = 'Mechanical Design Manufacturing and Automation 2022 Class 1';
UPDATE class SET name = '机械设计制造及其自动化2023级1班' WHERE name = 'Mechanical Design Manufacturing and Automation 2023 Class 1';

-- 工商管理班级
UPDATE class SET name = '工商管理2020级1班' WHERE name = 'Business Administration 2020 Class 1';
UPDATE class SET name = '工商管理2021级1班' WHERE name = 'Business Administration 2021 Class 1';
UPDATE class SET name = '工商管理2022级1班' WHERE name = 'Business Administration 2022 Class 1';
UPDATE class SET name = '工商管理2023级1班' WHERE name = 'Business Administration 2023 Class 1';

-- 英语班级
UPDATE class SET name = '英语2020级1班' WHERE name = 'English 2020 Class 1';
UPDATE class SET name = '英语2021级1班' WHERE name = 'English 2021 Class 1';
UPDATE class SET name = '英语2022级1班' WHERE name = 'English 2022 Class 1';
UPDATE class SET name = '英语2023级1班' WHERE name = 'English 2023 Class 1';

-- 2. 修复专业数据
UPDATE major SET name = '计算机科学与技术' WHERE name = 'Computer Science and Technology';
UPDATE major SET name = '软件工程' WHERE name = 'Software Engineering';
UPDATE major SET name = '网络工程' WHERE name = 'Network Engineering';
UPDATE major SET name = '人工智能' WHERE name = 'Artificial Intelligence';
UPDATE major SET name = '数据科学与大数据技术' WHERE name = 'Data Science and Big Data Technology';
UPDATE major SET name = '电子信息工程' WHERE name = 'Electronic Information Engineering';
UPDATE major SET name = '通信工程' WHERE name = 'Communication Engineering';
UPDATE major SET name = '自动化' WHERE name = 'Automation';
UPDATE major SET name = '机械设计制造及其自动化' WHERE name = 'Mechanical Design Manufacturing and Automation';
UPDATE major SET name = '机械电子工程' WHERE name = 'Mechatronics Engineering';
UPDATE major SET name = '车辆工程' WHERE name = 'Vehicle Engineering';
UPDATE major SET name = '工业设计' WHERE name = 'Industrial Design';
UPDATE major SET name = '工商管理' WHERE name = 'Business Administration';
UPDATE major SET name = '会计学' WHERE name = 'Accounting';
UPDATE major SET name = '市场营销' WHERE name = 'Marketing';
UPDATE major SET name = '国际经济与贸易' WHERE name = 'International Economics and Trade';
UPDATE major SET name = '英语' WHERE name = 'English';
UPDATE major SET name = '日语' WHERE name = 'Japanese';
UPDATE major SET name = '德语' WHERE name = 'German';
UPDATE major SET name = '法语' WHERE name = 'French';
UPDATE major SET name = '视觉传达设计' WHERE name = 'Visual Communication Design';
UPDATE major SET name = '环境设计' WHERE name = 'Environmental Design';
UPDATE major SET name = '产品设计' WHERE name = 'Product Design';

-- 3. 修复学位类型
UPDATE major SET degree = '工学学士' WHERE degree = 'B.Eng';
UPDATE major SET degree = '工学学士' WHERE degree = 'Bachelor of Engineering';
UPDATE major SET degree = '理学学士' WHERE degree = 'B.Sc';
UPDATE major SET degree = '理学学士' WHERE degree = 'Bachelor of Science';
UPDATE major SET degree = '文学学士' WHERE degree = 'B.A';
UPDATE major SET degree = '文学学士' WHERE degree = 'Bachelor of Arts';
UPDATE major SET degree = '管理学学士' WHERE degree = 'B.M';
UPDATE major SET degree = '管理学学士' WHERE degree = 'Bachelor of Management';
UPDATE major SET degree = '艺术学学士' WHERE degree = 'B.F.A';
UPDATE major SET degree = '艺术学学士' WHERE degree = 'Bachelor of Fine Arts';

-- 4. 修复课程数据
UPDATE course SET name = '程序设计基础' WHERE name = 'Programming Fundamentals';
UPDATE course SET name = '数据结构' WHERE name = 'Data Structures';
UPDATE course SET name = '算法分析' WHERE name = 'Algorithm Analysis';
UPDATE course SET name = '计算机网络' WHERE name = 'Computer Networks';
UPDATE course SET name = '数据库系统' WHERE name = 'Database Systems';
UPDATE course SET name = '操作系统' WHERE name = 'Operating Systems';
UPDATE course SET name = '软件工程' WHERE name = 'Software Engineering';
UPDATE course SET name = '计算机图形学' WHERE name = 'Computer Graphics';
UPDATE course SET name = '人工智能' WHERE name = 'Artificial Intelligence';
UPDATE course SET name = '机器学习' WHERE name = 'Machine Learning';
UPDATE course SET name = 'Web开发技术' WHERE name = 'Web Development';
UPDATE course SET name = '移动应用开发' WHERE name = 'Mobile App Development';
UPDATE course SET name = '网络安全' WHERE name = 'Network Security';
UPDATE course SET name = '数字信号处理' WHERE name = 'Digital Signal Processing';
UPDATE course SET name = '电子电路' WHERE name = 'Electronic Circuits';
UPDATE course SET name = '通信原理' WHERE name = 'Communication Principles';
UPDATE course SET name = '控制系统' WHERE name = 'Control Systems';
UPDATE course SET name = '机械设计' WHERE name = 'Mechanical Design';
UPDATE course SET name = '制造技术' WHERE name = 'Manufacturing Technology';
UPDATE course SET name = '材料科学' WHERE name = 'Materials Science';
UPDATE course SET name = '热力学' WHERE name = 'Thermodynamics';
UPDATE course SET name = '流体力学' WHERE name = 'Fluid Mechanics';
UPDATE course SET name = '管理学原理' WHERE name = 'Management Principles';
UPDATE course SET name = '财务会计' WHERE name = 'Financial Accounting';
UPDATE course SET name = '营销管理' WHERE name = 'Marketing Management';
UPDATE course SET name = '国际贸易' WHERE name = 'International Trade';
UPDATE course SET name = '英国文学' WHERE name = 'English Literature';
UPDATE course SET name = '英语语法' WHERE name = 'English Grammar';
UPDATE course SET name = '翻译理论' WHERE name = 'Translation Theory';
UPDATE course SET name = '设计原理' WHERE name = 'Design Principles';
UPDATE course SET name = '色彩理论' WHERE name = 'Color Theory';
UPDATE course SET name = '字体设计' WHERE name = 'Typography';
UPDATE course SET name = '室内设计' WHERE name = 'Interior Design';
UPDATE course SET name = '景观设计' WHERE name = 'Landscape Design';

-- 5. 修复教室数据
UPDATE classroom SET name = '计算机实验室1' WHERE name = 'Computer Lab 1';
UPDATE classroom SET name = '计算机实验室2' WHERE name = 'Computer Lab 2';
UPDATE classroom SET name = '计算机实验室3' WHERE name = 'Computer Lab 3';
UPDATE classroom SET name = '人工智能研究实验室' WHERE name = 'AI Research Lab';
UPDATE classroom SET name = '软件开发实验室' WHERE name = 'Software Engineering Lab';
UPDATE classroom SET name = '数据库实验室' WHERE name = 'Database Lab';
UPDATE classroom SET name = '网络实验室' WHERE name = 'Network Lab';
UPDATE classroom SET name = '电子实验室1' WHERE name = 'Electronics Lab 1';
UPDATE classroom SET name = '电子实验室2' WHERE name = 'Electronics Lab 2';
UPDATE classroom SET name = '通信实验室' WHERE name = 'Communication Lab';
UPDATE classroom SET name = '信号处理实验室' WHERE name = 'Signal Processing Lab';
UPDATE classroom SET name = '控制系统实验室' WHERE name = 'Control Systems Lab';
UPDATE classroom SET name = '机械制图室' WHERE name = 'Mechanical Drawing Room';
UPDATE classroom SET name = '材料实验室' WHERE name = 'Materials Lab';
UPDATE classroom SET name = '制造技术实验室' WHERE name = 'Manufacturing Lab';
UPDATE classroom SET name = '热力学实验室' WHERE name = 'Thermodynamics Lab';
UPDATE classroom SET name = 'CAD设计实验室' WHERE name = 'CAD Lab';
UPDATE classroom SET name = '商务模拟实验室' WHERE name = 'Business Simulation Lab';
UPDATE classroom SET name = '会计实验室' WHERE name = 'Accounting Lab';
UPDATE classroom SET name = '语言实验室1' WHERE name = 'Language Lab 1';
UPDATE classroom SET name = '语言实验室2' WHERE name = 'Language Lab 2';
UPDATE classroom SET name = '翻译实验室' WHERE name = 'Translation Lab';
UPDATE classroom SET name = '设计工作室1' WHERE name = 'Design Studio 1';
UPDATE classroom SET name = '设计工作室2' WHERE name = 'Design Studio 2';
UPDATE classroom SET name = '艺术创作工作室' WHERE name = 'Art Workshop';

-- 6. 修复建筑名称
UPDATE classroom SET building = '第一教学楼' WHERE building = 'Teaching Building A';
UPDATE classroom SET building = '第二教学楼' WHERE building = 'Teaching Building B';
UPDATE classroom SET building = '第三教学楼' WHERE building = 'Teaching Building C';
UPDATE classroom SET building = '第四教学楼' WHERE building = 'Teaching Building D';
UPDATE classroom SET building = '第五教学楼' WHERE building = 'Teaching Building E';
UPDATE classroom SET building = '第六教学楼' WHERE building = 'Teaching Building F';
UPDATE classroom SET building = '实验楼' WHERE building = 'Laboratory Building';
UPDATE classroom SET building = '工程楼' WHERE building = 'Engineering Building';
UPDATE classroom SET building = '理科楼' WHERE building = 'Science Building';
UPDATE classroom SET building = '文科楼' WHERE building = 'Liberal Arts Building';
UPDATE classroom SET building = '艺术楼' WHERE building = 'Art Building';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;

-- 显示更新结果
SELECT '简单中文化修复完成！' AS 状态;

-- 检查更新结果
SELECT '班级数据' AS 类型, COUNT(*) AS 总数, 
       SUM(CASE WHEN name LIKE '%级%班' THEN 1 ELSE 0 END) AS 中文数量
FROM class
UNION ALL
SELECT '专业数据', COUNT(*), 
       SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' AND name NOT LIKE '%Administration%' THEN 1 ELSE 0 END)
FROM major
UNION ALL
SELECT '课程数据', COUNT(*), 
       SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' AND name NOT LIKE '%System%' THEN 1 ELSE 0 END)
FROM course
UNION ALL
SELECT '教室数据', COUNT(*), 
       SUM(CASE WHEN name LIKE '%实验室%' OR name LIKE '%教室%' OR name LIKE '%工作室%' THEN 1 ELSE 0 END)
FROM classroom;
