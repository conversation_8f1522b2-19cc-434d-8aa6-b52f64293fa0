package com.university.management.controller;

import com.university.management.model.entity.Class;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.ClassService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Api(tags = "班级管理")
@RestController
@RequestMapping("/api/classes")
public class ClassController {

    private final ClassService classService;

    @Autowired
    public ClassController(ClassService classService) {
        this.classService = classService;
    }

    @ApiOperation("获取所有班级")
    @GetMapping
    public ApiResponse<List<Class>> getAllClasses() {
        List<Class> classes = classService.findAll();
        return ApiResponse.success("获取班级列表成功", classes);
    }

    @ApiOperation("分页获取班级")
    @GetMapping("/page")
    public ApiResponse<Page<Class>> getClassesPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "ASC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success(classService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询班级")
    @GetMapping("/search")
    public ApiResponse<Page<Class>> searchClasses(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer majorId,
            @RequestParam(required = false) Integer grade,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(classService.findByConditions(name, majorId, grade, pageRequest));
    }

    @ApiOperation("获取班级详情")
    @GetMapping("/{id}")
    public ApiResponse<Class> getClassById(@PathVariable Integer id) {
        Optional<Class> clazz = classService.findById(id);
        return clazz.map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.errorGeneric(404, "班级不存在"));
    }

    @ApiOperation("创建班级")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Class> createClass(@Valid @RequestBody Class clazz) {
        Class savedClass = classService.save(clazz);
        return ApiResponse.success("创建班级成功", savedClass);
    }

    @ApiOperation("更新班级")
    @PutMapping("/{id}")
    public ApiResponse<Class> updateClass(@PathVariable Integer id, @Valid @RequestBody Class classDetails) {
        Optional<Class> clazz = classService.findById(id);
        if (clazz.isPresent()) {
            classDetails.setId(id);
            Class updatedClass = classService.save(classDetails);
            return ApiResponse.success("更新班级成功", updatedClass);
        } else {
            return ApiResponse.errorGeneric(404, "班级不存在");
        }
    }

    @ApiOperation("删除班级")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteClass(@PathVariable Integer id) {
        Optional<Class> clazz = classService.findById(id);
        if (clazz.isPresent()) {
            classService.deleteById(id);
            return ApiResponse.success("删除班级成功", null);
        } else {
            return ApiResponse.errorGeneric(404, "班级不存在");
        }
    }

    @ApiOperation("根据专业ID获取班级列表")
    @GetMapping("/major/{majorId}")
    public ApiResponse<List<Class>> getClassesByMajorId(@PathVariable Integer majorId) {
        List<Class> classes = classService.findByMajorId(majorId);
        return ApiResponse.success("获取专业班级列表成功", classes);
    }

    @ApiOperation("根据年级获取班级列表")
    @GetMapping("/grade/{grade}")
    public ApiResponse<List<Class>> getClassesByGrade(@PathVariable Integer grade) {
        List<Class> classes = classService.findByGrade(grade);
        return ApiResponse.success("获取年级班级列表成功", classes);
    }

    @ApiOperation("获取班级统计信息")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getClassStats() {
        return ApiResponse.success("获取班级统计信息成功", classService.getClassStats());
    }
}
