package com.university.management.controller;

import com.university.management.model.common.RestResult;
import com.university.management.model.common.ResultCodeConstant;
import com.university.management.model.dto.LoginDTO;
import com.university.management.model.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@Api(tags = "认证管理")
public class AuthController {

    private static final Logger log = LoggerFactory.getLogger(AuthController.class);

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public RestResult<Map<String, Object>> login(@RequestBody @Valid LoginDTO loginDTO) {
        log.info("用户登录: {}", loginDTO.getUsername());
        
        // 这里简化处理，只要用户名是admin，密码是123456就认为登录成功
        if ("admin".equals(loginDTO.getUsername()) && "123456".equals(loginDTO.getPassword())) {
            Map<String, Object> result = new HashMap<>();
            // 生成一个模拟的token
            String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6ImFkbWluIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
            result.put("token", token);
            return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, result);
        } else {
            return new RestResult<>(ResultCodeConstant.CODE_000001, "用户名或密码错误");
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info")
    @ApiOperation("获取当前用户信息")
    public RestResult<UserInfoVO> getUserInfo() {
        // 这里简化处理，返回一个模拟的用户信息
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.setUserId(1);
        userInfo.setUsername("admin");
        userInfo.setRealName("系统管理员");
        userInfo.setAvatar("https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif");
        userInfo.setRoles(new String[]{"admin"});
        userInfo.setPermissions(new String[]{"*:*:*"});
        
        return new RestResult<>(ResultCodeConstant.CODE_000000, ResultCodeConstant.CODE_000000_MSG, userInfo);
    }

    /**
     * 用户登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public RestResult<Void> logout() {
        return new RestResult<>(ResultCodeConstant.CODE_000000, "登出成功");
    }
} 