import request from '@/utils/request'

const baseUrl = '/api/departments'

// 获取所有院系信息
export function getAllDepartments() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取院系信息
export function getDepartmentsByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询院系信息
export function searchDepartments({ name, code, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      code,
      page,
      size
    }
  })
}

// 根据ID获取院系信息
export function getDepartmentById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建院系信息
export function createDepartment(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新院系信息
export function updateDepartment(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除院系信息
export function deleteDepartment(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取院系统计信息
export function getDepartmentStats() {
  return request({
    url: `${baseUrl}/stats`,
    method: 'get'
  })
}

// 获取院系下的专业列表
export function getDepartmentMajors(departmentId) {
  return request({
    url: `${baseUrl}/${departmentId}/majors`,
    method: 'get'
  })
}

// 获取院系下的教师列表
export function getDepartmentTeachers(departmentId) {
  return request({
    url: `${baseUrl}/${departmentId}/teachers`,
    method: 'get'
  })
}
