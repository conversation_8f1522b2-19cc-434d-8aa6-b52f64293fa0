package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.Department;
import com.university.management.repository.DepartmentRepository;
import com.university.management.service.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 部门服务实现类
 */
@Service
@Transactional
public class DepartmentServiceImpl implements DepartmentService {

    private final DepartmentRepository departmentRepository;

    @Autowired
    public DepartmentServiceImpl(DepartmentRepository departmentRepository) {
        this.departmentRepository = departmentRepository;
    }

    @Override
    public Department createDepartment(Department department) {
        return departmentRepository.save(department);
    }

    @Override
    public Department updateDepartment(Integer id, Department departmentDetails) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Department not found with id: " + id));

        department.setName(departmentDetails.getName());
        department.setDepartmentNo(departmentDetails.getDepartmentNo());
        department.setDescription(departmentDetails.getDescription());
        department.setDean(departmentDetails.getDean());
        department.setTelephone(departmentDetails.getTelephone());
        department.setEmail(departmentDetails.getEmail());
        department.setAddress(departmentDetails.getAddress());
        department.setEstablishedDate(departmentDetails.getEstablishedDate());
        department.setWebsite(departmentDetails.getWebsite());
        department.setSort(departmentDetails.getSort());
        department.setStatus(departmentDetails.getStatus());
        
        return departmentRepository.save(department);
    }

    @Override
    public void deleteDepartment(Integer id) {
        Department department = departmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Department not found with id: " + id));
        departmentRepository.delete(department);
    }

    @Override
    public Optional<Department> findById(Integer id) {
        return departmentRepository.findById(id);
    }

    @Override
    public Optional<Department> findByDepartmentNo(String departmentNo) {
        return departmentRepository.findByDepartmentNo(departmentNo);
    }

    @Override
    public List<Department> findAll() {
        return departmentRepository.findAll();
    }

    @Override
    public Page<Department> findAll(Pageable pageable) {
        return departmentRepository.findAll(pageable);
    }

    @Override
    public Page<Department> findByConditions(String name, String dean, Pageable pageable) {
        return departmentRepository.findByConditions(name, dean, pageable);
    }
} 