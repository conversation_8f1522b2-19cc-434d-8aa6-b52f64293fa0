import request from '@/utils/request'

const baseUrl = '/api/users'

// 用户相关类型定义
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResult {
  token: string
}

export interface UserInfo {
  id: number
  username: string
  realName?: string
  email?: string
  phone?: string
  avatar?: string
  status: number
  roleId?: number
  createdTime?: string
}

export interface UserInfoResult {
  userInfo: UserInfo
  roles: string[]
  permissions: string[]
}

export interface UserQuery {
  username?: string
  realName?: string
  roleId?: number
  page?: number
  size?: number
}

export interface UserPageResult {
  content: UserInfo[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
  empty: boolean
}

export interface PasswordForm {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户登录
export function login(data: LoginForm) {
  return request<{ data: LoginResult }>({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

// 获取当前用户信息
export function getUserInfo() {
  return request<{ data: UserInfoResult }>({
    url: '/api/auth/info',
    method: 'get'
  })
}

// 用户登出
export function logout() {
  return request<void>({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 获取所有用户信息
export function getAllUsers() {
  return request<{ data: UserInfo[] }>({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取用户信息
export function getUsersByPage(page = 0, size = 10, sortBy = 'id') {
  return request<{ data: UserPageResult }>({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询用户信息
export function searchUsers({ username, realName, roleId, page = 0, size = 10 }: UserQuery) {
  return request<{ data: UserPageResult }>({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      username,
      realName,
      roleId,
      page,
      size
    }
  })
}

// 根据ID获取用户信息
export function getUserById(id: number) {
  return request<{ data: UserInfo }>({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建用户信息
export function createUser(data: Partial<UserInfo>) {
  return request<{ data: UserInfo }>({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUser(id: number, data: Partial<UserInfo>) {
  return request<{ data: UserInfo }>({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除用户信息
export function deleteUser(id: number) {
  return request<void>({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 修改密码
export function changePassword(data: PasswordForm) {
  return request<void>({
    url: `${baseUrl}/change-password`,
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(id: number) {
  return request<void>({
    url: `${baseUrl}/${id}/reset-password`,
    method: 'post'
  })
} 