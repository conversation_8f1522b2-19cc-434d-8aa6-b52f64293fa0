import request from '@/utils/request'

const baseUrl = '/api/roles'

// 模拟角色数据
const mockRoles = [
  {
    id: 1,
    roleName: '超级管理员',
    roleCode: 'SUPER_ADMIN',
    description: '系统超级管理员，拥有所有权限',
    status: 0,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    roleName: '系统管理员',
    roleCode: 'ADMIN',
    description: '系统管理员，负责系统配置和用户管理',
    status: 0,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 3,
    roleName: '教务管理员',
    roleCode: 'ACADEMIC_ADMIN',
    description: '教务管理员，负责课程和成绩管理',
    status: 0,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 4,
    roleName: '教师',
    roleCode: 'TEACHER',
    description: '教师角色，可以管理自己的课程和学生',
    status: 0,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 5,
    roleName: '学生',
    roleCode: 'STUDENT',
    description: '学生角色，可以查看课程和成绩',
    status: 0,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 6,
    roleName: '访客',
    roleCode: 'GUEST',
    description: '访客角色，只能查看基本信息',
    status: 1,
    createTime: '2024-01-01 10:00:00'
  }
]

// 分页工具函数
const paginate = (data, page = 0, size = 10) => {
  const start = page * size
  const end = start + size
  return {
    content: data.slice(start, end),
    totalElements: data.length,
    totalPages: Math.ceil(data.length / size),
    size: size,
    number: page,
    first: page === 0,
    last: page >= Math.ceil(data.length / size) - 1
  }
}

// 获取所有角色
export function getAllRoles() {
  // 先尝试真实API，失败则使用模拟数据
  return request({
    url: baseUrl,
    method: 'get'
  }).catch(() => {
    console.log('🔄 角色API不可用，使用模拟数据')
    return Promise.resolve({
      success: true,
      data: mockRoles
    })
  })
}

// 分页获取角色
export function getRolesByPage(page = 0, size = 10, sortBy = 'id') {
  // 先尝试真实API，失败则使用模拟数据
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  }).catch(() => {
    console.log('🔄 角色分页API不可用，使用模拟数据')
    return Promise.resolve({
      success: true,
      data: paginate(mockRoles, page, size)
    })
  })
}

// 根据ID获取角色
export function getRoleById(id) {
  // 先尝试真实API，失败则使用模拟数据
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  }).catch(() => {
    console.log('🔄 角色详情API不可用，使用模拟数据')
    const role = mockRoles.find(r => r.id === id)
    if (role) {
      return Promise.resolve({
        success: true,
        data: role
      })
    } else {
      return Promise.reject(new Error('角色不存在'))
    }
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  }).catch(() => {
    console.log('🔄 创建角色API不可用，模拟成功响应')
    return Promise.resolve({
      success: true,
      message: '创建角色成功（模拟）',
      data: { ...data, id: Date.now() }
    })
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  }).catch(() => {
    console.log('🔄 更新角色API不可用，模拟成功响应')
    return Promise.resolve({
      success: true,
      message: '更新角色成功（模拟）',
      data: { ...data, id }
    })
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  }).catch(() => {
    console.log('🔄 删除角色API不可用，模拟成功响应')
    return Promise.resolve({
      success: true,
      message: '删除角色成功（模拟）'
    })
  })
}

// 获取角色权限
export function getRolePermissions(roleId) {
  return request({
    url: `${baseUrl}/${roleId}/permissions`,
    method: 'get'
  })
}

// 更新角色权限
export function updateRolePermissions(roleId, permissions) {
  return request({
    url: `${baseUrl}/${roleId}/permissions`,
    method: 'put',
    data: permissions
  })
}

// 获取所有权限
export function getAllPermissions() {
  return request({
    url: '/api/permissions',
    method: 'get'
  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/api/permissions/tree',
    method: 'get'
  })
} 