<template>
  <div class="teacher-edit-container">
    <div class="header">
      <h2>{{ isEdit ? '编辑教师' : '添加教师' }}</h2>
    </div>

    <el-form
      ref="formRef"
      :model="teacherForm"
      :rules="rules"
      label-width="100px"
      class="teacher-form"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="teacherForm.name" placeholder="请输入姓名"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工号" prop="employeeNumber">
            <el-input v-model="teacherForm.employeeNumber" placeholder="请输入工号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="teacherForm.gender" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" value="MALE"></el-option>
              <el-option label="女" value="FEMALE"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生日" prop="birthDate">
            <el-date-picker
              v-model="teacherForm.birthDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属部门" prop="department">
            <el-input v-model="teacherForm.department" placeholder="请输入所属部门"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职称" prop="title">
            <el-select v-model="teacherForm.title" placeholder="请选择职称" style="width: 100%">
              <el-option label="教授" value="教授"></el-option>
              <el-option label="副教授" value="副教授"></el-option>
              <el-option label="讲师" value="讲师"></el-option>
              <el-option label="助教" value="助教"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="teacherForm.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="teacherForm.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入职日期" prop="hireDate">
            <el-date-picker
              v-model="teacherForm.hireDate"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-select v-model="teacherForm.education" placeholder="请选择学历" style="width: 100%">
              <el-option label="博士" value="博士"></el-option>
              <el-option label="硕士" value="硕士"></el-option>
              <el-option label="本科" value="本科"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="研究领域" prop="researchArea">
        <el-input v-model="teacherForm.researchArea" placeholder="请输入研究领域"></el-input>
      </el-form-item>

      <el-form-item label="简介" prop="description">
        <el-input
          v-model="teacherForm.description"
          type="textarea"
          :rows="4"
          placeholder="请输入教师简介"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="goBack">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getTeacherById, createTeacher, updateTeacher } from '@/api/teacher'

export default {
  name: 'TeacherEdit',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)

    // 判断是编辑还是新增
    const isEdit = computed(() => {
      return route.params.id !== undefined
    })

    // 表单数据
    const teacherForm = reactive({
      id: null,
      name: '',
      employeeNumber: '',
      gender: '',
      birthDate: '',
      department: '',
      title: '',
      email: '',
      phone: '',
      hireDate: '',
      education: '',
      researchArea: '',
      description: ''
    })

    // 表单验证规则
    const rules = reactive({
      name: [
        { required: true, message: '请输入教师姓名', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      employeeNumber: [
        { required: true, message: '请输入工号', trigger: 'blur' },
        { pattern: /^\d{5,10}$/, message: '工号格式不正确（5-10位数字）', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      department: [
        { required: true, message: '请输入所属部门', trigger: 'blur' }
      ],
      title: [
        { required: true, message: '请选择职称', trigger: 'change' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
      ],
      hireDate: [
        { required: true, message: '请选择入职日期', trigger: 'change' }
      ]
    })

    // 获取教师详情
    const fetchTeacherDetail = async (id) => {
      loading.value = true
      try {
        const res = await getTeacherById(id)
        if (res.data) {
          // 将后端返回的数据填充到表单中
          Object.assign(teacherForm, res.data)
        }
      } catch (error) {
        console.error('获取教师详情失败:', error)
        ElMessage.error('获取教师详情失败')
      } finally {
        loading.value = false
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            if (isEdit.value) {
              // 编辑模式
              await updateTeacher(teacherForm.id, teacherForm)
              ElMessage.success('更新教师信息成功')
            } else {
              // 新增模式
              await createTeacher(teacherForm)
              ElMessage.success('添加教师成功')
            }
            goBack()
          } catch (error) {
            console.error('保存教师信息失败:', error)
            ElMessage.error('保存教师信息失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 返回上一页
    const goBack = () => {
      router.push('/teachers')
    }

    // 组件挂载时，如果是编辑模式则获取教师详情
    onMounted(() => {
      if (isEdit.value) {
        fetchTeacherDetail(route.params.id)
      }
    })

    return {
      formRef,
      loading,
      isEdit,
      teacherForm,
      rules,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.teacher-edit-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.teacher-form {
  max-width: 1000px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style> 