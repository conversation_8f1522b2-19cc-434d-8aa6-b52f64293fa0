-- 完整的数据库中文化更新脚本
-- 将所有英文数据更新为标准中文数据

USE university_management;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 删除所有英文班级数据，重新插入中文数据
DELETE FROM class WHERE name LIKE '%Class%' OR name LIKE '%Engineering%' OR name LIKE '%Science%' OR name LIKE '%Administration%';

-- 重新插入中文班级数据
INSERT INTO class (class_no, name, major_id, department_id, grade, class_size) VALUES
-- 计算机科学与技术专业班级
('CS2020-1', '计算机科学与技术2020级1班', 1, 1, 2020, 30),
('CS2020-2', '计算机科学与技术2020级2班', 1, 1, 2020, 28),
('CS2021-1', '计算机科学与技术2021级1班', 1, 1, 2021, 32),
('CS2021-2', '计算机科学与技术2021级2班', 1, 1, 2021, 30),
('CS2022-1', '计算机科学与技术2022级1班', 1, 1, 2022, 35),
('CS2023-1', '计算机科学与技术2023级1班', 1, 1, 2023, 33),

-- 软件工程专业班级
('SE2020-1', '软件工程2020级1班', 2, 1, 2020, 28),
('SE2021-1', '软件工程2021级1班', 2, 1, 2021, 30),
('SE2022-1', '软件工程2022级1班', 2, 1, 2022, 32),
('SE2023-1', '软件工程2023级1班', 2, 1, 2023, 29),

-- 网络工程专业班级
('NE2020-1', '网络工程2020级1班', 3, 1, 2020, 26),
('NE2021-1', '网络工程2021级1班', 3, 1, 2021, 28),
('NE2022-1', '网络工程2022级1班', 3, 1, 2022, 30),
('NE2023-1', '网络工程2023级1班', 3, 1, 2023, 27),

-- 电子信息工程专业班级
('EE2020-1', '电子信息工程2020级1班', 4, 2, 2020, 30),
('EE2021-1', '电子信息工程2021级1班', 4, 2, 2021, 32),
('EE2022-1', '电子信息工程2022级1班', 4, 2, 2022, 28),
('EE2023-1', '电子信息工程2023级1班', 4, 2, 2023, 31),

-- 通信工程专业班级
('TC2020-1', '通信工程2020级1班', 5, 2, 2020, 28),
('TC2021-1', '通信工程2021级1班', 5, 2, 2021, 30),
('TC2022-1', '通信工程2022级1班', 5, 2, 2022, 26),
('TC2023-1', '通信工程2023级1班', 5, 2, 2023, 29),

-- 机械设计制造及其自动化专业班级
('ME2020-1', '机械设计制造及其自动化2020级1班', 6, 3, 2020, 32),
('ME2021-1', '机械设计制造及其自动化2021级1班', 6, 3, 2021, 35),
('ME2022-1', '机械设计制造及其自动化2022级1班', 6, 3, 2022, 30),
('ME2023-1', '机械设计制造及其自动化2023级1班', 6, 3, 2023, 33),

-- 工业设计专业班级
('ID2020-1', '工业设计2020级1班', 7, 3, 2020, 25),
('ID2021-1', '工业设计2021级1班', 7, 3, 2021, 28),
('ID2022-1', '工业设计2022级1班', 7, 3, 2022, 26),
('ID2023-1', '工业设计2023级1班', 7, 3, 2023, 27),

-- 工商管理专业班级
('BM2020-1', '工商管理2020级1班', 8, 4, 2020, 35),
('BM2021-1', '工商管理2021级1班', 8, 4, 2021, 38),
('BM2022-1', '工商管理2022级1班', 8, 4, 2022, 32),
('BM2023-1', '工商管理2023级1班', 8, 4, 2023, 36),

-- 市场营销专业班级
('MK2020-1', '市场营销2020级1班', 9, 4, 2020, 30),
('MK2021-1', '市场营销2021级1班', 9, 4, 2021, 32),
('MK2022-1', '市场营销2022级1班', 9, 4, 2022, 28),
('MK2023-1', '市场营销2023级1班', 9, 4, 2023, 31),

-- 英语专业班级
('EN2020-1', '英语2020级1班', 10, 5, 2020, 28),
('EN2021-1', '英语2021级1班', 10, 5, 2021, 30),
('EN2022-1', '英语2022级1班', 10, 5, 2022, 26),
('EN2023-1', '英语2023级1班', 10, 5, 2023, 29);

-- 2. 更新专业数据为标准中文
UPDATE major SET 
    name = CASE major_no
        WHEN 'M001' THEN '计算机科学与技术'
        WHEN 'M002' THEN '软件工程'
        WHEN 'M003' THEN '网络工程'
        WHEN 'M004' THEN '电子信息工程'
        WHEN 'M005' THEN '通信工程'
        WHEN 'M006' THEN '机械设计制造及其自动化'
        WHEN 'M007' THEN '工业设计'
        WHEN 'M008' THEN '工商管理'
        WHEN 'M009' THEN '市场营销'
        WHEN 'M010' THEN '英语'
        ELSE name
    END,
    description = CASE major_no
        WHEN 'M001' THEN '计算机科学与技术专业培养具有扎实计算机理论基础的复合型人才。'
        WHEN 'M002' THEN '软件工程专业培养软件设计、开发和测试等方面的专业人才。'
        WHEN 'M003' THEN '网络工程专业培养计算机网络设计、建设与维护的专业人才。'
        WHEN 'M004' THEN '电子信息工程专业培养电子设备与信息系统的设计与开发人才。'
        WHEN 'M005' THEN '通信工程专业培养现代通信系统设计与维护的专业人才。'
        WHEN 'M006' THEN '机械设计制造及其自动化专业培养机械领域的专业人才。'
        WHEN 'M007' THEN '工业设计专业培养产品设计与开发的专业人才。'
        WHEN 'M008' THEN '工商管理专业培养企业管理与经营的专业人才。'
        WHEN 'M009' THEN '市场营销专业培养市场分析与营销策略的专业人才。'
        WHEN 'M010' THEN '英语专业培养英语语言文学与翻译的专业人才。'
        ELSE description
    END,
    degree = CASE 
        WHEN degree IN ('B.Eng', 'Bachelor of Engineering') THEN '工学学士'
        WHEN degree IN ('B.Sc', 'Bachelor of Science') THEN '理学学士'
        WHEN degree IN ('B.A', 'Bachelor of Arts') THEN '文学学士'
        WHEN degree IN ('B.M', 'Bachelor of Management') THEN '管理学学士'
        WHEN degree IN ('B.F.A', 'Bachelor of Fine Arts') THEN '艺术学学士'
        ELSE degree
    END
WHERE major_no IN ('M001', 'M002', 'M003', 'M004', 'M005', 'M006', 'M007', 'M008', 'M009', 'M010');

-- 3. 更新课程数据为中文
UPDATE course SET 
    name = CASE course_no
        WHEN 'C001' THEN '程序设计基础'
        WHEN 'C002' THEN '数据结构'
        WHEN 'C003' THEN '算法分析'
        WHEN 'C004' THEN '计算机网络'
        WHEN 'C005' THEN '数据库系统'
        WHEN 'C006' THEN '操作系统'
        WHEN 'C007' THEN '软件工程'
        WHEN 'C008' THEN '人工智能'
        WHEN 'C009' THEN '管理学原理'
        WHEN 'C010' THEN '英语综合教程'
        ELSE name
    END,
    description = CASE course_no
        WHEN 'C001' THEN '计算机程序设计基础课程，学习编程基本概念和方法。'
        WHEN 'C002' THEN '学习各种数据结构的原理与应用。'
        WHEN 'C003' THEN '算法设计与分析方法。'
        WHEN 'C004' THEN '计算机网络原理与技术。'
        WHEN 'C005' THEN '数据库理论与实践。'
        WHEN 'C006' THEN '操作系统原理与应用。'
        WHEN 'C007' THEN '软件工程方法与实践。'
        WHEN 'C008' THEN '人工智能基础理论与应用。'
        WHEN 'C009' THEN '管理学基础理论与方法。'
        WHEN 'C010' THEN '英语综合技能训练。'
        ELSE description
    END
WHERE course_no IN ('C001', 'C002', 'C003', 'C004', 'C005', 'C006', 'C007', 'C008', 'C009', 'C010');

-- 4. 更新教室数据为中文
UPDATE classroom SET 
    name = CASE 
        WHEN name LIKE '%Computer Lab%' THEN CONCAT('计算机实验室', SUBSTRING(name, -1))
        WHEN name LIKE '%Lecture Hall%' THEN CONCAT('阶梯教室', SUBSTRING(name, -3))
        WHEN name = 'AI Research Lab' THEN '人工智能研究实验室'
        WHEN name = 'Software Development Lab' THEN '软件开发实验室'
        WHEN name = 'Network Lab' THEN '网络实验室'
        WHEN name LIKE '%Electronics Lab%' THEN CONCAT('电子实验室', SUBSTRING(name, -1))
        WHEN name = 'Communication Lab' THEN '通信实验室'
        WHEN name = 'Mechanical Workshop' THEN '机械加工车间'
        WHEN name = 'CAD Lab' THEN 'CAD设计实验室'
        WHEN name = 'Business Simulation Lab' THEN '商务模拟实验室'
        WHEN name LIKE '%Language Lab%' THEN CONCAT('语言实验室', SUBSTRING(name, -1))
        WHEN name LIKE '%Design Studio%' THEN CONCAT('设计工作室', SUBSTRING(name, -1))
        ELSE name
    END,
    building = CASE 
        WHEN building = 'Teaching Building A' THEN '第一教学楼'
        WHEN building = 'Teaching Building B' THEN '第二教学楼'
        WHEN building = 'Laboratory Building' THEN '实验楼A'
        WHEN building = 'Engineering Building' THEN '工程楼'
        WHEN building = 'Science Building' THEN '理科楼'
        WHEN building = 'Liberal Arts Building' THEN '文科楼'
        WHEN building = 'Art Building' THEN '艺术楼'
        ELSE building
    END
WHERE name LIKE '%Lab%' OR name LIKE '%Hall%' OR name LIKE '%Studio%' OR name LIKE '%Workshop%'
   OR building LIKE '%Building%';

-- 5. 更新宿舍数据为中文（如果有英文）
UPDATE dormitory SET 
    building = CASE 
        WHEN building LIKE '%Dormitory%' THEN REPLACE(building, 'Dormitory', '号宿舍楼')
        WHEN building LIKE '%Building%' THEN REPLACE(building, 'Building', '号楼')
        ELSE building
    END
WHERE building LIKE '%Dormitory%' OR building LIKE '%Building%';

-- 6. 更新体育场馆数据为中文（如果有英文）
UPDATE sports_venue SET 
    name = CASE 
        WHEN name = 'Basketball Court' THEN '篮球场'
        WHEN name = 'Football Field' THEN '足球场'
        WHEN name = 'Tennis Court' THEN '网球场'
        WHEN name = 'Swimming Pool' THEN '游泳池'
        WHEN name = 'Gymnasium' THEN '体育馆'
        WHEN name = 'Track and Field' THEN '田径场'
        WHEN name = 'Badminton Court' THEN '羽毛球馆'
        WHEN name = 'Table Tennis Room' THEN '乒乓球馆'
        WHEN name = 'Fitness Center' THEN '健身房'
        WHEN name = 'Volleyball Court' THEN '排球场'
        ELSE name
    END
WHERE name LIKE '%Court%' OR name LIKE '%Field%' OR name LIKE '%Pool%' OR name LIKE '%Center%';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;

-- 显示更新结果
SELECT '数据中文化更新完成！' AS 状态;

SELECT 
    '班级' AS 数据类型, 
    COUNT(*) AS 总数量,
    SUM(CASE WHEN name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END) AS 中文数量
FROM class
UNION ALL
SELECT 
    '专业', 
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' AND name NOT LIKE '%Administration%' THEN 1 ELSE 0 END)
FROM major
UNION ALL
SELECT 
    '课程', 
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' AND name NOT LIKE '%System%' THEN 1 ELSE 0 END)
FROM course
UNION ALL
SELECT 
    '教室', 
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Lab%' AND name NOT LIKE '%Hall%' AND building NOT LIKE '%Building%' THEN 1 ELSE 0 END)
FROM classroom;
