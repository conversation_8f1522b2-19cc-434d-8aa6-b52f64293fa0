package com.university.management.service;

import com.university.management.model.entity.Major;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MajorService {
    
    List<Major> findAll();
    
    Page<Major> findAll(PageRequest pageRequest);
    
    Page<Major> findByConditions(String name, Integer departmentId, PageRequest pageRequest);
    
    Optional<Major> findById(Integer id);

    Major save(Major major);

    void deleteById(Integer id);

    List<Major> findByDepartmentId(Integer departmentId);

    // 统计方法
    Map<String, Object> getMajorStats();
}
