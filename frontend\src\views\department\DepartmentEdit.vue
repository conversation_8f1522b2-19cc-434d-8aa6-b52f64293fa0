<template>
  <div class="department-edit-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑院系' : '新增院系' }}</h2>
    </div>

    <el-card class="form-container">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px"
        label-position="right">

        <el-form-item label="院系编号" prop="departmentNo">
          <el-input v-model="form.departmentNo" placeholder="请输入院系编号" />
        </el-form-item>

        <el-form-item label="院系名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入院系名称" />
        </el-form-item>

        <el-form-item label="院长" prop="dean">
          <el-input v-model="form.dean" placeholder="请输入院长" />
        </el-form-item>

        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="form.telephone" placeholder="请输入联系电话" />
        </el-form-item>

        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱" />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入院系地址" />
        </el-form-item>

        <el-form-item label="成立时间" prop="establishedDate">
          <el-date-picker
            v-model="form.establishedDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="院系网站" prop="website">
          <el-input v-model="form.website" placeholder="请输入院系网站" />
        </el-form-item>

        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">正常</el-radio>
            <el-radio :label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="院系简介" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入院系简介" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// 引入API，实际项目中需要添加
// import { getDepartment, createDepartment, updateDepartment } from '@/api/department'

export default {
  name: 'DepartmentEdit',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)
    const departmentId = computed(() => route.params.id)
    const isEdit = computed(() => !!departmentId.value)

    // 表单数据
    const form = reactive({
      departmentNo: '',
      name: '',
      description: '',
      dean: '',
      telephone: '',
      email: '',
      address: '',
      establishedDate: '',
      website: '',
      sort: 0,
      status: 0
    })

    // 表单验证规则
    const rules = reactive({
      departmentNo: [{ required: true, message: '请输入院系编号', trigger: 'blur' }],
      name: [{ required: true, message: '请输入院系名称', trigger: 'blur' }],
      dean: [{ required: true, message: '请输入院长', trigger: 'blur' }],
      telephone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
      email: [
        { required: true, message: '请输入电子邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      establishedDate: [{ required: true, message: '请选择成立时间', trigger: 'change' }]
    })

    // 获取院系详情
    const fetchDepartmentDetail = async (id) => {
      try {
        // 实际项目中这里需要调用API
        // const response = await getDepartment(id)
        // Object.assign(form, response.data)
        ElMessage.info('模拟加载院系数据')
        // 模拟数据
        Object.assign(form, {
          departmentNo: 'CS',
          name: '计算机科学与技术学院',
          description: '计算机科学与技术学院成立于1985年，是国家重点学院。',
          dean: '张教授',
          telephone: '010-12345678',
          email: '<EMAIL>',
          address: '大学校区主楼B区',
          establishedDate: '1985-09-01',
          website: 'http://cs.university.edu',
          sort: 1,
          status: 0
        })
      } catch (error) {
        ElMessage.error('获取院系信息失败')
        console.error(error)
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        
        // 实际项目中这里需要调用API
        if (isEdit.value) {
          // await updateDepartment(departmentId.value, form)
          ElMessage.success('更新院系成功')
        } else {
          // await createDepartment(form)
          ElMessage.success('创建院系成功')
        }
        
        // 返回列表页
        router.push({ name: 'DepartmentList' })
      } catch (error) {
        console.error('表单验证失败', error)
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.back()
    }

    // 组件挂载时，如果是编辑模式，获取院系详情
    onMounted(() => {
      if (isEdit.value) {
        fetchDepartmentDetail(departmentId.value)
      }
    })

    return {
      formRef,
      form,
      rules,
      isEdit,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.department-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-container {
  max-width: 800px;
}
</style> 