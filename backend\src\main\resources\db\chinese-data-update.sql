-- 数据库中文化更新脚本
-- 将所有英文数据更新为中文数据

USE university_management;

-- 1. 更新班级数据为中文
UPDATE class SET name = CASE 
    WHEN name LIKE '%Computer Science%' AND name LIKE '%2020%' AND name LIKE '%Class 1%' THEN '计算机科学与技术2020级1班'
    WHEN name LIKE '%Computer Science%' AND name LIKE '%2020%' AND name LIKE '%Class 2%' THEN '计算机科学与技术2020级2班'
    WHEN name LIKE '%Computer Science%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '计算机科学与技术2021级1班'
    WHEN name LIKE '%Software Engineering%' AND name LIKE '%2020%' AND name LIKE '%Class 1%' THEN '软件工程2020级1班'
    WHEN name LIKE '%Software Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '软件工程2021级1班'
    WHEN name LIKE '%Network Engineering%' AND name LIKE '%2020%' AND name LIKE '%Class 1%' THEN '网络工程2020级1班'
    WHEN name LIKE '%Network Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '网络工程2021级1班'
    WHEN name LIKE '%Artificial Intelligence%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '人工智能2021级1班'
    WHEN name LIKE '%Data Science%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '数据科学与大数据技术2021级1班'
    WHEN name LIKE '%Electronic Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '电子信息工程2021级1班'
    WHEN name LIKE '%Communication Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '通信工程2021级1班'
    WHEN name LIKE '%Automation%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '自动化2021级1班'
    WHEN name LIKE '%Mechanical Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '机械设计制造及其自动化2021级1班'
    WHEN name LIKE '%Mechatronics Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '机械电子工程2021级1班'
    WHEN name LIKE '%Vehicle Engineering%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '车辆工程2021级1班'
    WHEN name LIKE '%Business Administration%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '工商管理2021级1班'
    WHEN name LIKE '%Accounting%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '会计学2021级1班'
    WHEN name LIKE '%Marketing%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '市场营销2021级1班'
    WHEN name LIKE '%International Economics%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '国际经济与贸易2021级1班'
    WHEN name LIKE '%English%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '英语2021级1班'
    WHEN name LIKE '%Japanese%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '日语2021级1班'
    WHEN name LIKE '%Visual Communication%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '视觉传达设计2021级1班'
    WHEN name LIKE '%Environmental Design%' AND name LIKE '%2021%' AND name LIKE '%Class 1%' THEN '环境设计2021级1班'
    ELSE name
END
WHERE name LIKE '%Class%' OR name LIKE '%Engineering%' OR name LIKE '%Science%' OR name LIKE '%Administration%';

-- 2. 更新专业数据为中文
UPDATE major SET 
    name = CASE 
        WHEN name = 'Computer Science and Technology' THEN '计算机科学与技术'
        WHEN name = 'Software Engineering' THEN '软件工程'
        WHEN name = 'Network Engineering' THEN '网络工程'
        WHEN name = 'Artificial Intelligence' THEN '人工智能'
        WHEN name = 'Data Science and Big Data Technology' THEN '数据科学与大数据技术'
        WHEN name = 'Electronic Information Engineering' THEN '电子信息工程'
        WHEN name = 'Communication Engineering' THEN '通信工程'
        WHEN name = 'Automation' THEN '自动化'
        WHEN name = 'Mechanical Design Manufacturing and Automation' THEN '机械设计制造及其自动化'
        WHEN name = 'Mechatronics Engineering' THEN '机械电子工程'
        WHEN name = 'Vehicle Engineering' THEN '车辆工程'
        WHEN name = 'Business Administration' THEN '工商管理'
        WHEN name = 'Accounting' THEN '会计学'
        WHEN name = 'Marketing' THEN '市场营销'
        WHEN name = 'International Economics and Trade' THEN '国际经济与贸易'
        WHEN name = 'English' THEN '英语'
        WHEN name = 'Japanese' THEN '日语'
        WHEN name = 'German' THEN '德语'
        WHEN name = 'French' THEN '法语'
        WHEN name = 'Visual Communication Design' THEN '视觉传达设计'
        WHEN name = 'Environmental Design' THEN '环境设计'
        WHEN name = 'Product Design' THEN '产品设计'
        ELSE name
    END,
    description = CASE 
        WHEN name = 'Computer Science and Technology' OR description LIKE '%computer science%' THEN '培养计算机科学与技术领域的高级专门人才'
        WHEN name = 'Software Engineering' OR description LIKE '%software%' THEN '培养软件开发与工程管理的专业人才'
        WHEN name = 'Network Engineering' OR description LIKE '%network%' THEN '培养网络系统设计与管理的专业人才'
        WHEN name = 'Artificial Intelligence' OR description LIKE '%AI%' THEN '培养人工智能技术应用的专业人才'
        WHEN name = 'Data Science and Big Data Technology' OR description LIKE '%data%' THEN '培养大数据分析与处理的专业人才'
        WHEN name = 'Electronic Information Engineering' OR description LIKE '%electronic%' THEN '培养电子信息系统设计与开发的专业人才'
        WHEN name = 'Communication Engineering' OR description LIKE '%communication%' THEN '培养通信系统设计与维护的专业人才'
        WHEN name = 'Automation' OR description LIKE '%automation%' THEN '培养自动化控制系统的专业人才'
        WHEN name = 'Mechanical Design Manufacturing and Automation' OR description LIKE '%mechanical%' THEN '培养机械设计制造及自动化的专业人才'
        WHEN name = 'Business Administration' OR description LIKE '%business%' THEN '培养企业管理与经营的专业人才'
        WHEN name = 'Accounting' OR description LIKE '%accounting%' THEN '培养财务会计与审计的专业人才'
        WHEN name = 'Marketing' OR description LIKE '%marketing%' THEN '培养市场分析与营销策略的专业人才'
        WHEN name = 'English' OR description LIKE '%English%' THEN '培养英语语言文学与翻译的专业人才'
        WHEN name = 'Visual Communication Design' OR description LIKE '%visual%' THEN '培养视觉设计的专业人才'
        WHEN name = 'Environmental Design' OR description LIKE '%environmental%' THEN '培养环境艺术设计的专业人才'
        ELSE description
    END,
    degree = CASE 
        WHEN degree = 'B.Eng' OR degree = 'Bachelor of Engineering' THEN '工学学士'
        WHEN degree = 'B.Sc' OR degree = 'Bachelor of Science' THEN '理学学士'
        WHEN degree = 'B.A' OR degree = 'Bachelor of Arts' THEN '文学学士'
        WHEN degree = 'B.M' OR degree = 'Bachelor of Management' THEN '管理学学士'
        WHEN degree = 'B.F.A' OR degree = 'Bachelor of Fine Arts' THEN '艺术学学士'
        ELSE degree
    END
WHERE name LIKE '%Engineering%' OR name LIKE '%Science%' OR name LIKE '%Administration%' OR name LIKE '%Design%' OR name LIKE '%English%';

-- 3. 更新课程数据为中文
UPDATE course SET 
    name = CASE 
        WHEN name = 'Programming Fundamentals' THEN '程序设计基础'
        WHEN name = 'Data Structures' THEN '数据结构'
        WHEN name = 'Algorithm Analysis' THEN '算法分析'
        WHEN name = 'Computer Networks' THEN '计算机网络'
        WHEN name = 'Database Systems' THEN '数据库系统'
        WHEN name = 'Operating Systems' THEN '操作系统'
        WHEN name = 'Software Engineering' THEN '软件工程'
        WHEN name = 'Computer Graphics' THEN '计算机图形学'
        WHEN name = 'Artificial Intelligence' THEN '人工智能'
        WHEN name = 'Machine Learning' THEN '机器学习'
        WHEN name = 'Web Development' THEN 'Web开发技术'
        WHEN name = 'Mobile App Development' THEN '移动应用开发'
        WHEN name = 'Network Security' THEN '网络安全'
        WHEN name = 'Digital Signal Processing' THEN '数字信号处理'
        WHEN name = 'Electronic Circuits' THEN '电子电路'
        WHEN name = 'Communication Principles' THEN '通信原理'
        WHEN name = 'Control Systems' THEN '控制系统'
        WHEN name = 'Mechanical Design' THEN '机械设计'
        WHEN name = 'Manufacturing Technology' THEN '制造技术'
        WHEN name = 'Materials Science' THEN '材料科学'
        WHEN name = 'Thermodynamics' THEN '热力学'
        WHEN name = 'Fluid Mechanics' THEN '流体力学'
        WHEN name = 'Management Principles' THEN '管理学原理'
        WHEN name = 'Financial Accounting' THEN '财务会计'
        WHEN name = 'Marketing Management' THEN '营销管理'
        WHEN name = 'International Trade' THEN '国际贸易'
        WHEN name = 'English Literature' THEN '英国文学'
        WHEN name = 'English Grammar' THEN '英语语法'
        WHEN name = 'Translation Theory' THEN '翻译理论'
        WHEN name = 'Design Principles' THEN '设计原理'
        WHEN name = 'Color Theory' THEN '色彩理论'
        WHEN name = 'Typography' THEN '字体设计'
        WHEN name = 'Interior Design' THEN '室内设计'
        WHEN name = 'Landscape Design' THEN '景观设计'
        ELSE name
    END,
    description = CASE 
        WHEN name LIKE '%Programming%' OR description LIKE '%programming%' THEN '计算机程序设计基础课程'
        WHEN name LIKE '%Data Structures%' OR description LIKE '%data structures%' THEN '学习各种数据结构的原理与应用'
        WHEN name LIKE '%Algorithm%' OR description LIKE '%algorithm%' THEN '算法设计与分析方法'
        WHEN name LIKE '%Networks%' OR description LIKE '%network%' THEN '计算机网络原理与技术'
        WHEN name LIKE '%Database%' OR description LIKE '%database%' THEN '数据库理论与实践'
        WHEN name LIKE '%Operating%' OR description LIKE '%operating%' THEN '操作系统原理与应用'
        WHEN name LIKE '%Software Engineering%' OR description LIKE '%software engineering%' THEN '软件工程方法与实践'
        WHEN name LIKE '%Graphics%' OR description LIKE '%graphics%' THEN '计算机图形学基础'
        WHEN name LIKE '%Intelligence%' OR description LIKE '%intelligence%' THEN '人工智能基础理论'
        WHEN name LIKE '%Learning%' OR description LIKE '%learning%' THEN '机器学习算法与应用'
        WHEN name LIKE '%Management%' OR description LIKE '%management%' THEN '管理学基础理论与方法'
        WHEN name LIKE '%Accounting%' OR description LIKE '%accounting%' THEN '会计学基础理论与实务'
        WHEN name LIKE '%Marketing%' OR description LIKE '%marketing%' THEN '市场营销理论与策略'
        WHEN name LIKE '%Design%' OR description LIKE '%design%' THEN '设计理论与实践方法'
        ELSE description
    END
WHERE name LIKE '%Programming%' OR name LIKE '%Data%' OR name LIKE '%Algorithm%' OR name LIKE '%Network%' 
   OR name LIKE '%Database%' OR name LIKE '%System%' OR name LIKE '%Engineering%' OR name LIKE '%Management%'
   OR name LIKE '%Design%' OR name LIKE '%Theory%' OR name LIKE '%Principles%';

-- 4. 更新教室数据为中文
UPDATE classroom SET 
    name = CASE 
        WHEN name = 'Computer Lab 1' THEN '计算机实验室1'
        WHEN name = 'Computer Lab 2' THEN '计算机实验室2'
        WHEN name = 'Computer Lab 3' THEN '计算机实验室3'
        WHEN name LIKE 'Lecture Hall%' THEN CONCAT('阶梯教室', SUBSTRING(name, -4))
        WHEN name = 'AI Research Lab' THEN '人工智能研究实验室'
        WHEN name = 'Software Development Lab' THEN '软件开发实验室'
        WHEN name = 'Network Lab' THEN '网络实验室'
        WHEN name = 'Electronics Lab 1' THEN '电子实验室1'
        WHEN name = 'Electronics Lab 2' THEN '电子实验室2'
        WHEN name = 'Communication Lab' THEN '通信实验室'
        WHEN name = 'Signal Processing Lab' THEN '信号处理实验室'
        WHEN name = 'Control Systems Lab' THEN '控制系统实验室'
        WHEN name = 'Mechanical Workshop' THEN '机械加工车间'
        WHEN name = 'CAD Lab' THEN 'CAD设计实验室'
        WHEN name = 'Materials Testing Lab' THEN '材料测试实验室'
        WHEN name = 'Thermodynamics Lab' THEN '热力学实验室'
        WHEN name = 'Business Simulation Lab' THEN '商务模拟实验室'
        WHEN name = 'Accounting Lab' THEN '会计实验室'
        WHEN name = 'Language Lab 1' THEN '语言实验室1'
        WHEN name = 'Language Lab 2' THEN '语言实验室2'
        WHEN name = 'Translation Lab' THEN '翻译实验室'
        WHEN name = 'Design Studio 1' THEN '设计工作室1'
        WHEN name = 'Design Studio 2' THEN '设计工作室2'
        WHEN name = 'Art Workshop' THEN '艺术创作工作室'
        ELSE name
    END,
    building = CASE 
        WHEN building = 'Teaching Building A' THEN '教学楼A座'
        WHEN building = 'Teaching Building B' THEN '教学楼B座'
        WHEN building = 'Teaching Building C' THEN '教学楼C座'
        WHEN building = 'Teaching Building D' THEN '教学楼D座'
        WHEN building = 'Teaching Building E' THEN '教学楼E座'
        WHEN building = 'Teaching Building F' THEN '教学楼F座'
        WHEN building = 'Laboratory Building' THEN '实验楼'
        WHEN building = 'Engineering Building' THEN '工程楼'
        WHEN building = 'Science Building' THEN '理科楼'
        WHEN building = 'Liberal Arts Building' THEN '文科楼'
        WHEN building = 'Art Building' THEN '艺术楼'
        ELSE building
    END,
    equipment = CASE 
        WHEN equipment LIKE '%Computers%' THEN REPLACE(equipment, 'Computers', '计算机')
        WHEN equipment LIKE '%Projector%' THEN REPLACE(equipment, 'Projector', '投影仪')
        WHEN equipment LIKE '%Air Conditioning%' THEN REPLACE(equipment, 'Air Conditioning', '空调')
        WHEN equipment LIKE '%Sound System%' THEN REPLACE(equipment, 'Sound System', '音响系统')
        WHEN equipment LIKE '%Whiteboard%' THEN REPLACE(equipment, 'Whiteboard', '白板')
        WHEN equipment LIKE '%Laboratory Equipment%' THEN REPLACE(equipment, 'Laboratory Equipment', '实验设备')
        ELSE equipment
    END
WHERE name LIKE '%Lab%' OR name LIKE '%Hall%' OR name LIKE '%Studio%' OR name LIKE '%Workshop%'
   OR building LIKE '%Building%' OR equipment LIKE '%Computers%' OR equipment LIKE '%Projector%';

-- 5. 更新图书数据为中文（如果有英文图书）
UPDATE book SET 
    title = CASE 
        WHEN title LIKE '%Programming%' THEN REPLACE(title, 'Programming', '程序设计')
        WHEN title LIKE '%Computer Science%' THEN REPLACE(title, 'Computer Science', '计算机科学')
        WHEN title LIKE '%Software Engineering%' THEN REPLACE(title, 'Software Engineering', '软件工程')
        WHEN title LIKE '%Data Structures%' THEN REPLACE(title, 'Data Structures', '数据结构')
        WHEN title LIKE '%Algorithms%' THEN REPLACE(title, 'Algorithms', '算法')
        WHEN title LIKE '%Database%' THEN REPLACE(title, 'Database', '数据库')
        WHEN title LIKE '%Networks%' THEN REPLACE(title, 'Networks', '网络')
        WHEN title LIKE '%Management%' THEN REPLACE(title, 'Management', '管理学')
        WHEN title LIKE '%Marketing%' THEN REPLACE(title, 'Marketing', '市场营销')
        WHEN title LIKE '%Accounting%' THEN REPLACE(title, 'Accounting', '会计学')
        WHEN title LIKE '%Design%' THEN REPLACE(title, 'Design', '设计')
        ELSE title
    END
WHERE title LIKE '%Programming%' OR title LIKE '%Computer%' OR title LIKE '%Software%' 
   OR title LIKE '%Data%' OR title LIKE '%Algorithm%' OR title LIKE '%Management%'
   OR title LIKE '%Design%' OR title LIKE '%Engineering%';

-- 提交更改
COMMIT;

-- 显示更新结果统计
SELECT '班级数据中文化完成' AS 更新类型, COUNT(*) AS 更新数量 
FROM class WHERE name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%'
UNION ALL
SELECT '专业数据中文化完成', COUNT(*) 
FROM major WHERE name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' AND name NOT LIKE '%Administration%'
UNION ALL
SELECT '课程数据中文化完成', COUNT(*) 
FROM course WHERE name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' AND name NOT LIKE '%System%'
UNION ALL
SELECT '教室数据中文化完成', COUNT(*) 
FROM classroom WHERE name NOT LIKE '%Lab%' AND name NOT LIKE '%Hall%' AND building NOT LIKE '%Building%';

SELECT '数据中文化更新完成！' AS 状态;
