-- 数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS university_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE university_management;

-- 院系表
CREATE TABLE IF NOT EXISTS department (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    department_no VARCHAR(20) NOT NULL UNIQUE COMMENT '院系编号',
    name VARCHAR(50) NOT NULL COMMENT '院系名称',
    description VARCHAR(500) COMMENT '院系简介',
    dean VARCHAR(20) COMMENT '院长',
    telephone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(50) COMMENT '电子邮箱',
    address VARCHAR(100) COMMENT '地址',
    established_date VARCHAR(20) COMMENT '建立时间',
    website VARCHAR(100) COMMENT '院系网站',
    sort INT COMMENT '排序号',
    status INT DEFAULT 0 COMMENT '状态: 0-正常, 1-停用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '院系表';

-- 专业表
CREATE TABLE IF NOT EXISTS major (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    major_no VARCHAR(20) NOT NULL UNIQUE COMMENT '专业编号',
    name VARCHAR(50) NOT NULL COMMENT '专业名称',
    department_id INT NOT NULL COMMENT '所属院系ID',
    description VARCHAR(500) COMMENT '专业简介',
    length VARCHAR(10) COMMENT '学制',
    degree VARCHAR(20) COMMENT '授予学位',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_department_id (department_id) COMMENT '院系ID索引',
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '专业表';

-- 班级表
CREATE TABLE IF NOT EXISTS class (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    class_no VARCHAR(20) NOT NULL UNIQUE COMMENT '班级编号',
    name VARCHAR(50) NOT NULL COMMENT '班级名称',
    major_id INT NOT NULL COMMENT '所属专业ID',
    department_id INT NOT NULL COMMENT '所属院系ID',
    grade INT COMMENT '年级',
    teacher_id INT COMMENT '班主任ID',
    class_size INT COMMENT '班级人数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_major_id (major_id) COMMENT '专业ID索引',
    INDEX idx_department_id (department_id) COMMENT '院系ID索引',
    INDEX idx_teacher_id (teacher_id) COMMENT '班主任ID索引',
    FOREIGN KEY (major_id) REFERENCES major(id),
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '班级表';

-- 教师表
CREATE TABLE IF NOT EXISTS teacher (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    teacher_no VARCHAR(20) NOT NULL UNIQUE COMMENT '教师工号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender INT(1) COMMENT '性别(0-女，1-男)',
    birthday DATE COMMENT '出生日期',
    age INT COMMENT '年龄',
    title INT COMMENT '职称(0-助教，1-讲师，2-副教授，3-教授)',
    phone VARCHAR(11) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    id_card VARCHAR(18) COMMENT '身份证号',
    college_id INT COMMENT '所属学院ID',
    department_id INT COMMENT '所属院系ID',
    hire_date DATE COMMENT '入职日期',
    status INT COMMENT '教师状态(0-在职，1-离职，2-退休)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_department_id (department_id) COMMENT '院系ID索引',
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '教师表';

-- 学生表
CREATE TABLE IF NOT EXISTS student (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    student_no VARCHAR(20) NOT NULL UNIQUE COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    gender INT(1) COMMENT '性别(0-女，1-男)',
    birthday DATE COMMENT '出生日期',
    age INT COMMENT '年龄',
    id_card VARCHAR(18) COMMENT '身份证号',
    phone VARCHAR(11) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(200) COMMENT '家庭住址',
    class_id INT COMMENT '所属班级ID',
    major_id INT COMMENT '所属专业ID',
    college_id INT COMMENT '所属学院ID',
    department_id INT COMMENT '所属院系ID',
    enroll_year INT COMMENT '入学年份',
    status INT COMMENT '学生状态(0-在读，1-休学，2-退学，3-毕业)',
    dormitory_id INT COMMENT '宿舍ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_class_id (class_id) COMMENT '班级ID索引',
    INDEX idx_major_id (major_id) COMMENT '专业ID索引',
    INDEX idx_department_id (department_id) COMMENT '院系ID索引',
    INDEX idx_dormitory_id (dormitory_id) COMMENT '宿舍ID索引',
    FOREIGN KEY (class_id) REFERENCES class(id),
    FOREIGN KEY (major_id) REFERENCES major(id),
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '学生表';

-- 课程表
CREATE TABLE IF NOT EXISTS course (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    course_no VARCHAR(20) NOT NULL UNIQUE COMMENT '课程编号',
    name VARCHAR(50) NOT NULL COMMENT '课程名称',
    credit DECIMAL(3,1) COMMENT '学分',
    hours INT COMMENT '学时',
    type INT COMMENT '课程类型(0-必修，1-选修)',
    department_id INT COMMENT '所属院系ID',
    description VARCHAR(500) COMMENT '课程描述',
    status INT COMMENT '课程状态(0-正常，1-停开)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_department_id (department_id) COMMENT '院系ID索引',
    FOREIGN KEY (department_id) REFERENCES department(id)
) COMMENT '课程表';

-- 教室表
CREATE TABLE IF NOT EXISTS classroom (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    building VARCHAR(50) COMMENT '教学楼',
    room_no VARCHAR(20) COMMENT '教室号',
    name VARCHAR(100) COMMENT '教室名称',
    floor INT COMMENT '楼层',
    type INT COMMENT '教室类型(0-普通教室，1-多媒体教室，2-实验室)',
    capacity INT COMMENT '容量',
    equipment VARCHAR(500) COMMENT '设备描述',
    status INT COMMENT '状态(0-可用，1-维修中，2-已废弃)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '教室表';

-- 宿舍表
CREATE TABLE IF NOT EXISTS dormitory (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    dormitory_no VARCHAR(20) COMMENT '宿舍编号',
    building VARCHAR(50) COMMENT '宿舍楼',
    floor INT COMMENT '楼层',
    room_no VARCHAR(10) COMMENT '房间号',
    type INT COMMENT '宿舍类型(0-男生宿舍，1-女生宿舍)',
    capacity INT COMMENT '容量',
    occupied INT COMMENT '已住人数',
    manager_id INT COMMENT '管理员ID',
    fee DOUBLE COMMENT '宿舍费用',
    equipment VARCHAR(500) COMMENT '设备描述',
    status INT COMMENT '状态(0-正常，1-维修中，2-已废弃)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '宿舍表';

-- 图书表
CREATE TABLE IF NOT EXISTS book (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    isbn VARCHAR(20) COMMENT 'ISBN',
    title VARCHAR(100) NOT NULL COMMENT '书名',
    author VARCHAR(50) COMMENT '作者',
    publisher VARCHAR(100) COMMENT '出版社',
    publish_date DATE COMMENT '出版日期',
    category INT COMMENT '分类(0-文学，1-科技，2-历史，3-艺术，4-经济，5-哲学，6-其他)',
    total INT COMMENT '总数量',
    available INT COMMENT '可借数量',
    location VARCHAR(50) COMMENT '图书位置',
    description VARCHAR(500) COMMENT '图书描述',
    price DECIMAL(10,2) COMMENT '价格',
    status INT COMMENT '状态(0-正常，1-已下架)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '图书表';

-- 体育场馆表
CREATE TABLE IF NOT EXISTS sports_venue (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '场馆名称',
    type INT COMMENT '场馆类型(0-室内，1-室外)',
    location VARCHAR(100) COMMENT '位置',
    capacity INT COMMENT '容量',
    opening_hours VARCHAR(50) COMMENT '开放时间',
    status INT COMMENT '状态(0-可用，1-维修中，2-已预约)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '体育场馆表';

-- 课程表(排课表)
CREATE TABLE IF NOT EXISTS schedule (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    course_id INT NOT NULL COMMENT '课程ID',
    teacher_id INT NOT NULL COMMENT '教师ID',
    classroom_id INT NOT NULL COMMENT '教室ID',
    semester VARCHAR(20) COMMENT '学期',
    day_of_week INT COMMENT '星期几(1-7)',
    start_time VARCHAR(10) COMMENT '开始时间',
    end_time VARCHAR(10) COMMENT '结束时间',
    start_week INT COMMENT '开始周',
    end_week INT COMMENT '结束周',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_course_id (course_id) COMMENT '课程ID索引',
    INDEX idx_teacher_id (teacher_id) COMMENT '教师ID索引',
    INDEX idx_classroom_id (classroom_id) COMMENT '教室ID索引',
    FOREIGN KEY (course_id) REFERENCES course(id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(id),
    FOREIGN KEY (classroom_id) REFERENCES classroom(id)
) COMMENT '课程表(排课表)';

-- 选课表
CREATE TABLE IF NOT EXISTS student_course (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    student_id INT NOT NULL COMMENT '学生ID',
    schedule_id INT NOT NULL COMMENT '排课ID',
    score DECIMAL(5,2) COMMENT '成绩',
    grade_point DECIMAL(3,2) COMMENT '绩点',
    evaluation VARCHAR(500) COMMENT '评价',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_student_id (student_id) COMMENT '学生ID索引',
    INDEX idx_schedule_id (schedule_id) COMMENT '排课ID索引',
    FOREIGN KEY (student_id) REFERENCES student(id),
    FOREIGN KEY (schedule_id) REFERENCES schedule(id)
) COMMENT '选课表';

-- 借阅记录表
CREATE TABLE IF NOT EXISTS borrow_record (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    student_id INT NOT NULL COMMENT '学生ID',
    book_id INT NOT NULL COMMENT '图书ID',
    borrow_date DATE NOT NULL COMMENT '借阅日期',
    expected_return_date DATE NOT NULL COMMENT '预期归还日期',
    actual_return_date DATE COMMENT '实际归还日期',
    fine DECIMAL(10,2) DEFAULT 0.00 COMMENT '罚款',
    status INT COMMENT '状态(0-借阅中，1-已归还，2-逾期未还)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_student_id (student_id) COMMENT '学生ID索引',
    INDEX idx_book_id (book_id) COMMENT '图书ID索引',
    FOREIGN KEY (student_id) REFERENCES student(id),
    FOREIGN KEY (book_id) REFERENCES book(id)
) COMMENT '借阅记录表';

-- 场馆预约表
CREATE TABLE IF NOT EXISTS venue_booking (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    venue_id INT NOT NULL COMMENT '场馆ID',
    user_id INT NOT NULL COMMENT '用户ID',
    user_type INT COMMENT '用户类型(0-学生，1-教师)',
    booking_date DATE NOT NULL COMMENT '预约日期',
    start_time VARCHAR(10) NOT NULL COMMENT '开始时间',
    end_time VARCHAR(10) NOT NULL COMMENT '结束时间',
    purpose VARCHAR(200) COMMENT '用途',
    status INT COMMENT '状态(0-待审核，1-已批准，2-已拒绝，3-已取消)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_venue_id (venue_id) COMMENT '场馆ID索引',
    FOREIGN KEY (venue_id) REFERENCES sports_venue(id)
) COMMENT '场馆预约表';

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    user_type INT COMMENT '用户类型(0-管理员，1-学生，2-教师)',
    related_id INT COMMENT '关联ID',
    phone VARCHAR(11) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(200) COMMENT '头像',
    status INT COMMENT '状态(0-正常，1-禁用)',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS sys_role (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status INT COMMENT '状态(0-正常，1-禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)'
) COMMENT '角色表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id INT NOT NULL COMMENT '用户ID',
    role_id INT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_role_id (role_id) COMMENT '角色ID索引',
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    FOREIGN KEY (role_id) REFERENCES sys_role(id)
) COMMENT '用户角色关联表';

-- 菜单表 (暂时移除自引用外键约束)
CREATE TABLE IF NOT EXISTS sys_menu (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    parent_id INT DEFAULT 0 COMMENT '父菜单ID',
    name VARCHAR(50) NOT NULL COMMENT '菜单名称',
    path VARCHAR(100) COMMENT '路由地址',
    component VARCHAR(100) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '菜单图标',
    sort INT COMMENT '排序',
    visible INT COMMENT '是否可见(0-可见，1-隐藏)',
    type INT COMMENT '菜单类型(0-目录，1-菜单，2-按钮)',
    permission VARCHAR(100) COMMENT '权限标识',
    status INT COMMENT '状态(0-正常，1-禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted INT DEFAULT 0 COMMENT '是否删除(0-未删除，1-已删除)',
    INDEX idx_parent_id (parent_id) COMMENT '父菜单ID索引'
) COMMENT '菜单表';

-- 角色菜单关联表
CREATE TABLE IF NOT EXISTS sys_role_menu (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    role_id INT NOT NULL COMMENT '角色ID',
    menu_id INT NOT NULL COMMENT '菜单ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_role_id (role_id) COMMENT '角色ID索引',
    INDEX idx_menu_id (menu_id) COMMENT '菜单ID索引',
    FOREIGN KEY (role_id) REFERENCES sys_role(id),
    FOREIGN KEY (menu_id) REFERENCES sys_menu(id)
) COMMENT '角色菜单关联表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS sys_operation_log (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id INT COMMENT '用户ID',
    module VARCHAR(50) COMMENT '操作模块',
    operation VARCHAR(50) COMMENT '操作类型',
    method VARCHAR(200) COMMENT '操作方法',
    params TEXT COMMENT '操作参数',
    ip VARCHAR(50) COMMENT '操作IP',
    status INT COMMENT '操作状态(0-成功，1-失败)',
    error_msg TEXT COMMENT '错误信息',
    operation_time DATETIME COMMENT '操作时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '操作日志表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS sys_login_log (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id INT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    ip VARCHAR(50) COMMENT '登录IP',
    location VARCHAR(100) COMMENT '登录地点',
    browser VARCHAR(50) COMMENT '浏览器类型',
    os VARCHAR(50) COMMENT '操作系统',
    status INT COMMENT '登录状态(0-成功，1-失败)',
    msg VARCHAR(200) COMMENT '提示消息',
    login_time DATETIME COMMENT '登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '登录日志表'; 