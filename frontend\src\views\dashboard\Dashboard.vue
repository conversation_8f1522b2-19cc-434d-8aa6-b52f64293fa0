<template>
  <div class="dashboard-container">
    <h2>仪表盘</h2>

    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="card-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-primary">
            <div class="card-icon">
              <el-icon :size="30"><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">学生总数</div>
              <div class="card-value">{{ statistics.studentCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.studentGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.studentGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-success">
            <div class="card-icon">
              <el-icon :size="30"><Avatar /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">教师总数</div>
              <div class="card-value">{{ statistics.teacherCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.teacherGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.teacherGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-warning">
            <div class="card-icon">
              <el-icon :size="30"><Reading /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">课程总数</div>
              <div class="card-value">{{ statistics.courseCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.courseGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.courseGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card" :body-style="{ padding: '0px' }">
          <div class="card-content bg-danger">
            <div class="card-icon">
              <el-icon :size="30"><School /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">教室总数</div>
              <div class="card-value">{{ statistics.classroomCount }}</div>
            </div>
          </div>
          <div class="card-footer">
            <span>较上月增长 {{ statistics.classroomGrowth }}%</span>
            <i class="el-icon-top" v-if="statistics.classroomGrowth > 0"></i>
            <i class="el-icon-bottom" v-else></i>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第一行图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>学生专业分布</span>
              <el-radio-group v-model="chartFilters.studentMajorType" size="small">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="top5">Top 5</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="studentMajorChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>学生年级分布</span>
              <el-radio-group v-model="chartFilters.studentGradeView" size="small">
                <el-radio-button label="bar">柱状图</el-radio-button>
                <el-radio-button label="pie">饼图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="studentGradeChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>教师部门分布</span>
            </div>
          </template>
          <div id="teacherDepartmentChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>课程学分分布</span>
            </div>
          </template>
          <div id="courseCreditChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第三行图表（趋势图） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>年度趋势分析</span>
              <el-select v-model="chartFilters.trendType" size="small" style="width: 150px">
                <el-option label="学生数量" value="student"></el-option>
                <el-option label="教师数量" value="teacher"></el-option>
                <el-option label="课程数量" value="course"></el-option>
              </el-select>
            </div>
          </template>
          <div id="trendChart" class="chart-container" style="height: 380px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第四行（通知和快捷链接） -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>系统通知</span>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity.timestamp"
              :type="activity.type"
              :color="activity.color"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <span>快捷菜单</span>
            </div>
          </template>
          <div class="quick-links">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(link, index) in quickLinks" :key="index">
                <div class="quick-link-item" @click="navigateTo(link.path)">
                  <el-icon :size="36">
                    <component :is="link.icon" />
                  </el-icon>
                  <span>{{ link.title }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { User, Avatar, Reading, School, TrendCharts, Management, House, Collection } from '@element-plus/icons-vue'

// 模拟 API 数据，实际项目中需要从 API 获取数据
import { getStudentStatsByMajor, getStudentStatsByGrade, getAllStudents } from '@/api/student'
import { getTeacherStatsByDepartment, getAllTeachers } from '@/api/teacher'
import { getCourseStatsByCredit, getAllCourses } from '@/api/course'

export default {
  name: 'Dashboard',
  components: {
    User,
    Avatar,
    Reading,
    School,
    TrendCharts,
    Management,
    House,
    Collection
  },
  setup() {
    const router = useRouter()
    
    // 图表实例
    const charts = reactive({
      studentMajor: null,
      studentGrade: null,
      teacherDepartment: null,
      courseCredit: null,
      trend: null
    })
    
    // 图表过滤器
    const chartFilters = reactive({
      studentMajorType: 'all',
      studentGradeView: 'bar',
      trendType: 'student'
    })
    
    // 统计数据
    const statistics = reactive({
      studentCount: 0,
      studentGrowth: 0,
      teacherCount: 0,
      teacherGrowth: 0,
      courseCount: 0,
      courseGrowth: 0,
      classroomCount: 0,
      classroomGrowth: 0
    })
    
    // 系统通知
    const activities = [
      {
        content: '系统更新：新增课程表导出功能',
        timestamp: '2023-06-25 10:30',
        type: 'primary',
        color: '#409EFF'
      },
      {
        content: '通知：2023学年第一学期课程安排已发布',
        timestamp: '2023-06-23 15:45',
        type: 'success',
        color: '#67C23A'
      },
      {
        content: '提醒：请教师尽快提交期末考试安排',
        timestamp: '2023-06-20 09:15',
        type: 'warning',
        color: '#E6A23C'
      },
      {
        content: '通知：系统将于本周日凌晨进行维护',
        timestamp: '2023-06-18 11:00',
        type: 'info',
        color: '#909399'
      }
    ]
    
    // 快捷链接
    const quickLinks = [
      { title: '学生管理', icon: 'User', path: '/students' },
      { title: '教师管理', icon: 'Avatar', path: '/teachers' },
      { title: '课程管理', icon: 'Reading', path: '/courses' },
      { title: '教室管理', icon: 'School', path: '/classrooms' },
      { title: '宿舍管理', icon: 'House', path: '/dormitories' },
      { title: '图书管理', icon: 'Collection', path: '/books' }
    ]
    
    // 初始化学生专业分布图表
    const initStudentMajorChart = async () => {
      console.log('🔧 初始化学生专业分布图表')
      const chartDom = document.getElementById('studentMajorChart')
      console.log('📊 图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到学生专业分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 容器尺寸为0，延迟初始化')
        setTimeout(() => initStudentMajorChart(), 500)
        return
      }

      charts.studentMajor = echarts.init(chartDom)
      console.log('✅ ECharts实例创建成功:', charts.studentMajor)

      try {
        console.log('📡 开始获取学生专业分布数据')
        const res = await getStudentStatsByMajor()
        console.log('📊 API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 原始数据:', data)
        if (data) {
          // 将专业ID转换为专业名称
          const majorNames = {
            '1': '计算机科学与技术',
            '2': '软件工程',
            '3': '人工智能',
            '4': '网络工程',
            '5': '数学与应用数学',
            '6': '统计学',
            '7': '物理学',
            '8': '应用物理学',
            '9': '化学',
            '10': '生物科学'
          }

          // API返回的数据中key就是专业名称，直接使用
          console.log('🔄 转换后数据:', data)
          updateStudentMajorChart(data)
        }
      } catch (error) {
        console.error('获取学生专业分布数据失败:', error)

        // 显示错误信息，不使用模拟数据
        if (charts.studentMajor) {
          charts.studentMajor.setOption({
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            series: []
          })
        }
      }
    }
    
    // 更新学生专业分布图表
    const updateStudentMajorChart = (data) => {
      console.log('🎨 开始更新学生专业分布图表')
      console.log('📊 图表实例:', charts.studentMajor)
      console.log('📈 输入数据:', data)
      if (!charts.studentMajor) {
        console.error('❌ 图表实例不存在')
        return
      }

      let chartData = Object.entries(data).map(([name, value]) => ({ name, value }))
      console.log('🔄 处理后的图表数据:', chartData)
      
      // 根据过滤条件处理数据
      if (chartFilters.studentMajorType === 'top5') {
        chartData.sort((a, b) => b.value - a.value)
        chartData = chartData.slice(0, 5)
      }
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: chartData.map(item => item.name)
        },
        series: [
          {
            name: '专业分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
      
      console.log('⚙️ 图表配置:', option)
      charts.studentMajor.setOption(option)
      console.log('✅ 图表配置已设置')

      // 手动触发resize确保图表正确显示
      setTimeout(() => {
        charts.studentMajor.resize()
        console.log('🔄 图表已resize')
      }, 100)
    }
    
    // 初始化学生年级分布图表
    const initStudentGradeChart = async () => {
      console.log('🔧 初始化学生年级分布图表')
      const chartDom = document.getElementById('studentGradeChart')
      console.log('📊 年级图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到学生年级分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 年级图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 年级图表容器尺寸为0，延迟初始化')
        setTimeout(() => initStudentGradeChart(), 500)
        return
      }

      charts.studentGrade = echarts.init(chartDom)
      console.log('✅ 年级图表ECharts实例创建成功:', charts.studentGrade)

      try {
        console.log('📡 开始获取学生年级分布数据')
        const res = await getStudentStatsByGrade()
        console.log('📊 年级API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 年级原始数据:', data)
        if (data) {
          // 将年份转换为年级名称
          const convertedData = {}
          Object.entries(data).forEach(([year, count]) => {
            convertedData[`${year}级`] = count
          })
          console.log('🔄 年级转换后数据:', convertedData)

          updateStudentGradeChart(convertedData)
        }
      } catch (error) {
        console.error('获取学生年级分布数据失败:', error)

        // 显示错误信息，不使用模拟数据
        if (charts.studentGrade) {
          charts.studentGrade.setOption({
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            series: []
          })
        }
      }
    }
    
    // 更新学生年级分布图表
    const updateStudentGradeChart = (data) => {
      console.log('🎨 开始更新学生年级分布图表')
      console.log('📊 年级图表实例:', charts.studentGrade)
      console.log('📈 年级输入数据:', data)
      if (!charts.studentGrade) {
        console.error('❌ 年级图表实例不存在')
        return
      }

      const keys = Object.keys(data)
      const values = Object.values(data)
      console.log('🔄 年级处理后的数据:', { keys, values })

      let option
      
      if (chartFilters.studentGradeView === 'bar') {
        option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'category',
            data: keys,
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '学生人数',
              type: 'bar',
              data: values,
              itemStyle: {
                color: '#91cc75'
              }
            }
          ]
        }
      } else {
        option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: keys
          },
          series: [
            {
              name: '年级分布',
              type: 'pie',
              radius: '70%',
              data: keys.map((key, index) => ({
                name: key,
                value: values[index]
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      }

      console.log('⚙️ 年级图表配置:', option)
      charts.studentGrade.setOption(option)
      console.log('✅ 年级图表配置已设置')

      // 强制resize
      setTimeout(() => {
        charts.studentGrade.resize()
        console.log('🔄 年级图表已resize')
      }, 100)
    }
    
    // 初始化教师部门分布图表
    const initTeacherDepartmentChart = async () => {
      console.log('🔧 初始化教师部门分布图表')
      const chartDom = document.getElementById('teacherDepartmentChart')
      console.log('📊 部门图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到教师部门分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 部门图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 部门图表容器尺寸为0，延迟初始化')
        setTimeout(() => initTeacherDepartmentChart(), 500)
        return
      }

      charts.teacherDepartment = echarts.init(chartDom)
      console.log('✅ 部门图表ECharts实例创建成功:', charts.teacherDepartment)

      try {
        console.log('📡 开始获取教师部门分布数据')
        const res = await getTeacherStatsByDepartment()
        console.log('📊 部门API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 部门原始数据:', data)
        if (data) {
          // 将部门ID转换为部门名称
          const departmentNames = {
            '1': '计算机学院',
            '2': '数学学院',
            '3': '物理学院',
            '4': '化学学院',
            '5': '生物学院',
            '6': '外语学院',
            '7': '经济学院',
            '8': '管理学院',
            '9': '文学院',
            '10': '法学院'
          }

          const chartData = Object.entries(data).map(([id, value]) => ({
            name: departmentNames[id] || `部门${id}`,
            value
          }))
          console.log('🔄 部门转换后数据:', chartData)
          console.log('🎨 开始更新教师部门分布图表')
          console.log('📊 部门图表实例:', charts.teacherDepartment)

          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 20,
              bottom: 20,
              data: chartData.map(item => item.name)
            },
            series: [
              {
                name: '部门分布',
                type: 'pie',
                radius: '70%',
                center: ['40%', '50%'],
                data: chartData,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                label: {
                  formatter: '{b}: {c} ({d}%)'
                }
              }
            ]
          }

          console.log('⚙️ 部门图表配置:', option)
          charts.teacherDepartment.setOption(option)
          console.log('✅ 部门图表配置已设置')

          // 强制resize
          setTimeout(() => {
            charts.teacherDepartment.resize()
            console.log('🔄 部门图表已resize')
          }, 100)
        }
      } catch (error) {
        console.error('获取教师部门分布数据失败:', error)

        // 显示错误信息，不使用模拟数据
        if (charts.teacherDepartment) {
          charts.teacherDepartment.setOption({
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            series: []
          })
        }
      }
    }
    
    // 初始化课程学分分布图表
    const initCourseCreditChart = async () => {
      console.log('🔧 初始化课程学分分布图表')
      const chartDom = document.getElementById('courseCreditChart')
      console.log('📊 学分图表容器:', chartDom)
      if (!chartDom) {
        console.error('❌ 找不到课程学分分布图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 等待容器完全渲染
      await nextTick()

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 学分图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 学分图表容器尺寸为0，延迟初始化')
        setTimeout(() => initCourseCreditChart(), 500)
        return
      }

      charts.courseCredit = echarts.init(chartDom)
      console.log('✅ 学分图表ECharts实例创建成功:', charts.courseCredit)

      try {
        console.log('📡 开始获取课程学分分布数据')
        const res = await getCourseStatsByCredit()
        console.log('📊 学分API响应:', res)
        // 检查数据格式，可能直接是数据对象，也可能在res.data中
        const data = res.data || res
        console.log('📈 学分原始数据:', data)
        if (data) {
          const keys = Object.keys(data)
          const values = Object.values(data)
          console.log('🔄 学分数据处理:', { keys, values })
          console.log('🎨 开始更新课程学分分布图表')
          console.log('📊 学分图表实例:', charts.courseCredit)

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            xAxis: {
              type: 'category',
              data: keys,
              axisLabel: {
                interval: 0
              }
            },
            yAxis: {
              type: 'value',
              name: '课程数量'
            },
            series: [
              {
                name: '课程数量',
                type: 'bar',
                data: values,
                itemStyle: {
                  color: '#5470c6'
                }
              }
            ]
          }

          console.log('⚙️ 学分图表配置:', option)
          charts.courseCredit.setOption(option)
          console.log('✅ 学分图表配置已设置')

          // 强制resize
          setTimeout(() => {
            charts.courseCredit.resize()
            console.log('🔄 学分图表已resize')
          }, 100)
        }
      } catch (error) {
        console.error('获取课程学分分布数据失败:', error)

        // 显示错误信息，不使用模拟数据
        if (charts.courseCredit) {
          charts.courseCredit.setOption({
            title: {
              text: '数据加载失败',
              subtext: '请检查后端服务是否正常',
              left: 'center',
              top: 'middle',
              textStyle: {
                color: '#999',
                fontSize: 16
              }
            },
            series: []
          })
        }
      }
    }
    
    // 加载真实统计数据
    const loadRealStatistics = async () => {
      console.log('🔧 开始加载真实统计数据')
      try {
        // 获取学生总数
        const studentsRes = await getAllStudents()
        const students = studentsRes.data || studentsRes || []
        statistics.studentCount = students.length
        console.log('📊 学生总数:', statistics.studentCount)

        // 获取教师总数
        const teachersRes = await getAllTeachers()
        const teachers = teachersRes.data || teachersRes || []
        statistics.teacherCount = teachers.length
        console.log('📊 教师总数:', statistics.teacherCount)

        // 获取课程总数
        const coursesRes = await getAllCourses()
        const courses = coursesRes.data || coursesRes || []
        statistics.courseCount = courses.length
        console.log('📊 课程总数:', statistics.courseCount)

        // 暂时保持教室数据为模拟数据，因为没有对应的API
        statistics.classroomCount = 86
        console.log('📊 教室总数:', statistics.classroomCount, '(模拟数据)')

        console.log('✅ 统计数据加载完成')
      } catch (error) {
        console.error('❌ 加载统计数据失败:', error)
        // 显示错误状态，不使用模拟数据
        statistics.studentCount = 0
        statistics.teacherCount = 0
        statistics.courseCount = 0
        statistics.classroomCount = 0
      }
    }

    // 初始化趋势图表
    const initTrendChart = () => {
      const chartDom = document.getElementById('trendChart')
      if (!chartDom) {
        console.error('❌ 找不到趋势图表容器')
        return
      }

      // 确保容器有明确的尺寸
      chartDom.style.width = '100%'
      chartDom.style.height = '300px'
      chartDom.style.minHeight = '300px'
      chartDom.style.display = 'block'

      // 检查容器实际尺寸
      const rect = chartDom.getBoundingClientRect()
      console.log('📏 趋势图表容器实际尺寸:', { width: rect.width, height: rect.height })

      if (rect.width === 0 || rect.height === 0) {
        console.error('❌ 趋势图表容器尺寸为0，延迟初始化')
        setTimeout(() => initTrendChart(), 500)
        return
      }

      charts.trend = echarts.init(chartDom)
      console.log('✅ 趋势图表ECharts实例创建成功:', charts.trend)
      updateTrendChart()
    }
    
    // 更新趋势图表数据
    const updateTrendChart = () => {
      if (!charts.trend) return
      
      // 模拟趋势数据
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let data
      let title = ''
      
      if (chartFilters.trendType === 'student') {
        data = [1150, 1158, 1165, 1170, 1182, 1190, 1195, 1205, 1220, 1235, 1245, 1256]
        title = '学生数量趋势'
      } else if (chartFilters.trendType === 'teacher') {
        data = [160, 161, 162, 162, 163, 164, 164, 165, 166, 167, 168, 168]
        title = '教师数量趋势'
      } else {
        data = [305, 307, 310, 312, 315, 315, 318, 320, 322, 325, 326, 327]
        title = '课程数量趋势'
      }
      
      const option = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: months
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: title,
            type: 'line',
            data: data,
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ]
            },
            markLine: {
              data: [{ type: 'average', name: '平均值' }]
            },
            smooth: true,
            lineStyle: {
              width: 3
            },
            areaStyle: {
              opacity: 0.2
            }
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      }
      
      charts.trend.setOption(option)
    }
    
    // 导航到指定路径
    const navigateTo = (path) => {
      router.push(path)
    }
    
    // 监听窗口大小变化，重新绘制图表
    const handleResize = () => {
      Object.values(charts).forEach(chart => {
        if (chart) {
          try {
            chart.resize()
          } catch (error) {
            console.error('❌ 图表resize失败:', error)
          }
        }
      })
    }

    // 图表错误恢复机制
    const recoverCharts = () => {
      console.log('🔄 尝试恢复图表')
      const chartIds = ['studentMajorChart', 'studentGradeChart', 'teacherDepartmentChart', 'courseCreditChart', 'trendChart']

      chartIds.forEach(id => {
        const element = document.getElementById(id)
        if (element && (!charts[id.replace('Chart', '')] || charts[id.replace('Chart', '')].isDisposed())) {
          console.log(`🔧 恢复图表: ${id}`)
          setTimeout(() => {
            if (id === 'studentMajorChart') initStudentMajorChart()
            else if (id === 'studentGradeChart') initStudentGradeChart()
            else if (id === 'teacherDepartmentChart') initTeacherDepartmentChart()
            else if (id === 'courseCreditChart') initCourseCreditChart()
            else if (id === 'trendChart') initTrendChart()
          }, 100)
        }
      })
    }
    
    // 监听图表过滤器变化
    watch(() => chartFilters.studentMajorType, async () => {
      try {
        const res = await getStudentStatsByMajor()
        if (res.data) {
          updateStudentMajorChart(res.data)
        }
      } catch (error) {
        console.error('获取学生专业分布数据失败:', error)
        // 不使用模拟数据，让图表显示错误状态
      }
    })
    
    watch(() => chartFilters.studentGradeView, async () => {
      try {
        const res = await getStudentStatsByGrade()
        if (res.data) {
          updateStudentGradeChart(res.data)
        }
      } catch (error) {
        console.error('获取学生年级分布数据失败:', error)
        // 不使用模拟数据，让图表显示错误状态
      }
    })
    
    watch(() => chartFilters.trendType, () => {
      updateTrendChart()
    })
    
    // 初始化所有图表的函数
    const initAllCharts = async () => {
      console.log('🚀 开始初始化所有图表')

      // 检查所有图表容器是否存在
      const containers = [
        'studentMajorChart',
        'studentGradeChart',
        'teacherDepartmentChart',
        'courseCreditChart',
        'trendChart'
      ]

      let allContainersReady = true
      containers.forEach(id => {
        const element = document.getElementById(id)
        console.log(`📦 容器 ${id}:`, element)
        if (element) {
          const rect = element.getBoundingClientRect()
          console.log(`📏 容器 ${id} 尺寸:`, {
            width: rect.width,
            height: rect.height,
            display: getComputedStyle(element).display,
            visibility: getComputedStyle(element).visibility
          })
          if (rect.width === 0 || rect.height === 0) {
            allContainersReady = false
          }
        } else {
          allContainersReady = false
        }
      })

      if (!allContainersReady) {
        console.log('⏳ 容器未完全准备好，延迟初始化')
        setTimeout(initAllCharts, 200)
        return
      }

      // 依次初始化图表
      try {
        await initStudentMajorChart()
        await initStudentGradeChart()
        await initTeacherDepartmentChart()
        await initCourseCreditChart()
        initTrendChart()
        console.log('✅ 所有图表初始化完成')
      } catch (error) {
        console.error('❌ 图表初始化失败:', error)
      }
    }

    // 组件挂载后初始化图表
    onMounted(async () => {
      await nextTick()

      // 先加载真实统计数据
      await loadRealStatistics()

      // 使用requestAnimationFrame确保DOM完全渲染
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          initAllCharts()
        })
      })

      window.addEventListener('resize', handleResize)
    })
    
    // 组件卸载前清理图表实例和事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      
      Object.values(charts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
    })
    
    return {
      statistics,
      activities,
      quickLinks,
      chartFilters,
      navigateTo
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.card-row {
  margin-bottom: 20px;
}

.data-card {
  height: 120px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-content {
  height: 85px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.bg-primary {
  background-color: #409EFF;
  color: white;
}

.bg-success {
  background-color: #67C23A;
  color: white;
}

.bg-warning {
  background-color: #E6A23C;
  color: white;
}

.bg-danger {
  background-color: #F56C6C;
  color: white;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 16px;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
}

.card-footer {
  background-color: #f5f7fa;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #606266;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 10px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  min-height: 300px;
  width: 100%;
  position: relative;
  display: block;
}

/* 确保所有图表容器都有正确的尺寸 */
#studentMajorChart,
#studentGradeChart,
#teacherDepartmentChart,
#courseCreditChart,
#trendChart {
  width: 100% !important;
  height: 300px !important;
  min-height: 300px !important;
  display: block !important;
}

.quick-links {
  padding: 10px 0;
}

.quick-link-item {
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.quick-link-item:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.quick-link-item .el-icon {
  color: #409EFF;
  margin-bottom: 10px;
}

.quick-link-item span {
  font-size: 14px;
}

@media (max-width: 768px) {
  .data-card {
    margin-bottom: 20px;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style> 