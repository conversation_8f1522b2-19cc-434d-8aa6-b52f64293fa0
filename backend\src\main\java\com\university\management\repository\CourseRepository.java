package com.university.management.repository;

import com.university.management.model.entity.Course;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CourseRepository extends JpaRepository<Course, Integer> {
    
    Optional<Course> findByCourseNo(String courseNo);
    
    Page<Course> findByNameContaining(String name, Pageable pageable);
    
    @Query("SELECT c FROM Course c WHERE " +
           "(:name IS NULL OR c.name LIKE %:name%) AND " +
           "(:department IS NULL OR c.collegeId = :department) AND " +
           "(:credits IS NULL OR c.credit = :credits)")
    Page<Course> findByConditions(
            @Param("name") String name, 
            @Param("department") String department, 
            @Param("credits") Integer credits, 
            Pageable pageable);
    
    List<Course> findByCollegeId(Integer collegeId);
    
    boolean existsByCourseNo(String courseNo);
} 