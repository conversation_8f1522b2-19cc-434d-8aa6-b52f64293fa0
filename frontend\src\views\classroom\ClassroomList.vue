<template>
  <div class="classroom-list-container">
    <div class="page-header">
      <h2>教室管理</h2>
      <el-button type="primary" @click="handleAddClassroom">新增教室</el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="教学楼">
          <el-input v-model="queryParams.building" placeholder="请输入教学楼" clearable />
        </el-form-item>
        <el-form-item label="教室类型">
          <el-select v-model="queryParams.type" placeholder="请选择教室类型" clearable>
            <el-option label="普通教室" :value="0" />
            <el-option label="多媒体教室" :value="1" />
            <el-option label="实验室" :value="2" />
            <el-option label="会议室" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="容纳人数">
          <el-input-number v-model="queryParams.capacity" :min="0" placeholder="最小容纳人数" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container">
      <el-table :data="classroomList" border v-loading="loading" style="width: 100%">
        <el-table-column prop="roomNo" label="教室编号" align="center" />
        <el-table-column prop="name" label="教室名称" align="center" />
        <el-table-column prop="building" label="教学楼" align="center" />
        <el-table-column prop="floor" label="楼层" align="center" />
        <el-table-column label="教室类型" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 0">普通教室</el-tag>
            <el-tag v-else-if="scope.row.type === 1" type="success">多媒体教室</el-tag>
            <el-tag v-else-if="scope.row.type === 2" type="warning">实验室</el-tag>
            <el-tag v-else-if="scope.row.type === 3" type="info">会议室</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容纳人数" align="center" />
        <el-table-column label="状态" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 0" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === 1" type="warning">维修中</el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="danger">已废弃</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据定义
const loading = ref(false)
const classroomList = ref([])
const total = ref(0)
const queryParams = reactive({
  building: '',
  type: null,
  capacity: null,
  pageNum: 1,
  pageSize: 10
})

// 生命周期钩子
onMounted(() => {
  getClassroomList()
})

// 方法定义
const getClassroomList = () => {
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    classroomList.value = [
      {
        id: 1,
        roomNo: 'A101',
        name: '教室A101',
        building: 'A楼',
        floor: 1,
        type: 0,
        capacity: 60,
        status: 0
      },
      {
        id: 2,
        roomNo: 'B201',
        name: '多媒体教室B201',
        building: 'B楼',
        floor: 2,
        type: 1,
        capacity: 120,
        status: 0
      },
      {
        id: 3,
        roomNo: 'C305',
        name: '物理实验室C305',
        building: 'C楼',
        floor: 3,
        type: 2,
        capacity: 50,
        status: 1
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getClassroomList()
}

const resetQuery = () => {
  queryParams.building = ''
  queryParams.type = null
  queryParams.capacity = null
  queryParams.pageNum = 1
  handleQuery()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  getClassroomList()
}

const handleCurrentChange = (currentPage) => {
  queryParams.pageNum = currentPage
  getClassroomList()
}

const handleAddClassroom = () => {
  ElMessage.info('新增教室功能待实现')
}

const handleEdit = (row) => {
  ElMessage.info(`编辑教室: ${row.roomNo} 功能待实现`)
}

const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除教室 ${row.roomNo} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`删除教室: ${row.roomNo} 成功`)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}
</script>

<style scoped>
.classroom-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 