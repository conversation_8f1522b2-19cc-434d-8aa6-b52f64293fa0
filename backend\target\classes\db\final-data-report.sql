-- 最终数据统计报告
USE university_management;

-- 生成完整的数据统计报告
SELECT '=== 大学学生管理系统数据统计报告 ===' as report_title;

SELECT 
    '基础数据统计' as category,
    'department' as table_name, 
    '院系' as description,
    COUNT(*) as count 
FROM department 
UNION ALL
SELECT 
    '基础数据统计',
    'major',
    '专业',
    COUNT(*) 
FROM major 
UNION ALL
SELECT 
    '基础数据统计',
    'class',
    '班级',
    COUNT(*) 
FROM class
UNION ALL
SELECT 
    '人员数据统计',
    'student',
    '学生',
    COUNT(*) 
FROM student 
UNION ALL
SELECT 
    '人员数据统计',
    'teacher',
    '教师',
    COUNT(*) 
FROM teacher
UNION ALL
SELECT 
    '教学数据统计',
    'course',
    '课程',
    COUNT(*) 
FROM course 
UNION ALL
SELECT 
    '教学数据统计',
    'schedule',
    '课程表',
    COUNT(*) 
FROM schedule
UNION ALL
SELECT 
    '教学数据统计',
    'student_course',
    '学生选课',
    COUNT(*) 
FROM student_course
UNION ALL
SELECT 
    '图书数据统计',
    'book',
    '图书',
    COUNT(*) 
FROM book 
UNION ALL
SELECT 
    '图书数据统计',
    'borrow_record',
    '借阅记录',
    COUNT(*) 
FROM borrow_record
UNION ALL
SELECT 
    '设施数据统计',
    'classroom',
    '教室',
    COUNT(*) 
FROM classroom 
UNION ALL
SELECT 
    '设施数据统计',
    'dormitory',
    '宿舍',
    COUNT(*) 
FROM dormitory
UNION ALL
SELECT 
    '设施数据统计',
    'sports_venue',
    '体育场馆',
    COUNT(*) 
FROM sports_venue
UNION ALL
SELECT 
    '设施数据统计',
    'venue_booking',
    '场馆预订',
    COUNT(*) 
FROM venue_booking;

-- 详细统计信息
SELECT '=== 详细统计信息 ===' as detail_title;

-- 按院系统计学生数量
SELECT 
    '按院系统计学生' as stat_type,
    d.name as department_name,
    COUNT(s.id) as student_count
FROM department d
LEFT JOIN student s ON d.id = s.department_id
GROUP BY d.id, d.name
ORDER BY student_count DESC;

-- 按专业统计学生数量
SELECT 
    '按专业统计学生' as stat_type,
    m.name as major_name,
    COUNT(s.id) as student_count
FROM major m
LEFT JOIN student s ON m.id = s.major_id
GROUP BY m.id, m.name
ORDER BY student_count DESC;

-- 按年级统计学生数量
SELECT 
    '按年级统计学生' as stat_type,
    s.enroll_year as enroll_year,
    COUNT(s.id) as student_count
FROM student s
GROUP BY s.enroll_year
ORDER BY s.enroll_year;

-- 按院系统计教师数量
SELECT 
    '按院系统计教师' as stat_type,
    d.name as department_name,
    COUNT(t.id) as teacher_count
FROM department d
LEFT JOIN teacher t ON d.id = t.department_id
GROUP BY d.id, d.name
ORDER BY teacher_count DESC;

-- 按院系统计课程数量
SELECT 
    '按院系统计课程' as stat_type,
    d.name as department_name,
    COUNT(c.id) as course_count
FROM department d
LEFT JOIN course c ON d.id = c.department_id
GROUP BY d.id, d.name
ORDER BY course_count DESC;

-- 图书分类统计
SELECT 
    '按分类统计图书' as stat_type,
    CASE 
        WHEN category = 0 THEN '语言文学'
        WHEN category = 1 THEN '计算机科学'
        WHEN category = 2 THEN '通识教育'
        WHEN category = 3 THEN '艺术设计'
        WHEN category = 4 THEN '经济管理'
        ELSE '其他'
    END as category_name,
    COUNT(*) as book_count,
    SUM(total) as total_copies,
    SUM(available) as available_copies
FROM book
GROUP BY category
ORDER BY book_count DESC;

SELECT '=== 数据完整性检查 ===' as integrity_title;

-- 检查学生是否都分配了班级
SELECT 
    '学生班级分配检查' as check_type,
    COUNT(*) as total_students,
    COUNT(class_id) as students_with_class,
    (COUNT(*) - COUNT(class_id)) as students_without_class
FROM student;

-- 检查课程是否都有课程表
SELECT 
    '课程表安排检查' as check_type,
    COUNT(DISTINCT c.id) as total_courses,
    COUNT(DISTINCT s.course_id) as courses_with_schedule,
    (COUNT(DISTINCT c.id) - COUNT(DISTINCT s.course_id)) as courses_without_schedule
FROM course c
LEFT JOIN schedule s ON c.id = s.course_id;

SELECT '=== 报告生成完成 ===' as report_end;
