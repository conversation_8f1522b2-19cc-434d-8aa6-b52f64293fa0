package com.university.management.service;

import com.university.management.model.entity.Classroom;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 教室服务接口
 */
public interface ClassroomService {

    /**
     * 保存教室
     */
    Classroom save(Classroom classroom);

    /**
     * 根据ID查找教室
     */
    Optional<Classroom> findById(Integer id);

    /**
     * 获取所有教室
     */
    List<Classroom> findAll();

    /**
     * 分页获取教室
     */
    Page<Classroom> findAll(Pageable pageable);

    /**
     * 根据ID删除教室
     */
    void deleteById(Integer id);
} 