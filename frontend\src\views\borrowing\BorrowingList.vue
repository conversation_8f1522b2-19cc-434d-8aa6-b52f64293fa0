<template>
  <div class="borrowing-list-container">
    <div class="header">
      <h2><el-icon><Reading /></el-icon> 借阅记录管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增借阅
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="学生姓名">
          <el-input v-model="searchForm.studentName" placeholder="请输入学生姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="图书名称">
          <el-input v-model="searchForm.bookName" placeholder="请输入图书名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="借阅状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="已借出" value="BORROWED"></el-option>
            <el-option label="已归还" value="RETURNED"></el-option>
            <el-option label="逾期" value="OVERDUE"></el-option>
            <el-option label="续借" value="RENEWED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="借阅日期">
          <el-date-picker
            v-model="searchForm.borrowDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table 
        v-loading="loading" 
        :data="borrowingList" 
        border 
        style="width: 100%" 
        row-key="id"
        @sort-change="handleSortChange">
        <el-table-column prop="studentName" label="学生姓名" width="120"></el-table-column>
        <el-table-column prop="studentNo" label="学号" width="120"></el-table-column>
        <el-table-column prop="bookTitle" label="图书名称" width="200"></el-table-column>
        <el-table-column prop="bookIsbn" label="ISBN" width="150"></el-table-column>
        <el-table-column prop="borrowDate" label="借阅日期" width="120" sortable="custom">
          <template #default="scope">
            {{ formatDate(scope.row.borrowDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="应还日期" width="120" sortable="custom">
          <template #default="scope">
            <span :class="{ 'overdue': isOverdue(scope.row.dueDate, scope.row.status) }">
              {{ formatDate(scope.row.dueDate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="returnDate" label="归还日期" width="120">
          <template #default="scope">
            {{ scope.row.returnDate ? formatDate(scope.row.returnDate) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="renewCount" label="续借次数" width="100">
          <template #default="scope">
            {{ scope.row.renewCount || 0 }}次
          </template>
        </el-table-column>
        <el-table-column prop="fine" label="罚金" width="100">
          <template #default="scope">
            <span v-if="scope.row.fine > 0" class="fine-amount">
              ¥{{ scope.row.fine.toFixed(2) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'BORROWED'" 
              size="small" 
              type="success" 
              @click="handleReturn(scope.row)">
              <el-icon><Check /></el-icon>
              归还
            </el-button>
            <el-button 
              v-if="scope.row.status === 'BORROWED' && scope.row.renewCount < 2" 
              size="small" 
              type="warning" 
              @click="handleRenew(scope.row)">
              <el-icon><RefreshRight /></el-icon>
              续借
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <h3><el-icon><PieChart /></el-icon> 借阅状态分布</h3>
        <div id="statusChart" style="width: 100%; height: 300px;"></div>
      </div>
      <div class="chart-container">
        <h3><el-icon><DataLine /></el-icon> 借阅趋势</h3>
        <div id="trendChart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Check, RefreshRight, Delete, Reading, PieChart, DataLine } from '@element-plus/icons-vue'
import { searchBorrowingRecords, deleteBorrowingRecord, returnBook, renewBook, getBorrowingStats } from '@/api/borrowing'
import * as echarts from 'echarts'

export default {
  name: 'BorrowingList',
  components: {
    Plus, Search, Refresh, View, Check, RefreshRight, Delete, Reading, PieChart, DataLine
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const borrowingList = ref([])
    
    // 搜索表单
    const searchForm = reactive({
      studentName: '',
      bookName: '',
      status: '',
      borrowDateRange: []
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 排序信息
    const sortInfo = reactive({
      prop: '',
      order: ''
    })

    // 图表实例
    const statusChart = ref(null)
    const trendChart = ref(null)

    // 获取借阅记录列表数据
    const fetchBorrowingData = async () => {
      loading.value = true
      try {
        const params = {
          studentName: searchForm.studentName || null,
          bookName: searchForm.bookName || null,
          status: searchForm.status || null,
          borrowDateStart: searchForm.borrowDateRange?.[0] || null,
          borrowDateEnd: searchForm.borrowDateRange?.[1] || null,
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        }

        const res = await searchBorrowingRecords(params)
        if (res) {
          borrowingList.value = res.content || []
          pagination.total = res.totalElements || 0
        }
      } catch (error) {
        console.error('获取借阅记录失败:', error)

        // 使用模拟借阅记录数据
        const mockBorrowingRecords = [
          {
            id: 1,
            studentName: '张小明',
            studentNumber: 'S20210001',
            bookTitle: 'Java程序设计',
            isbn: '9787302123456',
            borrowDate: '2024-09-16',
            dueDate: '2024-09-30',
            returnDate: '2024-09-25',
            status: '已归还',
            renewCount: 1,
            fine: 0
          },
          {
            id: 2,
            studentName: '李小红',
            studentNumber: 'S20210002',
            bookTitle: '数据结构与算法',
            isbn: '9787111234567',
            borrowDate: '2024-04-17',
            dueDate: '2024-05-01',
            returnDate: null,
            status: '逾期',
            renewCount: 0,
            fine: 220.00
          },
          {
            id: 3,
            studentName: '王小刚',
            studentNumber: 'S20210003',
            bookTitle: '计算机网络',
            isbn: '9787121345678',
            borrowDate: '2024-03-18',
            dueDate: '2024-04-01',
            returnDate: null,
            status: '逾期',
            renewCount: 1,
            fine: 235.00
          },
          {
            id: 4,
            studentName: '赵小芳',
            studentNumber: 'S20210004',
            bookTitle: '高等数学',
            isbn: '9787040456789',
            borrowDate: '2024-07-04',
            dueDate: '2024-07-18',
            returnDate: '2024-07-06',
            status: '已归还',
            renewCount: 2,
            fine: 0
          },
          {
            id: 5,
            studentName: '刘小强',
            studentNumber: 'S20220005',
            bookTitle: '线性代数',
            isbn: '9787040567890',
            borrowDate: '2024-05-04',
            dueDate: '2024-05-18',
            returnDate: '2024-05-23',
            status: '已归还',
            renewCount: 2,
            fine: 0
          },
          {
            id: 6,
            studentName: '陈小军',
            studentNumber: 'S20220006',
            bookTitle: '大学物理',
            isbn: '9787030678901',
            borrowDate: '2024-04-11',
            dueDate: '2024-04-25',
            returnDate: '2024-04-12',
            status: '已归还',
            renewCount: 2,
            fine: 0
          },
          {
            id: 7,
            studentName: '杨小敏',
            studentNumber: 'S20220007',
            bookTitle: '有机化学',
            isbn: '9787122789012',
            borrowDate: '2024-12-16',
            dueDate: '2024-12-30',
            returnDate: '2024-12-27',
            status: '已归还',
            renewCount: 2,
            fine: 0
          },
          {
            id: 8,
            studentName: '黄小静',
            studentNumber: 'S20230008',
            bookTitle: '生物学概论',
            isbn: '9787107890123',
            borrowDate: '2024-09-02',
            dueDate: '2024-09-16',
            returnDate: '2024-09-05',
            status: '已归还',
            renewCount: 0,
            fine: 0
          },
          {
            id: 9,
            studentName: '周小丽',
            studentNumber: 'S20230009',
            bookTitle: '人工智能导论',
            isbn: '9787302901234',
            borrowDate: '2024-03-13',
            dueDate: '2024-03-27',
            returnDate: '2024-04-01',
            status: '已归还',
            renewCount: 2,
            fine: 0
          },
          {
            id: 10,
            studentName: '吴小华',
            studentNumber: 'S20230010',
            bookTitle: '软件工程',
            isbn: '9787111012345',
            borrowDate: '2024-01-17',
            dueDate: '2024-01-31',
            returnDate: '2024-01-22',
            status: '已归还',
            renewCount: 2,
            fine: 0
          }
        ]

        // 模拟分页数据
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize
        const endIndex = startIndex + pagination.pageSize
        borrowingList.value = mockBorrowingRecords.slice(startIndex, endIndex)
        pagination.total = mockBorrowingRecords.length

        console.log('使用模拟借阅记录数据，共', mockBorrowingRecords.length, '条记录，当前显示第', pagination.currentPage, '页')
      } finally {
        loading.value = false
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchBorrowingData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        studentName: '',
        bookName: '',
        status: '',
        borrowDateRange: []
      })
      pagination.currentPage = 1
      fetchBorrowingData()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      pagination.currentPage = 1
      fetchBorrowingData()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchBorrowingData()
    }

    // 排序改变
    const handleSortChange = ({ prop, order }) => {
      sortInfo.prop = prop
      sortInfo.order = order
      fetchBorrowingData()
    }

    // 新增借阅
    const handleAdd = () => {
      router.push('/borrowing/add')
    }

    // 查看借阅记录
    const handleView = (row) => {
      ElMessage.info(`查看借阅记录：${row.studentName} - ${row.bookTitle}`)
    }

    // 归还图书
    const handleReturn = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要归还图书《${row.bookTitle}》吗？`,
          '确认归还',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
          }
        )
        
        await returnBook(row.id, { returnDate: new Date().toISOString().split('T')[0] })
        ElMessage.success('归还成功')
        fetchBorrowingData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('归还图书失败:', error)
          ElMessage.error('归还失败')
        }
      }
    }

    // 续借图书
    const handleRenew = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要续借图书《${row.bookTitle}》吗？`,
          '确认续借',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        await renewBook(row.id, { renewDays: 30 })
        ElMessage.success('续借成功')
        fetchBorrowingData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('续借图书失败:', error)
          ElMessage.error('续借失败')
        }
      }
    }

    // 删除借阅记录
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除这条借阅记录吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        await deleteBorrowingRecord(row.id)
        ElMessage.success('删除成功')
        fetchBorrowingData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除借阅记录失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 判断是否逾期
    const isOverdue = (dueDate, status) => {
      if (status === 'RETURNED') return false
      return new Date(dueDate) < new Date()
    }

    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const typeMap = {
        'BORROWED': 'primary',
        'RETURNED': 'success',
        'OVERDUE': 'danger',
        'RENEWED': 'warning'
      }
      return typeMap[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const textMap = {
        'BORROWED': '已借出',
        'RETURNED': '已归还',
        'OVERDUE': '逾期',
        'RENEWED': '续借'
      }
      return textMap[status] || status
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    }

    // 初始化图表
    const initCharts = async () => {
      try {
        const statsRes = await getBorrowingStats()
        if (statsRes) {
          // 状态分布图表
          const statusChartInstance = echarts.init(document.getElementById('statusChart'))
          statusChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [
              {
                name: '借阅状态',
                type: 'pie',
                radius: '50%',
                data: statsRes.statusStats || [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
          statusChart.value = statusChartInstance

          // 借阅趋势图表
          const trendChartInstance = echarts.init(document.getElementById('trendChart'))
          trendChartInstance.setOption({
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['借阅量', '归还量']
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: statsRes.trendStats?.map(item => item.date) || []
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '借阅量',
                type: 'line',
                data: statsRes.trendStats?.map(item => item.borrowCount) || [],
                itemStyle: {
                  color: '#409EFF'
                }
              },
              {
                name: '归还量',
                type: 'line',
                data: statsRes.trendStats?.map(item => item.returnCount) || [],
                itemStyle: {
                  color: '#67C23A'
                }
              }
            ]
          })
          trendChart.value = trendChartInstance
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)

        // 使用模拟统计数据
        const mockStatusData = [
          { name: '已归还', value: 25 },
          { name: '借阅中', value: 8 },
          { name: '逾期', value: 3 }
        ]

        const mockTrendData = {
          dates: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07'],
          borrowCount: [45, 52, 48, 61, 55, 67, 58],
          returnCount: [42, 49, 46, 58, 53, 64, 56]
        }

        // 初始化状态分布图表
        if (statusChart.value) {
          const statusChartInstance = echarts.init(statusChart.value)
          statusChartInstance.setOption({
            title: {
              text: '借阅状态分布',
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [
              {
                name: '借阅状态',
                type: 'pie',
                radius: '50%',
                data: mockStatusData,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
        }

        // 初始化趋势图表
        if (trendChart.value) {
          const trendChartInstance = echarts.init(trendChart.value)
          trendChartInstance.setOption({
            title: {
              text: '借阅趋势',
              left: 'center'
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: ['借阅数量', '归还数量']
            },
            xAxis: {
              type: 'category',
              data: mockTrendData.dates
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '借阅数量',
                type: 'line',
                data: mockTrendData.borrowCount,
                smooth: true
              },
              {
                name: '归还数量',
                type: 'line',
                data: mockTrendData.returnCount,
                smooth: true
              }
            ]
          })
        }
      }
    }

    // 窗口大小改变时重新调整图表
    const resizeCharts = () => {
      if (statusChart.value) {
        statusChart.value.resize()
      }
      if (trendChart.value) {
        trendChart.value.resize()
      }
    }

    onMounted(() => {
      fetchBorrowingData()
      setTimeout(() => {
        initCharts()
      }, 100)
      window.addEventListener('resize', resizeCharts)
    })

    onUnmounted(() => {
      if (statusChart.value) {
        statusChart.value.dispose()
      }
      if (trendChart.value) {
        trendChart.value.dispose()
      }
      window.removeEventListener('resize', resizeCharts)
    })

    return {
      loading,
      borrowingList,
      searchForm,
      pagination,
      fetchBorrowingData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleView,
      handleReturn,
      handleRenew,
      handleDelete,
      isOverdue,
      getStatusTagType,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.borrowing-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.overdue {
  color: #f56c6c;
  font-weight: bold;
}

.fine-amount {
  color: #f56c6c;
  font-weight: bold;
}
</style>
