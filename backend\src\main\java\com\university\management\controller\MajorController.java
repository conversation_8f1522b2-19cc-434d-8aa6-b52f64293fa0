package com.university.management.controller;

import com.university.management.model.entity.Major;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.MajorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Api(tags = "专业管理")
@RestController
@RequestMapping("/api/majors")
public class MajorController {

    private final MajorService majorService;

    @Autowired
    public MajorController(MajorService majorService) {
        this.majorService = majorService;
    }

    @ApiOperation("获取所有专业")
    @GetMapping
    public ApiResponse<List<Major>> getAllMajors() {
        List<Major> majors = majorService.findAll();
        return ApiResponse.success("获取专业列表成功", majors);
    }

    @ApiOperation("分页获取专业")
    @GetMapping("/page")
    public ApiResponse<Page<Major>> getMajorsPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "ASC") String direction) {
        Sort.Direction sortDirection = "ASC".equalsIgnoreCase(direction) ? Sort.Direction.ASC : Sort.Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortDirection, sortBy));
        return ApiResponse.success(majorService.findAll(pageRequest));
    }

    @ApiOperation("通过条件查询专业")
    @GetMapping("/search")
    public ApiResponse<Page<Major>> searchMajors(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer departmentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(majorService.findByConditions(name, departmentId, pageRequest));
    }

    @ApiOperation("获取专业详情")
    @GetMapping("/{id}")
    public ApiResponse<Major> getMajorById(@PathVariable Integer id) {
        Optional<Major> major = majorService.findById(id);
        return major.map(ApiResponse::success)
                .orElseGet(() -> ApiResponse.errorGeneric(404, "专业不存在"));
    }

    @ApiOperation("创建专业")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Major> createMajor(@Valid @RequestBody Major major) {
        Major savedMajor = majorService.save(major);
        return ApiResponse.success("创建专业成功", savedMajor);
    }

    @ApiOperation("更新专业")
    @PutMapping("/{id}")
    public ApiResponse<Major> updateMajor(@PathVariable Integer id, @Valid @RequestBody Major majorDetails) {
        Optional<Major> major = majorService.findById(id);
        if (major.isPresent()) {
            majorDetails.setId(id);
            Major updatedMajor = majorService.save(majorDetails);
            return ApiResponse.success("更新专业成功", updatedMajor);
        } else {
            return ApiResponse.errorGeneric(404, "专业不存在");
        }
    }

    @ApiOperation("删除专业")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteMajor(@PathVariable Integer id) {
        Optional<Major> major = majorService.findById(id);
        if (major.isPresent()) {
            majorService.deleteById(id);
            return ApiResponse.success("删除专业成功", null);
        } else {
            return ApiResponse.errorGeneric(404, "专业不存在");
        }
    }

    @ApiOperation("根据院系ID获取专业列表")
    @GetMapping("/department/{departmentId}")
    public ApiResponse<List<Major>> getMajorsByDepartmentId(@PathVariable Integer departmentId) {
        List<Major> majors = majorService.findByDepartmentId(departmentId);
        return ApiResponse.success("获取院系专业列表成功", majors);
    }

    @ApiOperation("获取专业统计信息")
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getMajorStats() {
        Map<String, Object> stats = majorService.getMajorStats();
        return ApiResponse.success("获取专业统计信息成功", stats);
    }
}
