# 数据库中文化更新指南

## 问题描述
数据库中的班级、专业、课程、教室等数据存在英文内容，需要全部更新为中文。

## 🎯 解决方案

### 方法一：执行完整中文化脚本（推荐）
```bash
# 进入数据库目录
cd backend/src/main/resources/db

# 执行完整中文化更新
mysql -u root -p university_management < complete-chinese-update.sql
```

### 方法二：执行渐进式更新
```bash
# 先执行基础中文化更新
mysql -u root -p university_management < chinese-data-update.sql

# 如果需要更彻底的更新，再执行完整更新
mysql -u root -p university_management < complete-chinese-update.sql
```

## 📋 更新内容

### 1. 班级数据中文化
**更新前：**
- Computer Science 2020 Class 1
- Software Engineering 2021 Class 1
- Network Engineering 2020 Class 1

**更新后：**
- 计算机科学与技术2020级1班
- 软件工程2021级1班
- 网络工程2020级1班

### 2. 专业数据中文化
**更新前：**
- Computer Science and Technology
- Software Engineering
- Network Engineering
- Electronic Information Engineering

**更新后：**
- 计算机科学与技术
- 软件工程
- 网络工程
- 电子信息工程

### 3. 课程数据中文化
**更新前：**
- Programming Fundamentals
- Data Structures
- Algorithm Analysis
- Computer Networks

**更新后：**
- 程序设计基础
- 数据结构
- 算法分析
- 计算机网络

### 4. 教室数据中文化
**更新前：**
- Computer Lab 1
- Lecture Hall A103
- Teaching Building A
- Laboratory Building

**更新后：**
- 计算机实验室1
- 阶梯教室A103
- 第一教学楼
- 实验楼A

### 5. 其他数据中文化
- 学位类型：B.Eng → 工学学士
- 建筑名称：Teaching Building → 教学楼
- 设备名称：Computers → 计算机
- 场馆名称：Basketball Court → 篮球场

## 🔍 验证更新结果

### SQL验证查询
```sql
-- 检查班级数据中文化情况
SELECT name, 
       CASE WHEN name LIKE '%Class%' OR name LIKE '%Engineering%' OR name LIKE '%Science%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM class 
ORDER BY 语言类型, name;

-- 检查专业数据中文化情况
SELECT name, degree,
       CASE WHEN name LIKE '%Engineering%' OR name LIKE '%Science%' OR name LIKE '%Administration%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM major 
ORDER BY 语言类型, name;

-- 检查课程数据中文化情况
SELECT name,
       CASE WHEN name LIKE '%Programming%' OR name LIKE '%Data%' OR name LIKE '%System%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM course 
ORDER BY 语言类型, name;

-- 检查教室数据中文化情况
SELECT name, building,
       CASE WHEN name LIKE '%Lab%' OR name LIKE '%Hall%' OR building LIKE '%Building%' 
            THEN '英文' ELSE '中文' END AS 语言类型
FROM classroom 
ORDER BY 语言类型, name;
```

### 统计查询
```sql
-- 统计各类数据的中文化比例
SELECT 
    '班级' AS 数据类型,
    COUNT(*) AS 总数,
    SUM(CASE WHEN name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' THEN 1 ELSE 0 END) AS 中文数量,
    ROUND(SUM(CASE WHEN name NOT LIKE '%Class%' AND name NOT LIKE '%Engineering%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS 中文化比例
FROM class
UNION ALL
SELECT 
    '专业',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Engineering%' AND name NOT LIKE '%Science%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM major
UNION ALL
SELECT 
    '课程',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Programming%' AND name NOT LIKE '%Data%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM course
UNION ALL
SELECT 
    '教室',
    COUNT(*),
    SUM(CASE WHEN name NOT LIKE '%Lab%' AND building NOT LIKE '%Building%' THEN 1 ELSE 0 END),
    ROUND(SUM(CASE WHEN name NOT LIKE '%Lab%' AND building NOT LIKE '%Building%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
FROM classroom;
```

## ⚠️ 注意事项

1. **备份数据**：执行前请备份数据库
   ```bash
   mysqldump -u root -p university_management > backup_before_chinese_update.sql
   ```

2. **检查外键约束**：脚本会临时禁用外键检查，确保更新顺利进行

3. **分批执行**：如果数据量很大，可以分批执行更新

4. **验证结果**：更新完成后使用验证查询检查结果

## 🎉 预期结果

更新完成后，您应该看到：
- ✅ 所有班级名称为中文（如：计算机科学与技术2020级1班）
- ✅ 所有专业名称为中文（如：计算机科学与技术）
- ✅ 所有课程名称为中文（如：程序设计基础）
- ✅ 所有教室名称为中文（如：计算机实验室1）
- ✅ 学位类型为中文（如：工学学士）
- ✅ 建筑名称为中文（如：第一教学楼）

## 🔧 故障排除

### 问题1：更新失败
```bash
# 检查MySQL连接
mysql -u root -p -e "SELECT 1"

# 检查数据库是否存在
mysql -u root -p -e "SHOW DATABASES LIKE 'university_management'"
```

### 问题2：部分数据未更新
```sql
-- 手动检查特定表的英文数据
SELECT * FROM class WHERE name LIKE '%Class%' OR name LIKE '%Engineering%';
SELECT * FROM major WHERE name LIKE '%Engineering%' OR name LIKE '%Science%';
SELECT * FROM course WHERE name LIKE '%Programming%' OR name LIKE '%Data%';
```

### 问题3：外键约束错误
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行更新操作
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
```

---

**💡 提示**：建议在非生产环境先测试更新脚本，确认无误后再在生产环境执行。
