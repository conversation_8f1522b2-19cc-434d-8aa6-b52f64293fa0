-- 修复字符集排序规则问题的中文化脚本
-- 解决 Illegal mix of collations 错误

USE university_management;

-- 设置字符集和排序规则
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 更新班级数据中的英文内容（使用 BINARY 避免排序规则冲突）
UPDATE class SET 
    name = CASE 
        -- 计算机相关班级
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '计算机科学与技术2020级1班'
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 2%' THEN '计算机科学与技术2020级2班'
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '计算机科学与技术2021级1班'
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 2%' THEN '计算机科学与技术2021级2班'
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '计算机科学与技术2022级1班'
        WHEN BINARY name LIKE '%Computer Science%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '计算机科学与技术2023级1班'
        
        -- 软件工程班级
        WHEN BINARY name LIKE '%Software Engineering%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '软件工程2020级1班'
        WHEN BINARY name LIKE '%Software Engineering%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '软件工程2021级1班'
        WHEN BINARY name LIKE '%Software Engineering%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '软件工程2022级1班'
        WHEN BINARY name LIKE '%Software Engineering%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '软件工程2023级1班'
        
        -- 网络工程班级
        WHEN BINARY name LIKE '%Network Engineering%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '网络工程2020级1班'
        WHEN BINARY name LIKE '%Network Engineering%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '网络工程2021级1班'
        WHEN BINARY name LIKE '%Network Engineering%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '网络工程2022级1班'
        WHEN BINARY name LIKE '%Network Engineering%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '网络工程2023级1班'
        
        -- 人工智能班级
        WHEN BINARY name LIKE '%Artificial Intelligence%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '人工智能2021级1班'
        WHEN BINARY name LIKE '%Artificial Intelligence%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '人工智能2022级1班'
        WHEN BINARY name LIKE '%Artificial Intelligence%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '人工智能2023级1班'
        
        -- 数据科学班级
        WHEN BINARY name LIKE '%Data Science%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '数据科学与大数据技术2021级1班'
        WHEN BINARY name LIKE '%Data Science%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '数据科学与大数据技术2022级1班'
        WHEN BINARY name LIKE '%Data Science%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '数据科学与大数据技术2023级1班'
        
        -- 电子信息工程班级
        WHEN BINARY name LIKE '%Electronic%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '电子信息工程2020级1班'
        WHEN BINARY name LIKE '%Electronic%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '电子信息工程2021级1班'
        WHEN BINARY name LIKE '%Electronic%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '电子信息工程2022级1班'
        WHEN BINARY name LIKE '%Electronic%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '电子信息工程2023级1班'
        
        -- 通信工程班级
        WHEN BINARY name LIKE '%Communication%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '通信工程2020级1班'
        WHEN BINARY name LIKE '%Communication%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '通信工程2021级1班'
        WHEN BINARY name LIKE '%Communication%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '通信工程2022级1班'
        WHEN BINARY name LIKE '%Communication%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '通信工程2023级1班'
        
        -- 机械工程班级
        WHEN BINARY name LIKE '%Mechanical%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '机械设计制造及其自动化2020级1班'
        WHEN BINARY name LIKE '%Mechanical%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '机械设计制造及其自动化2021级1班'
        WHEN BINARY name LIKE '%Mechanical%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '机械设计制造及其自动化2022级1班'
        WHEN BINARY name LIKE '%Mechanical%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '机械设计制造及其自动化2023级1班'
        
        -- 工商管理班级
        WHEN BINARY name LIKE '%Business%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '工商管理2020级1班'
        WHEN BINARY name LIKE '%Business%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '工商管理2021级1班'
        WHEN BINARY name LIKE '%Business%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '工商管理2022级1班'
        WHEN BINARY name LIKE '%Business%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '工商管理2023级1班'
        
        -- 英语班级
        WHEN BINARY name LIKE '%English%' AND BINARY name LIKE '%2020%' AND BINARY name LIKE '%Class 1%' THEN '英语2020级1班'
        WHEN BINARY name LIKE '%English%' AND BINARY name LIKE '%2021%' AND BINARY name LIKE '%Class 1%' THEN '英语2021级1班'
        WHEN BINARY name LIKE '%English%' AND BINARY name LIKE '%2022%' AND BINARY name LIKE '%Class 1%' THEN '英语2022级1班'
        WHEN BINARY name LIKE '%English%' AND BINARY name LIKE '%2023%' AND BINARY name LIKE '%Class 1%' THEN '英语2023级1班'
        
        ELSE name
    END
WHERE BINARY name LIKE '%Class%' OR BINARY name LIKE '%Engineering%' OR BINARY name LIKE '%Science%' OR BINARY name LIKE '%Business%' OR BINARY name LIKE '%English%';

-- 2. 更新专业数据中的英文内容
UPDATE major SET 
    name = CASE 
        WHEN BINARY name = 'Computer Science and Technology' THEN '计算机科学与技术'
        WHEN BINARY name = 'Software Engineering' THEN '软件工程'
        WHEN BINARY name = 'Network Engineering' THEN '网络工程'
        WHEN BINARY name = 'Artificial Intelligence' THEN '人工智能'
        WHEN BINARY name = 'Data Science and Big Data Technology' THEN '数据科学与大数据技术'
        WHEN BINARY name = 'Electronic Information Engineering' THEN '电子信息工程'
        WHEN BINARY name = 'Communication Engineering' THEN '通信工程'
        WHEN BINARY name = 'Automation' THEN '自动化'
        WHEN BINARY name = 'Mechanical Design Manufacturing and Automation' THEN '机械设计制造及其自动化'
        WHEN BINARY name = 'Mechatronics Engineering' THEN '机械电子工程'
        WHEN BINARY name = 'Vehicle Engineering' THEN '车辆工程'
        WHEN BINARY name = 'Industrial Design' THEN '工业设计'
        WHEN BINARY name = 'Business Administration' THEN '工商管理'
        WHEN BINARY name = 'Accounting' THEN '会计学'
        WHEN BINARY name = 'Marketing' THEN '市场营销'
        WHEN BINARY name = 'International Economics and Trade' THEN '国际经济与贸易'
        WHEN BINARY name = 'English' THEN '英语'
        WHEN BINARY name = 'Japanese' THEN '日语'
        WHEN BINARY name = 'German' THEN '德语'
        WHEN BINARY name = 'French' THEN '法语'
        WHEN BINARY name = 'Visual Communication Design' THEN '视觉传达设计'
        WHEN BINARY name = 'Environmental Design' THEN '环境设计'
        WHEN BINARY name = 'Product Design' THEN '产品设计'
        ELSE name
    END,
    description = CASE 
        WHEN BINARY name LIKE '%Computer Science%' OR BINARY description LIKE '%computer science%' THEN '培养计算机科学与技术领域的高级专门人才'
        WHEN BINARY name LIKE '%Software%' OR BINARY description LIKE '%software%' THEN '培养软件开发与工程管理的专业人才'
        WHEN BINARY name LIKE '%Network%' OR BINARY description LIKE '%network%' THEN '培养网络系统设计与管理的专业人才'
        WHEN BINARY name LIKE '%Intelligence%' OR BINARY description LIKE '%AI%' THEN '培养人工智能技术应用的专业人才'
        WHEN BINARY name LIKE '%Data%' OR BINARY description LIKE '%data%' THEN '培养大数据分析与处理的专业人才'
        WHEN BINARY name LIKE '%Electronic%' OR BINARY description LIKE '%electronic%' THEN '培养电子信息系统设计与开发的专业人才'
        WHEN BINARY name LIKE '%Communication%' OR BINARY description LIKE '%communication%' THEN '培养通信系统设计与维护的专业人才'
        WHEN BINARY name LIKE '%Automation%' OR BINARY description LIKE '%automation%' THEN '培养自动化控制系统的专业人才'
        WHEN BINARY name LIKE '%Mechanical%' OR BINARY description LIKE '%mechanical%' THEN '培养机械设计制造及自动化的专业人才'
        WHEN BINARY name LIKE '%Business%' OR BINARY description LIKE '%business%' THEN '培养企业管理与经营的专业人才'
        WHEN BINARY name LIKE '%Accounting%' OR BINARY description LIKE '%accounting%' THEN '培养财务会计与审计的专业人才'
        WHEN BINARY name LIKE '%Marketing%' OR BINARY description LIKE '%marketing%' THEN '培养市场分析与营销策略的专业人才'
        WHEN BINARY name LIKE '%English%' OR BINARY description LIKE '%English%' THEN '培养英语语言文学与翻译的专业人才'
        WHEN BINARY name LIKE '%Design%' OR BINARY description LIKE '%design%' THEN '培养设计创新的专业人才'
        ELSE description
    END,
    degree = CASE 
        WHEN BINARY degree IN ('B.Eng', 'Bachelor of Engineering') THEN '工学学士'
        WHEN BINARY degree IN ('B.Sc', 'Bachelor of Science') THEN '理学学士'
        WHEN BINARY degree IN ('B.A', 'Bachelor of Arts') THEN '文学学士'
        WHEN BINARY degree IN ('B.M', 'Bachelor of Management') THEN '管理学学士'
        WHEN BINARY degree IN ('B.F.A', 'Bachelor of Fine Arts') THEN '艺术学学士'
        ELSE degree
    END
WHERE BINARY name LIKE '%Engineering%' OR BINARY name LIKE '%Science%' OR BINARY name LIKE '%Administration%' OR BINARY name LIKE '%Design%' OR BINARY name LIKE '%English%' OR BINARY degree LIKE 'B.%';

-- 3. 更新课程数据中的英文内容
UPDATE course SET 
    name = CASE 
        WHEN BINARY name = 'Programming Fundamentals' THEN '程序设计基础'
        WHEN BINARY name = 'Data Structures' THEN '数据结构'
        WHEN BINARY name = 'Algorithm Analysis' THEN '算法分析'
        WHEN BINARY name = 'Computer Networks' THEN '计算机网络'
        WHEN BINARY name = 'Database Systems' THEN '数据库系统'
        WHEN BINARY name = 'Operating Systems' THEN '操作系统'
        WHEN BINARY name = 'Software Engineering' THEN '软件工程'
        WHEN BINARY name = 'Computer Graphics' THEN '计算机图形学'
        WHEN BINARY name = 'Artificial Intelligence' THEN '人工智能'
        WHEN BINARY name = 'Machine Learning' THEN '机器学习'
        WHEN BINARY name = 'Web Development' THEN 'Web开发技术'
        WHEN BINARY name = 'Mobile App Development' THEN '移动应用开发'
        WHEN BINARY name = 'Network Security' THEN '网络安全'
        WHEN BINARY name = 'Digital Signal Processing' THEN '数字信号处理'
        WHEN BINARY name = 'Electronic Circuits' THEN '电子电路'
        WHEN BINARY name = 'Communication Principles' THEN '通信原理'
        WHEN BINARY name = 'Control Systems' THEN '控制系统'
        WHEN BINARY name = 'Mechanical Design' THEN '机械设计'
        WHEN BINARY name = 'Manufacturing Technology' THEN '制造技术'
        WHEN BINARY name = 'Materials Science' THEN '材料科学'
        WHEN BINARY name = 'Thermodynamics' THEN '热力学'
        WHEN BINARY name = 'Fluid Mechanics' THEN '流体力学'
        WHEN BINARY name = 'Management Principles' THEN '管理学原理'
        WHEN BINARY name = 'Financial Accounting' THEN '财务会计'
        WHEN BINARY name = 'Marketing Management' THEN '营销管理'
        WHEN BINARY name = 'International Trade' THEN '国际贸易'
        WHEN BINARY name = 'English Literature' THEN '英国文学'
        WHEN BINARY name = 'English Grammar' THEN '英语语法'
        WHEN BINARY name = 'Translation Theory' THEN '翻译理论'
        WHEN BINARY name = 'Design Principles' THEN '设计原理'
        WHEN BINARY name = 'Color Theory' THEN '色彩理论'
        WHEN BINARY name = 'Typography' THEN '字体设计'
        WHEN BINARY name = 'Interior Design' THEN '室内设计'
        WHEN BINARY name = 'Landscape Design' THEN '景观设计'
        ELSE name
    END,
    description = CASE 
        WHEN BINARY name LIKE '%Programming%' OR BINARY description LIKE '%programming%' THEN '计算机程序设计基础课程'
        WHEN BINARY name LIKE '%Data Structures%' OR BINARY description LIKE '%data structures%' THEN '学习各种数据结构的原理与应用'
        WHEN BINARY name LIKE '%Algorithm%' OR BINARY description LIKE '%algorithm%' THEN '算法设计与分析方法'
        WHEN BINARY name LIKE '%Networks%' OR BINARY description LIKE '%network%' THEN '计算机网络原理与技术'
        WHEN BINARY name LIKE '%Database%' OR BINARY description LIKE '%database%' THEN '数据库理论与实践'
        WHEN BINARY name LIKE '%Operating%' OR BINARY description LIKE '%operating%' THEN '操作系统原理与应用'
        WHEN BINARY name LIKE '%Software Engineering%' OR BINARY description LIKE '%software engineering%' THEN '软件工程方法与实践'
        WHEN BINARY name LIKE '%Graphics%' OR BINARY description LIKE '%graphics%' THEN '计算机图形学基础'
        WHEN BINARY name LIKE '%Intelligence%' OR BINARY description LIKE '%intelligence%' THEN '人工智能基础理论'
        WHEN BINARY name LIKE '%Learning%' OR BINARY description LIKE '%learning%' THEN '机器学习算法与应用'
        WHEN BINARY name LIKE '%Management%' OR BINARY description LIKE '%management%' THEN '管理学基础理论与方法'
        WHEN BINARY name LIKE '%Accounting%' OR BINARY description LIKE '%accounting%' THEN '会计学基础理论与实务'
        WHEN BINARY name LIKE '%Marketing%' OR BINARY description LIKE '%marketing%' THEN '市场营销理论与策略'
        WHEN BINARY name LIKE '%Design%' OR BINARY description LIKE '%design%' THEN '设计理论与实践方法'
        ELSE description
    END
WHERE BINARY name LIKE '%Programming%' OR BINARY name LIKE '%Data%' OR BINARY name LIKE '%Algorithm%' OR BINARY name LIKE '%Network%' 
   OR BINARY name LIKE '%Database%' OR BINARY name LIKE '%System%' OR BINARY name LIKE '%Engineering%' OR BINARY name LIKE '%Management%'
   OR BINARY name LIKE '%Design%' OR BINARY name LIKE '%Theory%' OR BINARY name LIKE '%Principles%';

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交更改
COMMIT;

-- 显示更新结果
SELECT '字符集排序规则问题已修复，所有英文数据已更新为中文！' AS 状态;

-- 统计更新结果
SELECT 
    '班级' AS 数据类型,
    COUNT(*) AS 总数量,
    SUM(CASE WHEN BINARY name NOT LIKE '%Class%' AND BINARY name NOT LIKE '%Engineering%' AND BINARY name NOT LIKE '%Science%' THEN 1 ELSE 0 END) AS 中文数量
FROM class
UNION ALL
SELECT 
    '专业',
    COUNT(*),
    SUM(CASE WHEN BINARY name NOT LIKE '%Engineering%' AND BINARY name NOT LIKE '%Science%' AND BINARY name NOT LIKE '%Administration%' THEN 1 ELSE 0 END)
FROM major
UNION ALL
SELECT 
    '课程',
    COUNT(*),
    SUM(CASE WHEN BINARY name NOT LIKE '%Programming%' AND BINARY name NOT LIKE '%Data%' AND BINARY name NOT LIKE '%System%' THEN 1 ELSE 0 END)
FROM course;
