package com.university.management.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

import javax.sql.DataSource;

/**
 * 数据库初始化配置
 * 负责在应用启动时执行SQL脚本初始化数据库
 */
@Configuration
public class DatabaseInitializer {

    private static final Logger log = LoggerFactory.getLogger(DatabaseInitializer.class);
    
    private final DataSource dataSource;

    /**
     * 构造函数
     */
    public DatabaseInitializer(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * 执行SQL脚本初始化数据库
     */
    @Bean
    public CommandLineRunner initDatabase() {
        return args -> {
            log.info("开始初始化数据库...");
            try {
                // 执行数据库表结构初始化脚本
                ResourceDatabasePopulator schemaPopulator = new ResourceDatabasePopulator();
                schemaPopulator.addScript(new ClassPathResource("db/init-schema.sql"));
                schemaPopulator.execute(dataSource);
                log.info("数据库表结构初始化完成");

                // 执行数据库数据初始化脚本
                ResourceDatabasePopulator dataPopulator = new ResourceDatabasePopulator();
                dataPopulator.addScript(new ClassPathResource("db/init-data.sql"));
                dataPopulator.execute(dataSource);
                log.info("数据库初始数据填充完成");
            } catch (Exception e) {
                log.error("数据库初始化失败：", e);
            }
            log.info("数据库初始化完成");
        };
    }
} 