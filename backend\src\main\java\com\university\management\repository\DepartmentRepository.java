package com.university.management.repository;

import com.university.management.model.entity.Department;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 部门数据访问接口
 */
@Repository
public interface DepartmentRepository extends JpaRepository<Department, Integer> {
    
    /**
     * 根据部门编号查询部门
     *
     * @param departmentNo 部门编号
     * @return 部门信息
     */
    Optional<Department> findByDepartmentNo(String departmentNo);
    
    /**
     * 根据部门名称查询部门
     *
     * @param name 部门名称
     * @param pageable 分页参数
     * @return 部门分页结果
     */
    Page<Department> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 条件查询部门
     *
     * @param name 部门名称
     * @param dean 院长姓名
     * @param pageable 分页参数
     * @return 部门分页结果
     */
    @Query("SELECT d FROM Department d WHERE " +
           "(:name IS NULL OR d.name LIKE %:name%) AND " +
           "(:dean IS NULL OR d.dean LIKE %:dean%)")
    Page<Department> findByConditions(
            @Param("name") String name, 
            @Param("dean") String dean, 
            Pageable pageable);
    
    /**
     * 检查部门编号是否存在
     *
     * @param departmentNo 部门编号
     * @return 是否存在
     */
    boolean existsByDepartmentNo(String departmentNo);
} 