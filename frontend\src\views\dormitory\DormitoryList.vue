<template>
  <div class="dormitory-list-container">
    <div class="page-header">
      <h2>宿舍管理</h2>
      <el-button type="primary" @click="handleAddDormitory">新增宿舍</el-button>
    </div>

    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="宿舍楼">
          <el-input v-model="queryParams.building" placeholder="请输入宿舍楼" clearable />
        </el-form-item>
        <el-form-item label="房间号">
          <el-input v-model="queryParams.roomNo" placeholder="请输入房间号" clearable />
        </el-form-item>
        <el-form-item label="宿舍类型">
          <el-select v-model="queryParams.roomType" placeholder="请选择宿舍类型" clearable>
            <el-option label="标准间" :value="0" />
            <el-option label="单人间" :value="1" />
            <el-option label="套间" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否满员">
          <el-select v-model="queryParams.isFull" placeholder="请选择状态" clearable>
            <el-option label="已满员" :value="true" />
            <el-option label="未满员" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="list-container">
      <el-table
        v-loading="loading"
        :data="dormitoryList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="building" label="宿舍楼" width="100" />
        <el-table-column prop="roomNo" label="房间号" width="100" />
        <el-table-column prop="roomType" label="宿舍类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.roomType === 0 ? 'success' : scope.row.roomType === 1 ? 'warning' : 'info'">
              {{ scope.row.roomType === 0 ? '标准间' : scope.row.roomType === 1 ? '单人间' : '套间' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容量" width="80" />
        <el-table-column prop="occupied" label="已住人数" width="100" />
        <el-table-column label="是否满员" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.occupied >= scope.row.capacity ? 'danger' : 'success'">
              {{ scope.row.occupied >= scope.row.capacity ? '已满员' : '未满员' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fee" label="费用(/月)" width="100" />
        <el-table-column prop="facilities" label="设施" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:page-size="queryParams.pageSize"
          v-model:current-page="queryParams.pageNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDormitoriesByPage } from '@/api/dormitory'

export default {
  name: 'DormitoryList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const total = ref(0)
    const dormitoryList = ref([])
    
    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      building: '',
      roomNo: '',
      roomType: undefined,
      isFull: undefined
    })
    
    // 获取宿舍列表
    const getList = async () => {
      loading.value = true
      try {
        const response = await getDormitoriesByPage(
          queryParams.pageNum - 1,
          queryParams.pageSize
        )

        // 响应拦截器已经处理了success判断，这里直接处理数据
        if (response && typeof response === 'object' && 'content' in response) {
          // 分页API返回的数据结构
          dormitoryList.value = response.content || []
          total.value = response.totalElements || 0

          console.log('✅ 成功获取宿舍数据，共', total.value, '条记录')
        } else if (Array.isArray(response)) {
          // 如果返回数组格式
          dormitoryList.value = response
          total.value = response.length
        } else {
          dormitoryList.value = []
          total.value = 0
        }
      } catch (error) {
        console.error('获取宿舍列表失败:', error)
        ElMessage.error('获取宿舍列表失败: ' + (error.message || '未知错误'))
        dormitoryList.value = []
        total.value = 0
      } finally {
        loading.value = false
      }
    }
    
    // 处理查询按钮点击
    const handleQuery = () => {
      queryParams.pageNum = 1
      getList()
    }
    
    // 重置查询条件
    const resetQuery = () => {
      queryParams.building = ''
      queryParams.roomNo = ''
      queryParams.roomType = undefined
      queryParams.isFull = undefined
      queryParams.pageNum = 1
      getList()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      queryParams.pageNum = val
      getList()
    }
    
    // 处理每页数量变化
    const handleSizeChange = (val) => {
      queryParams.pageSize = val
      queryParams.pageNum = 1
      getList()
    }
    
    // 查看宿舍详情
    const handleView = (row) => {
      ElMessage.info(`查看宿舍：${row.building}-${row.roomNo}`)
    }
    
    // 编辑宿舍
    const handleEdit = (row) => {
      router.push({ name: 'DormitoryEdit', params: { id: row.id } })
    }
    
    // 添加宿舍
    const handleAddDormitory = () => {
      router.push({ name: 'DormitoryAdd' })
    }
    
    // 删除宿舍
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除宿舍${row.building}-${row.roomNo}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 实际项目中这里需要调用API
          // await deleteDormitory(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          ElMessage.error('删除失败')
          console.error(error)
        }
      }).catch(() => {})
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      loading,
      dormitoryList,
      queryParams,
      total,
      handleQuery,
      resetQuery,
      handleCurrentChange,
      handleSizeChange,
      handleView,
      handleEdit,
      handleAddDormitory,
      handleDelete
    }
  }
}
</script>

<style scoped>
.dormitory-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}
</style> 