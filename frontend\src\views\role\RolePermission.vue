<template>
  <div class="role-permission-container">
    <div class="header">
      <h2>角色权限分配 - {{ roleName }}</h2>
      <div>
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>

    <div class="permission-content" v-loading="loading">
      <div class="permission-operations">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          全选
        </el-checkbox>
        <el-button type="text" @click="expandAll">展开全部</el-button>
        <el-button type="text" @click="collapseAll">折叠全部</el-button>
      </div>

             <div class="permission-tree-container">
         <el-card shadow="never">
           <el-tree
             ref="permissionTreeRef"
             :data="permissionTree"
             show-checkbox
             node-key="id"
             :props="{ children: 'children', label: 'name' }"
             :default-checked-keys="checkedPermissionIds"
             :default-expanded-keys="expandedKeys"
             @check="handleTreeCheck">
            <template #default="{ node, data }">
              <span class="permission-node">
                <span class="permission-icon">
                  <i :class="getPermissionIcon(data)"></i>
                </span>
                <span class="permission-name">{{ node.label }}</span>
                <span class="permission-code">{{ data.code }}</span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getRoleById, getRolePermissions, updateRolePermissions } from '@/api/role'
import { getPermissionTree } from '@/api/role'

export default {
  name: 'RolePermission',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const permissionTree = ref([])
    const checkedPermissionIds = ref([])
    const roleName = ref('')
    const loading = ref(false)
    const checkAll = ref(false)
    const isIndeterminate = ref(false)
    const permissionTreeRef = ref(null)
    const expandedKeys = ref([])
    
    // 权限类型对应的图标
    const permissionIcons = {
      'menu': 'el-icon-menu',
      'button': 'el-icon-s-operation',
      'api': 'el-icon-connection',
      'data': 'el-icon-s-data',
      'module': 'el-icon-s-grid'
    }

    // 获取角色信息
    const fetchRoleInfo = async () => {
      try {
        const roleId = route.params.id
        const res = await getRoleById(roleId)
        if (res.data) {
          roleName.value = res.data.name
        }
      } catch (error) {
        console.error('获取角色信息失败:', error)
        ElMessage.error('获取角色信息失败')
      }
    }

    // 获取权限树
    const fetchPermissionTree = async () => {
      loading.value = true
      try {
        const res = await getPermissionTree()
        if (res.data) {
          permissionTree.value = res.data
          // 获取所有一级节点的ID作为默认展开项
          expandedKeys.value = permissionTree.value.map(item => item.id)
        }
      } catch (error) {
        console.error('获取权限树失败:', error)
        ElMessage.error('获取权限树失败')
      } finally {
        loading.value = false
      }
    }

    // 获取角色权限
    const fetchRolePermissions = async () => {
      try {
        const roleId = route.params.id
        const res = await getRolePermissions(roleId)
        if (res.data) {
          checkedPermissionIds.value = res.data.map(item => item.id)
          // 检查是否为全选状态
          updateSelectStatus()
        }
      } catch (error) {
        console.error('获取角色权限失败:', error)
        ElMessage.error('获取角色权限失败')
      }
    }

    // 获取权限图标
    const getPermissionIcon = (data) => {
      return permissionIcons[data.type] || 'el-icon-setting'
    }

    // 全选操作
    const handleCheckAllChange = (val) => {
      if (!permissionTreeRef.value) return
      
      if (val) {
        // 全选
        permissionTreeRef.value.setCheckedNodes(getAllPermissions(permissionTree.value))
      } else {
        // 全不选
        permissionTreeRef.value.setCheckedKeys([])
      }
      isIndeterminate.value = false
    }

    // 递归获取所有权限
    const getAllPermissions = (permissions) => {
      let allPermissions = []
      const traverse = (list) => {
        list.forEach(item => {
          allPermissions.push(item)
          if (item.children && item.children.length > 0) {
            traverse(item.children)
          }
        })
      }
      traverse(permissions)
      return allPermissions
    }

    // 展开全部
    const expandAll = () => {
      if (!permissionTreeRef.value) return
      const allKeys = []
      const traverse = (list) => {
        list.forEach(item => {
          allKeys.push(item.id)
          if (item.children && item.children.length > 0) {
            traverse(item.children)
          }
        })
      }
      traverse(permissionTree.value)
      expandedKeys.value = allKeys
    }

    // 折叠全部
    const collapseAll = () => {
      expandedKeys.value = []
    }

    // 处理树节点选择
    const handleTreeCheck = () => {
      updateSelectStatus()
    }

    // 更新选择状态（全选/半选）
    const updateSelectStatus = () => {
      if (!permissionTreeRef.value) return
      
      const allPermissions = getAllPermissions(permissionTree.value)
      const checkedNodes = permissionTreeRef.value.getCheckedNodes()
      
      // 判断是否全选或半选
      if (checkedNodes.length === 0) {
        checkAll.value = false
        isIndeterminate.value = false
      } else if (checkedNodes.length === allPermissions.length) {
        checkAll.value = true
        isIndeterminate.value = false
      } else {
        checkAll.value = false
        isIndeterminate.value = true
      }
    }

    // 保存权限设置
    const handleSave = async () => {
      if (!permissionTreeRef.value) return
      
      // 获取选中的节点ID
      const checkedKeys = permissionTreeRef.value.getCheckedKeys()
      const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
      const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
      
      if (allCheckedKeys.length === 0) {
        ElMessageBox.confirm('您没有选择任何权限，确定要保存吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          savePermissions(allCheckedKeys)
        }).catch(() => {
          // 取消操作
        })
      } else {
        savePermissions(allCheckedKeys)
      }
    }

    // 保存权限到后端
    const savePermissions = async (permissionIds) => {
      const roleId = route.params.id
      loading.value = true
      try {
        await updateRolePermissions(roleId, permissionIds)
        ElMessage.success('权限设置保存成功')
      } catch (error) {
        console.error('保存权限失败:', error)
        ElMessage.error('保存权限失败')
      } finally {
        loading.value = false
      }
    }

    // 返回角色列表
    const goBack = () => {
      router.push('/roles')
    }

    onMounted(async () => {
      await fetchRoleInfo()
      await fetchPermissionTree()
      await fetchRolePermissions()
    })

    return {
      permissionTree,
      permissionTreeRef,
      checkedPermissionIds,
      roleName,
      loading,
      checkAll,
      isIndeterminate,
      expandedKeys,
      getPermissionIcon,
      handleCheckAllChange,
      expandAll,
      collapseAll,
      handleTreeCheck,
      handleSave,
      goBack
    }
  }
}
</script>

<style scoped>
.role-permission-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.permission-operations {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 15px;
}

.permission-operations .el-button {
  margin-left: 15px;
}

.permission-tree-container {
  max-height: 600px;
  overflow-y: auto;
}

.permission-node {
  display: flex;
  align-items: center;
}

.permission-icon {
  margin-right: 5px;
}

.permission-name {
  margin-right: 8px;
}

.permission-code {
  font-size: 12px;
  color: #999;
}
</style> 