-- 完整的大学学生管理系统真实数据初始化脚本
-- 替换所有前端模拟数据，使用真实数据库数据

USE university_management;

-- 清理现有数据（按依赖关系顺序）
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM borrow_record WHERE id > 0;
DELETE FROM student WHERE id > 0;
DELETE FROM teacher WHERE id > 0;
DELETE FROM course WHERE id > 0;
DELETE FROM book WHERE id > 0;
DELETE FROM classroom WHERE id > 0;
DELETE FROM dormitory WHERE id > 0;
DELETE FROM sports_venue WHERE id > 0;
DELETE FROM class WHERE id > 0;
DELETE FROM major WHERE id > 0;
DELETE FROM department WHERE id > 0;
SET FOREIGN_KEY_CHECKS = 1;

-- 1. 插入院系数据
INSERT IGNORE INTO department (department_no, name, description, dean, telephone, email, address, established_date, website, sort, status) VALUES
('CS01', '计算机科学与技术学院', '计算机科学与技术、软件工程、网络工程等专业', '张伟', '0571-88888001', '<EMAIL>', '计算机楼A座', '1985-09-01', 'http://cs.university.edu', 1, 0),
('EE01', '电子信息工程学院', '电子信息工程、通信工程、自动化等专业', '田军', '0571-88888002', '<EMAIL>', '电子楼B座', '1988-09-01', 'http://ee.university.edu', 2, 0),
('ME01', '机械工程学院', '机械设计制造、机械电子工程、车辆工程等专业', '方军', '0571-88888003', '<EMAIL>', '机械楼C座', '1990-09-01', 'http://me.university.edu', 3, 0),
('EM01', '经济管理学院', '工商管理、会计学、市场营销、国际经济与贸易等专业', '龙丽', '0571-88888004', '<EMAIL>', '管理楼D座', '1992-09-01', 'http://em.university.edu', 4, 0),
('FL01', '外国语学院', '英语、日语、德语、法语等专业', '夏华', '0571-88888005', '<EMAIL>', '外语楼E座', '1995-09-01', 'http://fl.university.edu', 5, 0),
('AD01', '艺术设计学院', '视觉传达设计、环境设计、产品设计等专业', '潘军', '0571-88888006', '<EMAIL>', '艺术楼F座', '1998-09-01', 'http://ad.university.edu', 6, 0);

-- 2. 插入专业数据
INSERT IGNORE INTO major (major_no, name, department_id, description, length, degree) VALUES
-- 计算机科学与技术学院专业
('CS0101', '计算机科学与技术', 1, '培养计算机科学与技术领域的高级专门人才', '4年', '工学学士'),
('SE0101', '软件工程', 1, '培养软件开发与工程管理的专业人才', '4年', '工学学士'),
('NE0101', '网络工程', 1, '培养网络系统设计与管理的专业人才', '4年', '工学学士'),
('AI0101', '人工智能', 1, '培养人工智能技术应用的专业人才', '4年', '工学学士'),
('DS0101', '数据科学与大数据技术', 1, '培养大数据分析与处理的专业人才', '4年', '工学学士'),

-- 电子信息工程学院专业
('EE0101', '电子信息工程', 2, '培养电子信息系统设计与应用的专业人才', '4年', '工学学士'),
('TC0101', '通信工程', 2, '培养通信系统与网络技术的专业人才', '4年', '工学学士'),
('AU0101', '自动化', 2, '培养自动控制系统设计的专业人才', '4年', '工学学士'),

-- 机械工程学院专业
('ME0101', '机械设计制造及其自动化', 3, '培养机械设计制造的专业人才', '4年', '工学学士'),
('ME0201', '机械电子工程', 3, '培养机电一体化技术的专业人才', '4年', '工学学士'),
('VE0101', '车辆工程', 3, '培养汽车设计制造的专业人才', '4年', '工学学士'),

-- 经济管理学院专业
('BM0101', '工商管理', 4, '培养企业管理的专业人才', '4年', '管理学学士'),
('AC0101', '会计学', 4, '培养财务会计的专业人才', '4年', '管理学学士'),
('MK0101', '市场营销', 4, '培养市场营销的专业人才', '4年', '管理学学士'),
('IT0101', '国际经济与贸易', 4, '培养国际贸易的专业人才', '4年', '经济学学士'),

-- 外国语学院专业
('EN0101', '英语', 5, '培养英语语言文学的专业人才', '4年', '文学学士'),
('JP0101', '日语', 5, '培养日语语言文学的专业人才', '4年', '文学学士'),

-- 艺术设计学院专业
('VD0101', '视觉传达设计', 6, '培养视觉设计的专业人才', '4年', '艺术学学士'),
('ED0101', '环境设计', 6, '培养环境艺术设计的专业人才', '4年', '艺术学学士');

-- 3. 插入班级数据
INSERT IGNORE INTO class (class_no, name, major_id, department_id, grade, teacher_id, class_size) VALUES
-- 2020级班级
('CS2020-1', '计算机2020-1班', 1, 1, 2020, NULL, 45),
('CS2020-2', '计算机2020-2班', 1, 1, 2020, NULL, 43),
('SE2020-1', '软件工程2020-1班', 2, 1, 2020, NULL, 48),
('SE2020-2', '软件工程2020-2班', 2, 1, 2020, NULL, 46),
('NE2020-1', '网络工程2020-1班', 3, 1, 2020, NULL, 42),

-- 2021级班级
('CS2021-1', '计算机2021-1班', 1, 1, 2021, NULL, 47),
('CS2021-2', '计算机2021-2班', 1, 1, 2021, NULL, 45),
('SE2021-1', '软件工程2021-1班', 2, 1, 2021, NULL, 49),
('NE2021-1', '网络工程2021-1班', 3, 1, 2021, NULL, 44),
('AI2021-1', '人工智能2021-1班', 4, 1, 2021, NULL, 38),

-- 2022级班级
('CS2022-1', '计算机2022-1班', 1, 1, 2022, NULL, 46),
('SE2022-1', '软件工程2022-1班', 2, 1, 2022, NULL, 47),
('NE2022-1', '网络工程2022-1班', 3, 1, 2022, NULL, 43),
('AI2022-1', '人工智能2022-1班', 4, 1, 2022, NULL, 40),
('DS2022-1', '数据科学2022-1班', 5, 1, 2022, NULL, 35),

-- 2023级班级
('CS2023-1', '计算机2023-1班', 1, 1, 2023, NULL, 48),
('SE2023-1', '软件工程2023-1班', 2, 1, 2023, NULL, 50),
('NE2023-1', '网络工程2023-1班', 3, 1, 2023, NULL, 45),
('AI2023-1', '人工智能2023-1班', 4, 1, 2023, NULL, 42),
('DS2023-1', '数据科学2023-1班', 5, 1, 2023, NULL, 38),

-- 其他学院班级
('EE2021-1', '电子信息2021-1班', 6, 2, 2021, NULL, 44),
('TC2021-1', '通信工程2021-1班', 7, 2, 2021, NULL, 41),
('AU2021-1', '自动化2021-1班', 8, 2, 2021, NULL, 43),
('ME2021-1', '机械设计2021-1班', 9, 3, 2021, NULL, 46),
('BM2021-1', '工商管理2021-1班', 12, 4, 2021, NULL, 52),
('AC2021-1', '会计学2021-1班', 13, 4, 2021, NULL, 48),
('EN2021-1', '英语2021-1班', 16, 5, 2021, NULL, 35);

-- 4. 插入教师数据
INSERT IGNORE INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, department_id, hire_date, status) VALUES
-- 计算机科学与技术学院教师
('T001001', '张伟', 1, '1975-03-15', 49, 3, '13900001001', '<EMAIL>', 1, '2000-09-01', 0),
('T001002', '李娜', 0, '1980-07-22', 44, 2, '13900001002', '<EMAIL>', 1, '2005-09-01', 0),
('T001003', '王强', 1, '1978-11-08', 46, 2, '13900001003', '<EMAIL>', 1, '2003-09-01', 0),
('T001004', '刘敏', 0, '1985-05-12', 39, 1, '13900001004', '<EMAIL>', 1, '2010-09-01', 0),
('T001005', '陈杰', 1, '1982-09-25', 42, 1, '13900001005', '<EMAIL>', 1, '2008-09-01', 0),
('T001006', '赵丽', 0, '1977-01-18', 47, 3, '13900001006', '<EMAIL>', 1, '2002-09-01', 0),
('T001007', '孙明', 1, '1983-06-30', 41, 2, '13900001007', '<EMAIL>', 1, '2009-09-01', 0),
('T001008', '周芳', 0, '1986-12-05', 38, 1, '13900001008', '<EMAIL>', 1, '2012-09-01', 0),
('T001009', '吴涛', 1, '1984-04-14', 40, 1, '13900001009', '<EMAIL>', 1, '2011-09-01', 0),
('T001010', '郑华', 1, '1979-08-20', 45, 2, '13900001010', '<EMAIL>', 1, '2004-09-01', 0),

-- 电子信息工程学院教师
('T002001', '田军', 1, '1976-05-10', 48, 3, '13900002001', '<EMAIL>', 2, '2001-09-01', 0),
('T002002', '邓红', 0, '1982-09-18', 42, 2, '13900002002', '<EMAIL>', 2, '2008-09-01', 0),
('T002003', '韩斌', 1, '1984-01-25', 40, 1, '13900002003', '<EMAIL>', 2, '2011-09-01', 0),
('T002004', '曹敏', 0, '1980-12-03', 44, 2, '13900002004', '<EMAIL>', 2, '2006-09-01', 0),
('T002005', '蒋涛', 1, '1986-08-16', 38, 1, '13900002005', '<EMAIL>', 2, '2013-09-01', 0),

-- 机械工程学院教师
('T003001', '方军', 1, '1974-02-20', 50, 3, '13900003001', '<EMAIL>', 3, '1999-09-01', 0),
('T003002', '石红', 0, '1981-06-14', 43, 2, '13900003002', '<EMAIL>', 3, '2007-09-01', 0),
('T003003', '袁斌', 1, '1987-11-30', 37, 1, '13900003003', '<EMAIL>', 3, '2014-09-01', 0),

-- 经济管理学院教师
('T004001', '龙丽', 0, '1979-03-25', 45, 2, '13900004001', '<EMAIL>', 4, '2004-09-01', 0),
('T004002', '段军', 1, '1983-07-08', 41, 1, '13900004002', '<EMAIL>', 4, '2010-09-01', 0),
('T004003', '史敏', 0, '1982-05-16', 42, 2, '13900004003', '<EMAIL>', 4, '2008-09-01', 0),
('T004004', '顾涛', 1, '1986-09-22', 38, 1, '13900004004', '<EMAIL>', 4, '2013-09-01', 0),

-- 外国语学院教师
('T005001', '夏华', 0, '1980-01-12', 44, 2, '13900005001', '<EMAIL>', 5, '2006-09-01', 0),
('T005002', '侯斌', 1, '1984-08-05', 40, 1, '13900005002', '<EMAIL>', 5, '2011-09-01', 0),
('T005003', '谭丽', 0, '1985-12-18', 39, 1, '13900005003', '<EMAIL>', 5, '2012-09-01', 0),

-- 艺术设计学院教师
('T006001', '潘军', 1, '1981-04-28', 43, 2, '13900006001', '<EMAIL>', 6, '2007-09-01', 0),
('T006002', '崔敏', 0, '1987-10-15', 37, 1, '13900006002', '<EMAIL>', 6, '2014-09-01', 0);

-- 5. 插入学生数据
INSERT IGNORE INTO student (student_no, name, gender, birthday, age, phone, email, major_id, class_id, department_id, enroll_year, status) VALUES
-- 计算机科学与技术专业学生 (2020级)
('S20200101', '张小明', 1, '2002-03-15', 22, '13800001001', '<EMAIL>', 1, 1, 1, 2020, 0),
('S20200102', '李小红', 0, '2002-05-20', 22, '13800001002', '<EMAIL>', 1, 1, 1, 2020, 0),
('S20200103', '王小刚', 1, '2002-07-10', 22, '13800001003', '<EMAIL>', 1, 1, 1, 2020, 0),
('S20200104', '赵小丽', 0, '2002-09-25', 22, '13800001004', '<EMAIL>', 1, 1, 1, 2020, 0),
('S20200105', '刘小强', 1, '2002-11-30', 22, '13800001005', '<EMAIL>', 1, 1, 1, 2020, 0),
('S20200106', '陈小华', 1, '2002-01-12', 22, '13800001006', '<EMAIL>', 1, 2, 1, 2020, 0),
('S20200107', '周小芳', 0, '2002-04-18', 22, '13800001007', '<EMAIL>', 1, 2, 1, 2020, 0),
('S20200108', '吴小军', 1, '2002-06-22', 22, '13800001008', '<EMAIL>', 1, 2, 1, 2020, 0),
('S20200109', '郑小燕', 0, '2002-08-14', 22, '13800001009', '<EMAIL>', 1, 2, 1, 2020, 0),
('S20200110', '孙小龙', 1, '2002-02-28', 22, '13800001010', '<EMAIL>', 1, 2, 1, 2020, 0),

-- 软件工程专业学生 (2020级)
('S20200201', '马小梅', 0, '2002-10-05', 22, '13800002001', '<EMAIL>', 2, 3, 2, 2020, 0),
('S20200202', '林小伟', 1, '2002-01-15', 22, '13800002002', '<EMAIL>', 2, 3, 2, 2020, 0),
('S20200203', '何小静', 0, '2002-03-20', 22, '13800002003', '<EMAIL>', 2, 3, 2, 2020, 0),
('S20200204', '高小峰', 1, '2002-05-10', 22, '13800002004', '<EMAIL>', 2, 3, 2, 2020, 0),
('S20200205', '许小娟', 0, '2002-07-25', 22, '13800002005', '<EMAIL>', 2, 4, 2, 2020, 0),
('S20200206', '田小勇', 1, '2002-02-14', 22, '13800002006', '<EMAIL>', 2, 4, 2, 2020, 0),
('S20200207', '邓小玲', 0, '2002-04-18', 22, '13800002007', '<EMAIL>', 2, 4, 2, 2020, 0),
('S20200208', '韩小斌', 1, '2002-06-22', 22, '13800002008', '<EMAIL>', 2, 4, 2, 2020, 0),

-- 网络工程专业学生 (2020级)
('S20200301', '曹小敏', 0, '2002-08-30', 22, '13800003001', '<EMAIL>', 3, 5, 3, 2020, 0),
('S20200302', '蒋小涛', 1, '2002-01-10', 22, '13800003002', '<EMAIL>', 3, 5, 3, 2020, 0),
('S20200303', '薛小慧', 0, '2002-03-15', 22, '13800003003', '<EMAIL>', 3, 5, 3, 2020, 0),
('S20200304', '雷小鹏', 1, '2002-05-20', 22, '13800003004', '<EMAIL>', 3, 5, 3, 2020, 0),

-- 计算机科学与技术专业学生 (2021级)
('S20210101', '方小兰', 0, '2003-07-25', 21, '13800101001', '<EMAIL>', 1, 6, 1, 2021, 0),
('S20210102', '石小军', 1, '2003-09-30', 21, '13800101002', '<EMAIL>', 1, 6, 1, 2021, 0),
('S20210103', '袁小丽', 0, '2003-11-12', 21, '13800101003', '<EMAIL>', 1, 6, 1, 2021, 0),
('S20210104', '龙小峰', 1, '2003-01-08', 21, '13800101004', '<EMAIL>', 1, 7, 1, 2021, 0),
('S20210105', '段小梅', 0, '2003-03-22', 21, '13800101005', '<EMAIL>', 1, 7, 1, 2021, 0),
('S20210106', '史小强', 1, '2003-05-16', 21, '13800101006', '<EMAIL>', 1, 7, 1, 2021, 0),

-- 软件工程专业学生 (2021级)
('S20210201', '顾小华', 0, '2003-02-12', 21, '13800201001', '<EMAIL>', 2, 8, 2, 2021, 0),
('S20210202', '夏小军', 1, '2003-04-28', 21, '13800201002', '<EMAIL>', 2, 8, 2, 2021, 0),
('S20210203', '侯小丽', 0, '2003-06-15', 21, '13800201003', '<EMAIL>', 2, 8, 2, 2021, 0),
('S20210204', '谭小斌', 1, '2003-08-20', 21, '13800201004', '<EMAIL>', 2, 8, 2, 2021, 0),

-- 网络工程专业学生 (2021级)
('S20210301', '潘小红', 0, '2003-10-05', 21, '13800301001', '<EMAIL>', 3, 9, 3, 2021, 0),
('S20210302', '崔小伟', 1, '2003-12-18', 21, '13800301002', '<EMAIL>', 3, 9, 3, 2021, 0),
('S20210303', '秦小敏', 0, '2003-02-25', 21, '13800301003', '<EMAIL>', 3, 9, 3, 2021, 0),

-- 人工智能专业学生 (2021级)
('S20210401', '尹小涛', 1, '2003-04-10', 21, '13800401001', '<EMAIL>', 4, 10, 4, 2021, 0),
('S20210402', '江小慧', 0, '2003-06-28', 21, '13800401002', '<EMAIL>', 4, 10, 4, 2021, 0),
('S20210403', '常小鹏', 1, '2003-08-14', 21, '13800401003', '<EMAIL>', 4, 10, 4, 2021, 0),

-- 计算机科学与技术专业学生 (2022级)
('S20220101', '汤小兰', 0, '2004-01-20', 20, '13800111001', '<EMAIL>', 1, 11, 1, 2022, 0),
('S20220102', '易小军', 1, '2004-03-15', 20, '13800111002', '<EMAIL>', 1, 11, 1, 2022, 0),
('S20220103', '罗小丽', 0, '2004-05-22', 20, '13800111003', '<EMAIL>', 1, 11, 1, 2022, 0),

-- 软件工程专业学生 (2022级)
('S20220201', '金小华', 1, '2004-02-08', 20, '13800211001', '<EMAIL>', 2, 12, 2, 2022, 0),
('S20220202', '戴小梅', 0, '2004-04-25', 20, '13800211002', '<EMAIL>', 2, 12, 2, 2022, 0),
('S20220203', '贺小强', 1, '2004-06-12', 20, '13800211003', '<EMAIL>', 2, 12, 2, 2022, 0),

-- 网络工程专业学生 (2022级)
('S20220301', '傅小红', 0, '2004-07-30', 20, '13800311001', '<EMAIL>', 3, 13, 3, 2022, 0),
('S20220302', '卢小伟', 1, '2004-09-18', 20, '13800311002', '<EMAIL>', 3, 13, 3, 2022, 0),

-- 人工智能专业学生 (2022级)
('S20220401', '水小敏', 0, '2004-11-05', 20, '13800411001', '<EMAIL>', 4, 14, 4, 2022, 0),
('S20220402', '花小涛', 1, '2004-01-28', 20, '13800411002', '<EMAIL>', 4, 14, 4, 2022, 0),

-- 数据科学与大数据技术专业学生 (2022级)
('S20220501', '苗小慧', 0, '2004-03-12', 20, '13800511001', '<EMAIL>', 5, 15, 4, 2022, 0),
('S20220502', '岑小鹏', 1, '2004-05-28', 20, '13800511002', '<EMAIL>', 5, 15, 4, 2022, 0);

-- 6. 插入课程数据
INSERT IGNORE INTO course (course_no, name, credit, hours, type, department_id, description, status) VALUES
-- 计算机科学与技术学院课程
('CS101', '程序设计基础', 3.0, 48, 0, 1, '计算机程序设计的基础课程，学习编程思维和基本语法', 0),
('CS102', '数据结构', 4.0, 64, 0, 1, '学习各种数据结构的原理和应用', 0),
('CS103', '算法分析与设计', 3.0, 48, 0, 1, '算法设计技巧和复杂度分析', 0),
('CS104', '计算机组成原理', 4.0, 64, 0, 1, '计算机硬件系统的组成和工作原理', 0),
('CS105', '操作系统', 4.0, 64, 0, 1, '操作系统的基本概念、原理和实现技术', 0),
('CS106', '计算机网络', 3.0, 48, 0, 1, '计算机网络的体系结构和协议', 0),
('CS107', '数据库系统', 3.0, 48, 0, 1, '数据库系统的设计、实现和应用', 0),
('CS108', '编译原理', 3.0, 48, 1, 1, '编译器的设计原理和实现技术', 0),
('CS109', '软件工程', 3.0, 48, 0, 1, '软件开发的工程化方法和管理', 0),
('CS110', '人工智能导论', 2.0, 32, 1, 1, '人工智能的基本概念和应用', 0),

-- 软件工程系课程
('SE101', '面向对象程序设计', 3.0, 48, 0, 2, 'Java/C++面向对象编程技术', 0),
('SE102', '软件需求工程', 2.0, 32, 0, 2, '软件需求分析和管理方法', 0),
('SE103', '软件架构设计', 3.0, 48, 0, 2, '软件系统架构设计原理和模式', 0),
('SE104', '软件测试', 2.0, 32, 0, 2, '软件测试理论、方法和工具', 0),
('SE105', '项目管理', 2.0, 32, 1, 2, '软件项目管理的理论和实践', 0),
('SE106', 'Web开发技术', 3.0, 48, 0, 2, '前端和后端Web开发技术', 0),
('SE107', '移动应用开发', 3.0, 48, 1, 2, 'Android/iOS移动应用开发', 0),
('SE108', '软件质量保证', 2.0, 32, 1, 2, '软件质量管理和保证方法', 0),

-- 网络工程系课程
('NE101', '网络协议分析', 3.0, 48, 0, 3, 'TCP/IP协议栈分析和应用', 0),
('NE102', '网络安全', 3.0, 48, 0, 3, '网络安全威胁、防护技术和管理', 0),
('NE103', '路由交换技术', 3.0, 48, 0, 3, '路由器和交换机的配置和管理', 0),
('NE104', '无线网络技术', 2.0, 32, 1, 3, '无线通信和移动网络技术', 0),
('NE105', '网络管理', 2.0, 32, 1, 3, '网络监控、配置和故障管理', 0),
('NE106', '云计算技术', 3.0, 48, 1, 3, '云计算平台和服务技术', 0),

-- 人工智能系课程
('AI101', '机器学习', 3.0, 48, 0, 4, '机器学习算法和应用', 0),
('AI102', '深度学习', 3.0, 48, 0, 4, '神经网络和深度学习技术', 0),
('AI103', '计算机视觉', 2.0, 32, 1, 4, '图像处理和计算机视觉算法', 0),
('AI104', '自然语言处理', 2.0, 32, 1, 4, '文本处理和语言理解技术', 0),
('AI105', '数据挖掘', 3.0, 48, 0, 4, '数据挖掘算法和应用', 0),
('AI106', '大数据技术', 3.0, 48, 0, 4, '大数据存储、处理和分析技术', 0),

-- 电子信息工程学院课程
('EE101', '电路分析', 4.0, 64, 0, 5, '电路的基本理论和分析方法', 0),
('EE102', '模拟电子技术', 3.0, 48, 0, 5, '模拟电子电路的分析和设计', 0),
('EE103', '数字电子技术', 3.0, 48, 0, 5, '数字电路的原理和应用', 0),
('EE104', '信号与系统', 3.0, 48, 0, 5, '信号处理和系统分析理论', 0),
('EE105', '通信原理', 4.0, 64, 0, 6, '通信系统的基本原理和技术', 0),
('EE106', '数字信号处理', 3.0, 48, 0, 6, '数字信号处理算法和应用', 0),
('EE107', '自动控制原理', 4.0, 64, 0, 7, '自动控制系统的分析和设计', 0),
('EE108', '现代控制理论', 3.0, 48, 1, 7, '现代控制理论和方法', 0),

-- 机械工程学院课程
('ME101', '机械制图', 3.0, 48, 0, 8, '机械图样的绘制和识读', 0),
('ME102', '理论力学', 4.0, 64, 0, 8, '静力学、运动学和动力学基础', 0),
('ME103', '材料力学', 4.0, 64, 0, 8, '材料的力学性能和强度计算', 0),
('ME104', '机械设计', 4.0, 64, 0, 8, '机械零件和传动装置设计', 0),
('ME105', '机械制造技术', 3.0, 48, 0, 8, '机械加工工艺和装备', 0),
('ME106', '数控技术', 3.0, 48, 1, 8, '数控机床编程和操作', 0),

-- 经济管理学院课程
('EM101', '管理学原理', 3.0, 48, 0, 11, '管理学的基本理论和方法', 0),
('EM102', '微观经济学', 3.0, 48, 0, 11, '微观经济理论和市场分析', 0),
('EM103', '宏观经济学', 3.0, 48, 0, 11, '宏观经济理论和政策分析', 0),
('EM104', '会计学原理', 3.0, 48, 0, 12, '会计的基本理论和方法', 0),
('EM105', '财务管理', 3.0, 48, 0, 12, '企业财务决策和管理', 0),
('EM106', '市场营销', 3.0, 48, 0, 13, '市场营销理论和实务', 0),

-- 外国语学院课程
('FL101', '综合英语', 4.0, 64, 0, 14, '英语综合技能训练', 0),
('FL102', '英语听力', 2.0, 32, 0, 14, '英语听力技能训练', 0),
('FL103', '英语口语', 2.0, 32, 0, 14, '英语口语表达训练', 0),
('FL104', '英语写作', 2.0, 32, 0, 14, '英语写作技能训练', 0),
('FL105', '基础日语', 4.0, 64, 0, 15, '日语基础语法和词汇', 0),
('FL106', '日语会话', 2.0, 32, 0, 15, '日语口语交际训练', 0);
