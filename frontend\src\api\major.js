import request from '@/utils/request'

const baseUrl = '/api/majors'

// 获取所有专业信息
export function getAllMajors() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取专业信息
export function getMajorsByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询专业信息
export function searchMajors({ name, code, departmentId, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      code,
      departmentId,
      page,
      size
    }
  })
}

// 根据ID获取专业信息
export function getMajorById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建专业信息
export function createMajor(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新专业信息
export function updateMajor(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除专业信息
export function deleteMajor(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 根据院系ID获取专业列表
export function getMajorsByDepartment(departmentId) {
  return request({
    url: `${baseUrl}/department/${departmentId}`,
    method: 'get'
  })
}

// 获取专业统计信息
export function getMajorStats() {
  return request({
    url: `${baseUrl}/stats`,
    method: 'get'
  })
}
