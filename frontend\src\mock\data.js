/**
 * 大学学生管理系统模拟数据
 * 用于前端开发和测试
 */

// 生成随机ID
const generateId = () => Math.floor(Math.random() * 10000) + 1

// 生成随机日期
const generateDate = (startYear = 2020, endYear = 2024) => {
  const start = new Date(startYear, 0, 1)
  const end = new Date(endYear, 11, 31)
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
  return date.toISOString().split('T')[0]
}

// 生成随机手机号
const generatePhone = () => {
  const prefixes = ['138', '139', '150', '151', '152', '158', '159', '188', '189']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0')
  return prefix + suffix
}

// 生成随机邮箱
const generateEmail = (name, domain = 'university.edu') => {
  const pinyin = name.replace(/[\u4e00-\u9fa5]/g, (char) => {
    // 简单的中文转拼音映射
    const pinyinMap = {
      '张': 'zhang', '李': 'li', '王': 'wang', '赵': 'zhao', '刘': 'liu',
      '陈': 'chen', '杨': 'yang', '黄': 'huang', '周': 'zhou', '吴': 'wu',
      '徐': 'xu', '孙': 'sun', '马': 'ma', '朱': 'zhu', '胡': 'hu',
      '郭': 'guo', '何': 'he', '高': 'gao', '林': 'lin', '罗': 'luo',
      '小': 'xiao', '明': 'ming', '红': 'hong', '刚': 'gang', '芳': 'fang',
      '强': 'qiang', '军': 'jun', '敏': 'min', '静': 'jing', '丽': 'li',
      '华': 'hua', '伟': 'wei', '建': 'jian', '国': 'guo', '文': 'wen',
      '三': 'san', '四': 'si', '五': 'wu', '六': 'liu', '七': 'qi',
      '八': 'ba', '九': 'jiu', '十': 'shi'
    }
    return pinyinMap[char] || char
  })
  return `${pinyin}@${domain}`
}

// 院系数据
export const departments = [
  {
    id: 1,
    code: 'CS',
    name: '计算机科学与技术学院',
    dean: '张教授',
    phone: '010-12345678',
    email: '<EMAIL>',
    establishedDate: '1985-09-01',
    status: 'ACTIVE',
    description: '计算机科学与技术学院成立于1985年，是国家重点学院。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    code: 'MATH',
    name: '数学与统计学院',
    dean: '李教授',
    phone: '010-12345679',
    email: '<EMAIL>',
    establishedDate: '1978-09-01',
    status: 'ACTIVE',
    description: '数学与统计学院是国家级重点学科。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    code: 'PHY',
    name: '物理学院',
    dean: '王教授',
    phone: '010-12345680',
    email: '<EMAIL>',
    establishedDate: '1960-09-01',
    status: 'ACTIVE',
    description: '物理学院拥有多个国家重点实验室。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    code: 'CHEM',
    name: '化学学院',
    dean: '刘教授',
    phone: '010-12345681',
    email: '<EMAIL>',
    establishedDate: '1965-09-01',
    status: 'ACTIVE',
    description: '化学学院在有机化学领域享有盛誉。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    code: 'BIO',
    name: '生物学院',
    dean: '陈教授',
    phone: '010-12345682',
    email: '<EMAIL>',
    establishedDate: '1970-09-01',
    status: 'ACTIVE',
    description: '生物学院在生物技术研究方面处于领先地位。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 6,
    code: 'ENG',
    name: '工程学院',
    dean: '杨教授',
    phone: '010-12345683',
    email: '<EMAIL>',
    establishedDate: '1980-09-01',
    status: 'ACTIVE',
    description: '工程学院培养高素质工程技术人才。',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 专业数据
export const majors = [
  {
    id: 1,
    code: 'CS01',
    name: '计算机科学与技术',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 120,
    description: '培养具有扎实计算机理论基础和实践能力的高级人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    code: 'CS02',
    name: '软件工程',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 100,
    description: '培养软件开发和项目管理的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    code: 'CS03',
    name: '网络工程',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 80,
    description: '培养网络设计、管理和安全的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    code: 'CS04',
    name: '人工智能',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 90,
    description: '培养人工智能算法和应用开发的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    code: 'MATH01',
    name: '数学与应用数学',
    departmentId: 2,
    departmentName: '数学与统计学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 70,
    description: '培养数学理论研究和应用的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 6,
    code: 'MATH02',
    name: '统计学',
    departmentId: 2,
    departmentName: '数学与统计学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 60,
    description: '培养数据分析和统计建模的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 7,
    code: 'PHY01',
    name: '物理学',
    departmentId: 3,
    departmentName: '物理学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 50,
    description: '培养物理学理论研究和实验的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 8,
    code: 'PHY02',
    name: '应用物理学',
    departmentId: 3,
    departmentName: '物理学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 45,
    description: '培养物理学在工程技术中应用的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 9,
    code: 'CHEM01',
    name: '化学',
    departmentId: 4,
    departmentName: '化学学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 55,
    description: '培养化学理论研究和实验的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 10,
    code: 'BIO01',
    name: '生物科学',
    departmentId: 5,
    departmentName: '生物学院',
    degree: 'BACHELOR',
    duration: 4,
    status: 'ACTIVE',
    studentCount: 65,
    description: '培养生物科学研究和应用的专业人才。',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 班级数据
export const classes = [
  {
    id: 1,
    name: '计算机2021级1班',
    grade: '2021',
    majorId: 1,
    majorName: '计算机科学与技术',
    advisorId: 1,
    advisorName: '张三',
    maxStudents: 30,
    studentCount: 28,
    classroom: 'A101',
    status: 'ACTIVE',
    description: '计算机科学与技术专业2021级第一班',
    createdAt: '2021-09-01T00:00:00'
  },
  {
    id: 2,
    name: '计算机2021级2班',
    grade: '2021',
    majorId: 1,
    majorName: '计算机科学与技术',
    advisorId: 2,
    advisorName: '李四',
    maxStudents: 30,
    studentCount: 29,
    classroom: 'A102',
    status: 'ACTIVE',
    description: '计算机科学与技术专业2021级第二班',
    createdAt: '2021-09-01T00:00:00'
  },
  {
    id: 3,
    name: '软件2021级1班',
    grade: '2021',
    majorId: 2,
    majorName: '软件工程',
    advisorId: 3,
    advisorName: '王五',
    maxStudents: 30,
    studentCount: 27,
    classroom: 'A103',
    status: 'ACTIVE',
    description: '软件工程专业2021级第一班',
    createdAt: '2021-09-01T00:00:00'
  },
  {
    id: 4,
    name: '网络2021级1班',
    grade: '2021',
    majorId: 3,
    majorName: '网络工程',
    advisorId: 4,
    advisorName: '赵六',
    maxStudents: 30,
    studentCount: 26,
    classroom: 'A104',
    status: 'ACTIVE',
    description: '网络工程专业2021级第一班',
    createdAt: '2021-09-01T00:00:00'
  },
  {
    id: 5,
    name: '人工智能2022级1班',
    grade: '2022',
    majorId: 4,
    majorName: '人工智能',
    advisorId: 5,
    advisorName: '刘七',
    maxStudents: 30,
    studentCount: 30,
    classroom: 'A105',
    status: 'ACTIVE',
    description: '人工智能专业2022级第一班',
    createdAt: '2022-09-01T00:00:00'
  },
  {
    id: 6,
    name: '数学2022级1班',
    grade: '2022',
    majorId: 5,
    majorName: '数学与应用数学',
    advisorId: 6,
    advisorName: '陈八',
    maxStudents: 25,
    studentCount: 23,
    classroom: 'B101',
    status: 'ACTIVE',
    description: '数学与应用数学专业2022级第一班',
    createdAt: '2022-09-01T00:00:00'
  },
  {
    id: 7,
    name: '统计2022级1班',
    grade: '2022',
    majorId: 6,
    majorName: '统计学',
    advisorId: 7,
    advisorName: '杨九',
    maxStudents: 25,
    studentCount: 24,
    classroom: 'B102',
    status: 'ACTIVE',
    description: '统计学专业2022级第一班',
    createdAt: '2022-09-01T00:00:00'
  },
  {
    id: 8,
    name: '物理2023级1班',
    grade: '2023',
    majorId: 7,
    majorName: '物理学',
    advisorId: 8,
    advisorName: '黄十',
    maxStudents: 25,
    studentCount: 22,
    classroom: 'C101',
    status: 'ACTIVE',
    description: '物理学专业2023级第一班',
    createdAt: '2023-09-01T00:00:00'
  },
  {
    id: 9,
    name: '化学2023级1班',
    grade: '2023',
    majorId: 9,
    majorName: '化学',
    advisorId: 9,
    advisorName: '周十一',
    maxStudents: 25,
    studentCount: 25,
    classroom: 'D101',
    status: 'ACTIVE',
    description: '化学专业2023级第一班',
    createdAt: '2023-09-01T00:00:00'
  },
  {
    id: 10,
    name: '生物2023级1班',
    grade: '2023',
    majorId: 10,
    majorName: '生物科学',
    advisorId: 10,
    advisorName: '吴十二',
    maxStudents: 25,
    studentCount: 24,
    classroom: 'E101',
    status: 'ACTIVE',
    description: '生物科学专业2023级第一班',
    createdAt: '2023-09-01T00:00:00'
  }
]

// 生成学生姓名
const studentNames = [
  '张小明', '李小红', '王小刚', '赵小芳', '刘小强', '陈小军', '杨小敏', '黄小静',
  '周小丽', '吴小华', '徐小伟', '孙小建', '马小国', '朱小文', '胡小娟', '郭小涛',
  '何小燕', '高小峰', '林小雨', '罗小雪', '宋小阳', '谢小月', '韩小星', '冯小云',
  '于小海', '董小山', '梁小川', '蒋小河', '魏小林', '薛小花', '叶小草', '阎小树',
  '余小石', '潘小金', '杜小银', '戴小铜', '夏小铁', '钟小钢', '汪小铝', '田小锌',
  '任小锡', '姜小铅', '范小汞', '方小镁', '石小钙', '姚小钠', '谭小钾', '廖小氯',
  '邹小氟', '熊小溴', '金小碘', '路小氦', '江小氖', '童小氩', '颜小氪', '郝小氙'
]

// 生成教师姓名
const teacherNames = [
  '张三', '李四', '王五', '赵六', '刘七', '陈八', '杨九', '黄十',
  '周十一', '吴十二', '徐教授', '孙博士', '马老师', '朱副教授', '胡讲师', '郭研究员',
  '何主任', '高院长', '林所长', '罗主管', '宋导师', '谢专家', '韩学者', '冯顾问'
]

// 生成学生数据
export const students = []
for (let i = 0; i < 50; i++) {
  const name = studentNames[i % studentNames.length]
  const classInfo = classes[i % classes.length]
  const majorInfo = majors.find(m => m.id === classInfo.majorId)
  const enrollYear = parseInt(classInfo.grade)
  const age = new Date().getFullYear() - enrollYear + 18

  students.push({
    id: i + 1,
    studentNo: `S${enrollYear}${(i + 1).toString().padStart(4, '0')}`,
    name: name + (i > studentNames.length - 1 ? (Math.floor(i / studentNames.length) + 1) : ''),
    gender: i % 2 === 0 ? '男' : '女',
    age: age,
    birthday: `${enrollYear - 18}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    idCard: `33010${enrollYear - 18}${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 9000) + 1000)}`,
    phone: generatePhone(),
    email: generateEmail(name, 'student.edu'),
    address: ['杭州市西湖区', '杭州市拱墅区', '杭州市滨江区', '杭州市上城区', '杭州市下城区', '杭州市江干区'][i % 6],
    classId: classInfo.id,
    className: classInfo.name,
    majorId: majorInfo.id,
    majorName: majorInfo.name,
    departmentId: majorInfo.departmentId,
    departmentName: majorInfo.departmentName,
    enrollYear: enrollYear,
    status: Math.random() > 0.1 ? '在校' : '休学',
    dormitoryId: Math.floor(Math.random() * 20) + 1,
    dormitoryNo: `D${Math.floor(Math.random() * 3) + 1}-${Math.floor(Math.random() * 3) + 1}${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
    gpa: (Math.random() * 2 + 2).toFixed(2), // 2.0-4.0
    totalCredits: Math.floor(Math.random() * 50) + 100,
    createdAt: `${enrollYear}-09-01T00:00:00`
  })
}

// 生成教师数据
export const teachers = []
const titles = ['助教', '讲师', '副教授', '教授']
const titleCodes = ['ASSISTANT', 'LECTURER', 'ASSOCIATE_PROFESSOR', 'PROFESSOR']

for (let i = 0; i < 30; i++) {
  const name = teacherNames[i % teacherNames.length]
  const dept = departments[i % departments.length]
  const titleIndex = Math.floor(Math.random() * titles.length)
  const hireYear = 2000 + Math.floor(Math.random() * 24)
  const age = Math.floor(Math.random() * 20) + 35

  teachers.push({
    id: i + 1,
    teacherNo: `T${String(i + 1).padStart(3, '0')}`,
    name: name + (i > teacherNames.length - 1 ? (Math.floor(i / teacherNames.length) + 1) : ''),
    gender: i % 3 === 0 ? '女' : '男',
    age: age,
    birthday: `${new Date().getFullYear() - age}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    idCard: `33010${new Date().getFullYear() - age}${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}${String(Math.floor(Math.random() * 9000) + 1000)}`,
    phone: generatePhone(),
    email: generateEmail(name, 'university.edu'),
    title: titles[titleIndex],
    titleCode: titleCodes[titleIndex],
    departmentId: dept.id,
    departmentName: dept.name,
    hireDate: `${hireYear}-07-01`,
    status: Math.random() > 0.05 ? '在职' : '离职',
    officeLocation: `${dept.code}楼${Math.floor(Math.random() * 5) + 1}${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
    researchArea: ['人工智能', '数据挖掘', '软件工程', '网络安全', '数据库', '算法设计', '机器学习', '计算机视觉'][Math.floor(Math.random() * 8)],
    education: ['博士', '硕士', '学士'][Math.floor(Math.random() * 3)],
    graduateSchool: ['清华大学', '北京大学', '浙江大学', '上海交通大学', '复旦大学', '中科院'][Math.floor(Math.random() * 6)],
    createdAt: `${hireYear}-07-01T00:00:00`
  })
}

// 生成课程数据
export const courses = [
  {
    id: 1,
    courseNo: 'CS101',
    name: '程序设计基础',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 3,
    hours: 48,
    type: '必修',
    semester: '第一学期',
    description: '计算机程序设计的基础课程，学习编程思维和基本语法。',
    teacherId: 1,
    teacherName: '张三',
    maxStudents: 60,
    enrolledStudents: 55,
    classroom: 'A101',
    schedule: '周一 8:00-9:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    courseNo: 'CS102',
    name: '数据结构',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 4,
    hours: 64,
    type: '必修',
    semester: '第二学期',
    description: '学习各种数据结构的原理和应用。',
    teacherId: 2,
    teacherName: '李四',
    maxStudents: 60,
    enrolledStudents: 58,
    classroom: 'A102',
    schedule: '周二 8:00-9:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    courseNo: 'CS103',
    name: '算法设计与分析',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 3,
    hours: 48,
    type: '必修',
    semester: '第三学期',
    description: '学习算法设计的基本方法和复杂度分析。',
    teacherId: 3,
    teacherName: '王五',
    maxStudents: 50,
    enrolledStudents: 45,
    classroom: 'A103',
    schedule: '周三 8:00-9:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    courseNo: 'CS201',
    name: '数据库系统',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 3,
    hours: 48,
    type: '必修',
    semester: '第四学期',
    description: '学习数据库的设计、实现和管理。',
    teacherId: 4,
    teacherName: '赵六',
    maxStudents: 50,
    enrolledStudents: 48,
    classroom: 'A104',
    schedule: '周四 8:00-9:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    courseNo: 'CS202',
    name: '计算机网络',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 3,
    hours: 48,
    type: '必修',
    semester: '第五学期',
    description: '学习计算机网络的原理和协议。',
    teacherId: 5,
    teacherName: '刘七',
    maxStudents: 50,
    enrolledStudents: 47,
    classroom: 'A105',
    schedule: '周五 8:00-9:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 6,
    courseNo: 'MATH101',
    name: '高等数学A',
    departmentId: 2,
    departmentName: '数学与统计学院',
    credit: 5,
    hours: 80,
    type: '必修',
    semester: '第一学期',
    description: '高等数学的基础课程。',
    teacherId: 6,
    teacherName: '陈八',
    maxStudents: 80,
    enrolledStudents: 75,
    classroom: 'B101',
    schedule: '周一 10:00-11:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 7,
    courseNo: 'MATH102',
    name: '线性代数',
    departmentId: 2,
    departmentName: '数学与统计学院',
    credit: 3,
    hours: 48,
    type: '必修',
    semester: '第二学期',
    description: '学习线性代数的基本理论和方法。',
    teacherId: 7,
    teacherName: '杨九',
    maxStudents: 70,
    enrolledStudents: 68,
    classroom: 'B102',
    schedule: '周二 10:00-11:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 8,
    courseNo: 'PHY101',
    name: '大学物理',
    departmentId: 3,
    departmentName: '物理学院',
    credit: 4,
    hours: 64,
    type: '必修',
    semester: '第二学期',
    description: '大学物理的基础课程。',
    teacherId: 8,
    teacherName: '黄十',
    maxStudents: 60,
    enrolledStudents: 55,
    classroom: 'C101',
    schedule: '周三 10:00-11:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 9,
    courseNo: 'ENG101',
    name: '大学英语',
    departmentId: 6,
    departmentName: '工程学院',
    credit: 2,
    hours: 32,
    type: '必修',
    semester: '第一学期',
    description: '提高英语听说读写能力。',
    teacherId: 9,
    teacherName: '周十一',
    maxStudents: 40,
    enrolledStudents: 38,
    classroom: 'D101',
    schedule: '周四 10:00-11:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 10,
    courseNo: 'CS301',
    name: '人工智能导论',
    departmentId: 1,
    departmentName: '计算机科学与技术学院',
    credit: 3,
    hours: 48,
    type: '选修',
    semester: '第六学期',
    description: '人工智能的基本概念和方法。',
    teacherId: 10,
    teacherName: '吴十二',
    maxStudents: 40,
    enrolledStudents: 35,
    classroom: 'A106',
    schedule: '周五 10:00-11:40',
    status: 'ACTIVE',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 生成图书数据
export const books = [
  {
    id: 1,
    bookNo: 'B001',
    title: 'Java程序设计',
    author: '张三',
    publisher: '清华大学出版社',
    publishDate: '2023-01-01',
    isbn: '9787302123456',
    category: '计算机',
    price: 59.80,
    totalCopies: 10,
    availableCopies: 7,
    location: 'A区1层',
    description: 'Java编程语言的入门教程',
    status: 'AVAILABLE',
    createdAt: '2023-01-01T00:00:00'
  },
  {
    id: 2,
    bookNo: 'B002',
    title: '数据结构与算法',
    author: '李四',
    publisher: '机械工业出版社',
    publishDate: '2023-02-01',
    isbn: '9787111234567',
    category: '计算机',
    price: 68.00,
    totalCopies: 15,
    availableCopies: 12,
    location: 'A区1层',
    description: '数据结构和算法的经典教材',
    status: 'AVAILABLE',
    createdAt: '2023-02-01T00:00:00'
  },
  {
    id: 3,
    bookNo: 'B003',
    title: '计算机网络',
    author: '王五',
    publisher: '电子工业出版社',
    publishDate: '2023-03-01',
    isbn: '9787121345678',
    category: '计算机',
    price: 72.50,
    totalCopies: 12,
    availableCopies: 8,
    location: 'A区1层',
    description: '计算机网络原理与技术',
    status: 'AVAILABLE',
    createdAt: '2023-03-01T00:00:00'
  },
  {
    id: 4,
    bookNo: 'B004',
    title: '高等数学',
    author: '赵六',
    publisher: '高等教育出版社',
    publishDate: '2023-04-01',
    isbn: '9787040456789',
    category: '数学',
    price: 45.00,
    totalCopies: 20,
    availableCopies: 15,
    location: 'B区2层',
    description: '高等数学教程',
    status: 'AVAILABLE',
    createdAt: '2023-04-01T00:00:00'
  },
  {
    id: 5,
    bookNo: 'B005',
    title: '线性代数',
    author: '刘七',
    publisher: '高等教育出版社',
    publishDate: '2023-05-01',
    isbn: '9787040567890',
    category: '数学',
    price: 38.00,
    totalCopies: 18,
    availableCopies: 14,
    location: 'B区2层',
    description: '线性代数基础教程',
    status: 'AVAILABLE',
    createdAt: '2023-05-01T00:00:00'
  },
  {
    id: 6,
    bookNo: 'B006',
    title: '大学物理',
    author: '陈八',
    publisher: '科学出版社',
    publishDate: '2023-06-01',
    isbn: '9787030678901',
    category: '物理',
    price: 55.00,
    totalCopies: 16,
    availableCopies: 11,
    location: 'C区3层',
    description: '大学物理学教程',
    status: 'AVAILABLE',
    createdAt: '2023-06-01T00:00:00'
  },
  {
    id: 7,
    bookNo: 'B007',
    title: '有机化学',
    author: '杨九',
    publisher: '化学工业出版社',
    publishDate: '2023-07-01',
    isbn: '9787122789012',
    category: '化学',
    price: 62.00,
    totalCopies: 14,
    availableCopies: 10,
    location: 'D区4层',
    description: '有机化学基础与应用',
    status: 'AVAILABLE',
    createdAt: '2023-07-01T00:00:00'
  },
  {
    id: 8,
    bookNo: 'B008',
    title: '生物学概论',
    author: '黄十',
    publisher: '人民教育出版社',
    publishDate: '2023-08-01',
    isbn: '9787107890123',
    category: '生物',
    price: 48.00,
    totalCopies: 13,
    availableCopies: 9,
    location: 'E区5层',
    description: '生物学基础知识',
    status: 'AVAILABLE',
    createdAt: '2023-08-01T00:00:00'
  },
  {
    id: 9,
    bookNo: 'B009',
    title: '人工智能导论',
    author: '周十一',
    publisher: '清华大学出版社',
    publishDate: '2023-09-01',
    isbn: '9787302901234',
    category: '计算机',
    price: 78.00,
    totalCopies: 10,
    availableCopies: 6,
    location: 'A区1层',
    description: '人工智能基础理论与应用',
    status: 'AVAILABLE',
    createdAt: '2023-09-01T00:00:00'
  },
  {
    id: 10,
    bookNo: 'B010',
    title: '软件工程',
    author: '吴十二',
    publisher: '机械工业出版社',
    publishDate: '2023-10-01',
    isbn: '9787111012345',
    category: '计算机',
    price: 65.00,
    totalCopies: 12,
    availableCopies: 8,
    location: 'A区1层',
    description: '软件工程理论与实践',
    status: 'AVAILABLE',
    createdAt: '2023-10-01T00:00:00'
  }
]

// 生成借阅记录数据
export const borrowingRecords = []
for (let i = 0; i < 30; i++) {
  const student = students[i % students.length]
  const book = books[i % books.length]
  const borrowDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1)
  const dueDate = new Date(borrowDate.getTime() + 14 * 24 * 60 * 60 * 1000) // 14天后
  const isReturned = Math.random() > 0.3
  const returnDate = isReturned ? new Date(borrowDate.getTime() + Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1000) : null
  const isOverdue = !isReturned && new Date() > dueDate
  const renewCount = Math.floor(Math.random() * 3)

  let status = 'BORROWED'
  if (isReturned) {
    status = 'RETURNED'
  } else if (isOverdue) {
    status = 'OVERDUE'
  } else if (renewCount > 0) {
    status = 'RENEWED'
  }

  const overdueDays = isOverdue ? Math.floor((new Date() - dueDate) / (24 * 60 * 60 * 1000)) : 0
  const fine = overdueDays * 0.5 // 每天0.5元罚金

  borrowingRecords.push({
    id: i + 1,
    studentId: student.id,
    studentName: student.name,
    studentNo: student.studentNo,
    bookId: book.id,
    bookTitle: book.title,
    bookIsbn: book.isbn,
    borrowDate: borrowDate.toISOString().split('T')[0],
    dueDate: dueDate.toISOString().split('T')[0],
    returnDate: returnDate ? returnDate.toISOString().split('T')[0] : null,
    status: status,
    renewCount: renewCount,
    fine: fine,
    notes: i % 5 === 0 ? '学生申请续借' : '',
    createdAt: borrowDate.toISOString()
  })
}

// 生成教室数据
export const classrooms = [
  {
    id: 1,
    roomNo: 'A101',
    name: '多媒体教室A101',
    building: '教学楼A',
    floor: 1,
    type: '多媒体教室',
    capacity: 50,
    equipment: ['投影仪', '音响', '空调', '黑板'],
    status: 'AVAILABLE',
    description: '配备现代化多媒体设备的教室',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    roomNo: 'A102',
    name: '普通教室A102',
    building: '教学楼A',
    floor: 1,
    type: '普通教室',
    capacity: 45,
    equipment: ['黑板', '空调'],
    status: 'AVAILABLE',
    description: '标准配置的普通教室',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    roomNo: 'B201',
    name: '实验室B201',
    building: '教学楼B',
    floor: 2,
    type: '实验室',
    capacity: 30,
    equipment: ['实验台', '通风设备', '安全设备'],
    status: 'AVAILABLE',
    description: '化学实验专用教室',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    roomNo: 'C301',
    name: '计算机机房C301',
    building: '教学楼C',
    floor: 3,
    type: '机房',
    capacity: 40,
    equipment: ['计算机', '投影仪', '空调', '网络设备'],
    status: 'AVAILABLE',
    description: '计算机教学专用机房',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    roomNo: 'D401',
    name: '阶梯教室D401',
    building: '教学楼D',
    floor: 4,
    type: '阶梯教室',
    capacity: 120,
    equipment: ['投影仪', '音响', '空调', '话筒'],
    status: 'AVAILABLE',
    description: '大型阶梯教室，适合大班授课',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 生成宿舍数据
export const dormitories = [
  {
    id: 1,
    dormitoryNo: 'D1-101',
    building: '1号宿舍楼',
    floor: 1,
    roomNo: '101',
    type: '男生宿舍',
    capacity: 4,
    occupied: 3,
    fee: 800.0,
    facilities: ['空调', '热水器', '网络', '独立卫生间'],
    status: 'AVAILABLE',
    description: '标准四人间宿舍',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    dormitoryNo: 'D1-102',
    building: '1号宿舍楼',
    floor: 1,
    roomNo: '102',
    type: '男生宿舍',
    capacity: 4,
    occupied: 4,
    fee: 800.0,
    facilities: ['空调', '热水器', '网络', '独立卫生间'],
    status: 'FULL',
    description: '标准四人间宿舍',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    dormitoryNo: 'D2-201',
    building: '2号宿舍楼',
    floor: 2,
    roomNo: '201',
    type: '女生宿舍',
    capacity: 4,
    occupied: 2,
    fee: 800.0,
    facilities: ['空调', '热水器', '网络', '独立卫生间'],
    status: 'AVAILABLE',
    description: '标准四人间宿舍',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    dormitoryNo: 'D2-202',
    building: '2号宿舍楼',
    floor: 2,
    roomNo: '202',
    type: '女生宿舍',
    capacity: 4,
    occupied: 4,
    fee: 800.0,
    facilities: ['空调', '热水器', '网络', '独立卫生间'],
    status: 'FULL',
    description: '标准四人间宿舍',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    dormitoryNo: 'D3-301',
    building: '3号宿舍楼',
    floor: 3,
    roomNo: '301',
    type: '研究生宿舍',
    capacity: 2,
    occupied: 1,
    fee: 1200.0,
    facilities: ['空调', '热水器', '网络', '独立卫生间', '书桌'],
    status: 'AVAILABLE',
    description: '研究生专用双人间',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 生成体育场馆数据
export const sportsVenues = [
  {
    id: 1,
    name: '体育馆',
    type: '室内场馆',
    location: '体育中心',
    capacity: 2000,
    facilities: ['篮球场', '羽毛球场', '乒乓球台', '健身器材'],
    openTime: '06:00-22:00',
    fee: 10.0,
    status: 'AVAILABLE',
    description: '综合性室内体育场馆',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 2,
    name: '足球场',
    type: '室外场地',
    location: '运动场',
    capacity: 500,
    facilities: ['标准足球场', '看台', '更衣室'],
    openTime: '06:00-21:00',
    fee: 0.0,
    status: 'AVAILABLE',
    description: '标准11人制足球场',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 3,
    name: '游泳馆',
    type: '室内场馆',
    location: '体育中心',
    capacity: 200,
    facilities: ['标准泳池', '更衣室', '淋浴间'],
    openTime: '06:00-22:00',
    fee: 15.0,
    status: 'AVAILABLE',
    description: '50米标准游泳池',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 4,
    name: '网球场',
    type: '室外场地',
    location: '网球中心',
    capacity: 50,
    facilities: ['标准网球场', '更衣室'],
    openTime: '06:00-21:00',
    fee: 20.0,
    status: 'AVAILABLE',
    description: '标准网球场地',
    createdAt: '2020-01-01T00:00:00'
  },
  {
    id: 5,
    name: '田径场',
    type: '室外场地',
    location: '运动场',
    capacity: 1000,
    facilities: ['400米跑道', '跳远沙坑', '铅球场'],
    openTime: '06:00-21:00',
    fee: 0.0,
    status: 'AVAILABLE',
    description: '标准400米田径场',
    createdAt: '2020-01-01T00:00:00'
  }
]

// 导出工具函数
export const mockUtils = {
  generateId,
  generateDate,
  generatePhone,
  generateEmail,
  studentNames,
  teacherNames
}
