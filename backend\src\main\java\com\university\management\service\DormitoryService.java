package com.university.management.service;

import com.university.management.model.entity.Dormitory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 宿舍服务接口
 */
public interface DormitoryService {

    /**
     * 保存宿舍
     */
    Dormitory save(Dormitory dormitory);

    /**
     * 根据ID查找宿舍
     */
    Optional<Dormitory> findById(Integer id);

    /**
     * 获取所有宿舍
     */
    List<Dormitory> findAll();

    /**
     * 分页获取宿舍
     */
    Page<Dormitory> findAll(Pageable pageable);

    /**
     * 根据ID删除宿舍
     */
    void deleteById(Integer id);
} 