package com.university.management.service.impl;

import com.university.management.model.entity.Class;
import com.university.management.repository.ClassRepository;
import com.university.management.service.ClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 班级服务实现类
 */
@Service
@Transactional
public class ClassServiceImpl implements ClassService {

    private final ClassRepository classRepository;

    @Autowired
    public ClassServiceImpl(ClassRepository classRepository) {
        this.classRepository = classRepository;
    }

    @Override
    public List<Class> findAll() {
        return classRepository.findAll();
    }

    @Override
    public Page<Class> findAll(PageRequest pageRequest) {
        return classRepository.findAll(pageRequest);
    }

    @Override
    public Page<Class> findByConditions(String name, Integer majorId, Integer grade, PageRequest pageRequest) {
        if (name != null && majorId != null && grade != null) {
            return classRepository.findByNameContainingAndMajorIdAndGrade(name, majorId, grade, pageRequest);
        } else if (name != null && majorId != null) {
            return classRepository.findByNameContainingAndMajorId(name, majorId, pageRequest);
        } else if (name != null && grade != null) {
            return classRepository.findByNameContainingAndGrade(name, grade, pageRequest);
        } else if (majorId != null && grade != null) {
            return classRepository.findByMajorIdAndGrade(majorId, grade, pageRequest);
        } else if (name != null) {
            return classRepository.findByNameContaining(name, pageRequest);
        } else if (majorId != null) {
            return classRepository.findByMajorId(majorId, pageRequest);
        } else if (grade != null) {
            return classRepository.findByGrade(grade, pageRequest);
        } else {
            return classRepository.findAll(pageRequest);
        }
    }

    @Override
    public Optional<Class> findById(Integer id) {
        return classRepository.findById(id);
    }

    @Override
    public Class save(Class clazz) {
        return classRepository.save(clazz);
    }

    @Override
    public void deleteById(Integer id) {
        classRepository.deleteById(id);
    }

    @Override
    public List<Class> findByMajorId(Integer majorId) {
        return classRepository.findByMajorId(majorId);
    }

    @Override
    public List<Class> findByGrade(Integer grade) {
        return classRepository.findByGrade(grade);
    }

    @Override
    public Map<String, Object> getClassStats() {
        List<Class> allClasses = classRepository.findAll();
        Map<String, Object> stats = new HashMap<>();

        // 年级分布统计
        Map<String, Long> gradeStats = allClasses.stream()
            .collect(Collectors.groupingBy(
                clazz -> clazz.getGrade() != null ? clazz.getGrade().toString() : "未知",
                Collectors.counting()
            ));

        // 各专业班级数量统计
        Map<String, Long> majorStats = allClasses.stream()
            .collect(Collectors.groupingBy(
                clazz -> clazz.getMajor() != null ? clazz.getMajor().getName() : "未知专业",
                Collectors.counting()
            ));

        stats.put("gradeStats", gradeStats.entrySet().stream()
            .map(entry -> Map.of("name", entry.getKey(), "value", entry.getValue()))
            .collect(Collectors.toList()));

        stats.put("majorStats", majorStats.entrySet().stream()
            .map(entry -> Map.of("name", entry.getKey(), "value", entry.getValue()))
            .collect(Collectors.toList()));

        return stats;
    }
}
