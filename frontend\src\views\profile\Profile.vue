<template>
  <div class="profile-container">
    <div class="header">
      <h2><el-icon><User /></el-icon> 个人信息</h2>
    </div>

    <el-card class="profile-card">
      <div class="profile-content">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <el-avatar :size="120" :src="userInfo.avatar" class="user-avatar">
            {{ userInfo.realName?.charAt(0) || userInfo.username?.charAt(0) }}
          </el-avatar>
          <el-button type="primary" size="small" @click="handleAvatarUpload" class="upload-btn">
            <el-icon><Upload /></el-icon>
            更换头像
          </el-button>
        </div>

        <!-- 信息区域 -->
        <div class="info-section">
          <el-form
            ref="formRef"
            :model="userInfo"
            :rules="rules"
            label-width="100px"
            class="profile-form"
            v-loading="loading"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="userInfo.username" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="userInfo.realName" :disabled="!isEditing"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="userInfo.email" :disabled="!isEditing"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="userInfo.phone" :disabled="!isEditing"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-select v-model="userInfo.gender" :disabled="!isEditing" style="width: 100%">
                    <el-option label="男" value="male"></el-option>
                    <el-option label="女" value="female"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="生日" prop="birthday">
                  <el-date-picker
                    v-model="userInfo.birthday"
                    type="date"
                    placeholder="选择日期"
                    :disabled="!isEditing"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="个人简介" prop="bio">
              <el-input
                v-model="userInfo.bio"
                type="textarea"
                :rows="4"
                placeholder="请输入个人简介"
                :disabled="!isEditing"
              ></el-input>
            </el-form-item>

            <el-form-item label="角色">
              <el-tag v-for="role in userInfo.roles" :key="role.id" type="primary" class="role-tag">
                {{ role.name }}
              </el-tag>
            </el-form-item>

            <el-form-item>
              <el-button v-if="!isEditing" type="primary" @click="startEdit">
                <el-icon><Edit /></el-icon>
                编辑信息
              </el-button>
              <template v-else>
                <el-button type="primary" @click="saveProfile">
                  <el-icon><Check /></el-icon>
                  保存
                </el-button>
                <el-button @click="cancelEdit">
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Upload, Edit, Check, Close } from '@element-plus/icons-vue'
import { getCurrentUser, updateUser } from '@/api/user'

export default {
  name: 'Profile',
  components: {
    User,
    Upload,
    Edit,
    Check,
    Close
  },
  setup() {
    const formRef = ref(null)
    const loading = ref(false)
    const isEditing = ref(false)
    const originalUserInfo = ref({})

    // 用户信息
    const userInfo = reactive({
      id: null,
      username: '',
      realName: '',
      email: '',
      phone: '',
      gender: '',
      birthday: '',
      bio: '',
      avatar: '',
      roles: []
    })

    // 表单验证规则
    const rules = reactive({
      realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    })

    // 获取当前用户信息
    const fetchUserInfo = async () => {
      loading.value = true
      try {
        const res = await getCurrentUser()
        if (res.data) {
          Object.assign(userInfo, res.data)
          originalUserInfo.value = { ...res.data }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        ElMessage.error('获取用户信息失败')
      } finally {
        loading.value = false
      }
    }

    // 开始编辑
    const startEdit = () => {
      isEditing.value = true
      originalUserInfo.value = { ...userInfo }
    }

    // 取消编辑
    const cancelEdit = () => {
      isEditing.value = false
      Object.assign(userInfo, originalUserInfo.value)
    }

    // 保存个人信息
    const saveProfile = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            await updateUser(userInfo.id, userInfo)
            ElMessage.success('个人信息更新成功')
            isEditing.value = false
            originalUserInfo.value = { ...userInfo }
          } catch (error) {
            console.error('更新个人信息失败:', error)
            ElMessage.error('更新个人信息失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 头像上传
    const handleAvatarUpload = () => {
      ElMessage.info('头像上传功能待实现')
    }

    // 组件挂载时获取用户信息
    onMounted(() => {
      fetchUserInfo()
    })

    return {
      formRef,
      loading,
      isEditing,
      userInfo,
      rules,
      startEdit,
      cancelEdit,
      saveProfile,
      handleAvatarUpload
    }
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.profile-card {
  max-width: 1000px;
}

.profile-content {
  display: flex;
  gap: 40px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  min-width: 160px;
}

.user-avatar {
  font-size: 48px;
  font-weight: bold;
}

.upload-btn {
  width: 100px;
}

.info-section {
  flex: 1;
}

.profile-form {
  max-width: 800px;
}

.role-tag {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .avatar-section {
    align-self: center;
  }
}
</style>
