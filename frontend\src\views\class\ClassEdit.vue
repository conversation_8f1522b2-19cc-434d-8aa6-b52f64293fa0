<template>
  <div class="class-edit-container">
    <h2><el-icon><UserFilled /></el-icon> {{ isEdit ? '编辑班级' : '添加班级' }}</h2>
    
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="class-form">
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="班级名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入班级名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年级" prop="grade">
            <el-select v-model="form.grade" placeholder="请选择年级" style="width: 100%">
              <el-option label="2021级" value="2021"></el-option>
              <el-option label="2022级" value="2022"></el-option>
              <el-option label="2023级" value="2023"></el-option>
              <el-option label="2024级" value="2024"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属专业" prop="majorId">
            <el-select v-model="form.majorId" placeholder="请选择专业" style="width: 100%">
              <el-option 
                v-for="major in majorOptions" 
                :key="major.id" 
                :label="major.name" 
                :value="major.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班主任" prop="advisorId">
            <el-select v-model="form.advisorId" placeholder="请选择班主任" style="width: 100%">
              <el-option 
                v-for="teacher in teacherOptions" 
                :key="teacher.id" 
                :label="teacher.name" 
                :value="teacher.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大人数" prop="maxStudents">
            <el-input-number v-model="form.maxStudents" :min="1" :max="100" placeholder="最大人数" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="教室" prop="classroom">
            <el-input v-model="form.classroom" placeholder="请输入教室" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="ACTIVE">正常</el-radio>
              <el-radio label="INACTIVE">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="班级介绍" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入班级介绍"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
        <el-button @click="handleCancel">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UserFilled, Check, Close } from '@element-plus/icons-vue'
import { getClassById, createClass, updateClass } from '@/api/class'
import { getAllMajors } from '@/api/major'
import { getAllTeachers } from '@/api/teacher'

export default {
  name: 'ClassEdit',
  components: {
    UserFilled, Check, Close
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref()
    const loading = ref(false)
    const majorOptions = ref([])
    const teacherOptions = ref([])

    const isEdit = computed(() => !!route.params.id)

    // 表单数据
    const form = reactive({
      name: '',
      grade: '',
      majorId: null,
      advisorId: null,
      maxStudents: 30,
      classroom: '',
      status: 'ACTIVE',
      description: ''
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入班级名称', trigger: 'blur' },
        { min: 2, max: 50, message: '班级名称长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      grade: [
        { required: true, message: '请选择年级', trigger: 'change' }
      ],
      majorId: [
        { required: true, message: '请选择所属专业', trigger: 'change' }
      ],
      advisorId: [
        { required: true, message: '请选择班主任', trigger: 'change' }
      ],
      maxStudents: [
        { required: true, message: '请输入最大人数', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    // 获取专业选项
    const fetchMajorOptions = async () => {
      try {
        const res = await getAllMajors()
        if (res) {
          majorOptions.value = res
        }
      } catch (error) {
        console.error('获取专业数据失败:', error)
      }
    }

    // 获取教师选项
    const fetchTeacherOptions = async () => {
      try {
        const res = await getAllTeachers()
        if (res) {
          teacherOptions.value = res
        }
      } catch (error) {
        console.error('获取教师数据失败:', error)
      }
    }

    // 获取班级详情
    const fetchClassDetail = async () => {
      if (!isEdit.value) return
      
      try {
        const res = await getClassById(route.params.id)
        if (res) {
          Object.assign(form, res)
        }
      } catch (error) {
        console.error('获取班级详情失败:', error)
        ElMessage.error('获取班级详情失败')
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        loading.value = true

        if (isEdit.value) {
          await updateClass(route.params.id, form)
          ElMessage.success('更新成功')
        } else {
          await createClass(form)
          ElMessage.success('创建成功')
        }

        router.push('/classes')
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          console.error('保存班级失败:', error)
          ElMessage.error('保存失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 取消操作
    const handleCancel = () => {
      router.push('/classes')
    }

    onMounted(() => {
      fetchMajorOptions()
      fetchTeacherOptions()
      fetchClassDetail()
    })

    return {
      formRef,
      loading,
      majorOptions,
      teacherOptions,
      isEdit,
      form,
      rules,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.class-edit-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
}

.class-edit-container h2 {
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.class-form {
  max-width: 800px;
}
</style>
