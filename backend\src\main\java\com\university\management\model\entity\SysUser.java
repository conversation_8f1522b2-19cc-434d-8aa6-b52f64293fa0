package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统用户实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SysUser", description = "系统用户信息")
@Entity
@TableName("sys_user")
public class SysUser extends BaseEntity {

    @ApiModelProperty(value = "用户名")
    @Column(unique = true, nullable = false, length = 50)
    private String username;

    @ApiModelProperty(value = "密码")
    @Column(nullable = false, length = 100)
    private String password;

    @ApiModelProperty(value = "真实姓名")
    @Column(length = 50)
    private String realName;

    @ApiModelProperty(value = "用户类型(0-管理员，1-学生，2-教师)")
    private Integer userType;

    @ApiModelProperty(value = "关联ID")
    private Integer relatedId;

    @ApiModelProperty(value = "手机号")
    @Column(length = 11)
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @Column(length = 100)
    private String email;

    @ApiModelProperty(value = "头像")
    @Column(length = 200)
    private String avatar;

    @ApiModelProperty(value = "状态(0-正常，1-禁用)")
    private Integer status;

    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_user_role",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    @TableField(exist = false)
    private List<SysRole> roles = new ArrayList<>();
} 