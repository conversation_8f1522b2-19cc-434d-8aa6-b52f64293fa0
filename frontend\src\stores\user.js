import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: {},
    roles: [],
    permissions: []
  }),

  getters: {
    isLoggedIn: (state) => !!state.token
  },

  actions: {
    // 登录
    async loginAction(loginForm) {
      try {
        const response = await login(loginForm)
        const { token } = response
        this.token = token
        localStorage.setItem('token', token)
        return response
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    },

    // 获取用户信息
    async getUserInfoAction() {
      try {
        const response = await getUserInfo()
        const userInfo = response
        const roles = userInfo.roles || []
        const permissions = userInfo.permissions || []
        this.userInfo = userInfo
        this.roles = roles
        this.permissions = permissions
        return { userInfo, roles, permissions }
      } catch (error) {
        console.error('Get user info failed:', error)
        throw error
      }
    },

    // 登出
    async logoutAction() {
      try {
        await logout()
        this.resetUserState()
        return true
      } catch (error) {
        console.error('Logout failed:', error)
        throw error
      }
    },

    // 重置用户状态
    resetUserState() {
      this.token = ''
      this.userInfo = {}
      this.roles = []
      this.permissions = []
      localStorage.removeItem('token')
    }
  }
}) 