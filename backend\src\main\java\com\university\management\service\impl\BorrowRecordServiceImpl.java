package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.Book;
import com.university.management.model.entity.BorrowRecord;
import com.university.management.repository.BookRepository;
import com.university.management.repository.BorrowRecordRepository;
import com.university.management.service.BorrowRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 借阅记录服务实现类
 */
@Service
@Transactional
public class BorrowRecordServiceImpl implements BorrowRecordService {

    private final BorrowRecordRepository borrowRecordRepository;
    private final BookRepository bookRepository;

    @Autowired
    public BorrowRecordServiceImpl(BorrowRecordRepository borrowRecordRepository, BookRepository bookRepository) {
        this.borrowRecordRepository = borrowRecordRepository;
        this.bookRepository = bookRepository;
    }

    @Override
    public BorrowRecord createBorrowRecord(BorrowRecord borrowRecord) {
        // 设置默认值
        if (borrowRecord.getBorrowDate() == null) {
            borrowRecord.setBorrowDate(LocalDate.now());
        }
        if (borrowRecord.getExpectedReturnDate() == null) {
            // 默认借阅期限为15天
            borrowRecord.setExpectedReturnDate(borrowRecord.getBorrowDate().plusDays(15));
        }
        if (borrowRecord.getStatus() == null) {
            // 默认状态为借阅中
            borrowRecord.setStatus(0);
        }
        
        // 更新图书可用数量
        Optional<Book> bookOpt = bookRepository.findById(borrowRecord.getBookId());
        if (bookOpt.isPresent()) {
            Book book = bookOpt.get();
            if (book.getAvailable() > 0) {
                book.setAvailable(book.getAvailable() - 1);
                bookRepository.save(book);
            } else {
                throw new IllegalStateException("图书已无可借阅库存");
            }
        }
        
        return borrowRecordRepository.save(borrowRecord);
    }

    @Override
    public BorrowRecord updateBorrowRecord(Integer id, BorrowRecord borrowRecordDetails) {
        BorrowRecord borrowRecord = borrowRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BorrowRecord not found with id: " + id));

        borrowRecord.setStudentId(borrowRecordDetails.getStudentId());
        borrowRecord.setBookId(borrowRecordDetails.getBookId());
        borrowRecord.setBorrowDate(borrowRecordDetails.getBorrowDate());
        borrowRecord.setExpectedReturnDate(borrowRecordDetails.getExpectedReturnDate());
        borrowRecord.setActualReturnDate(borrowRecordDetails.getActualReturnDate());
        borrowRecord.setFine(borrowRecordDetails.getFine());
        borrowRecord.setStatus(borrowRecordDetails.getStatus());
        
        return borrowRecordRepository.save(borrowRecord);
    }

    @Override
    public void deleteBorrowRecord(Integer id) {
        BorrowRecord borrowRecord = borrowRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BorrowRecord not found with id: " + id));
        borrowRecordRepository.delete(borrowRecord);
    }

    @Override
    public Optional<BorrowRecord> findById(Integer id) {
        return borrowRecordRepository.findById(id);
    }

    @Override
    public List<BorrowRecord> findAll() {
        return borrowRecordRepository.findAll();
    }

    @Override
    public Page<BorrowRecord> findAll(Pageable pageable) {
        return borrowRecordRepository.findAll(pageable);
    }

    @Override
    public Page<BorrowRecord> findByConditions(Integer studentId, Integer bookId, Integer status, Pageable pageable) {
        return borrowRecordRepository.findByConditions(studentId, bookId, status, pageable);
    }

    @Override
    public BorrowRecord returnBook(Integer id) {
        BorrowRecord borrowRecord = borrowRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BorrowRecord not found with id: " + id));
        
        // 只有借阅中的记录才能归还
        if (borrowRecord.getStatus() != 0) {
            throw new IllegalStateException("该借阅记录已归还或已取消");
        }
        
        // 设置归还日期和状态
        borrowRecord.setActualReturnDate(LocalDate.now());
        borrowRecord.setStatus(1); // 已归还
        
        // 计算罚款（如果逾期）
        if (borrowRecord.getActualReturnDate().isAfter(borrowRecord.getExpectedReturnDate())) {
            long overdueDays = borrowRecord.getActualReturnDate().toEpochDay() - 
                    borrowRecord.getExpectedReturnDate().toEpochDay();
            // 假设每天罚款1元
            borrowRecord.setFine(java.math.BigDecimal.valueOf(overdueDays));
        }
        
        // 更新图书可用数量
        Optional<Book> bookOpt = bookRepository.findById(borrowRecord.getBookId());
        if (bookOpt.isPresent()) {
            Book book = bookOpt.get();
            book.setAvailable(book.getAvailable() + 1);
            bookRepository.save(book);
        }

        return borrowRecordRepository.save(borrowRecord);
    }

    @Override
    public Map<String, Object> getBorrowingStats() {
        List<BorrowRecord> allRecords = borrowRecordRepository.findAll();
        Map<String, Object> stats = new HashMap<>();

        // 借阅状态分布统计
        Map<String, Long> statusStats = allRecords.stream()
            .collect(Collectors.groupingBy(
                record -> {
                    if (record.getReturnDate() != null) {
                        return "已归还";
                    } else if (record.getDueDate() != null && record.getDueDate().isBefore(LocalDate.now())) {
                        return "逾期";
                    } else {
                        return "借阅中";
                    }
                },
                Collectors.counting()
            ));

        // 借阅趋势统计（最近7天）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        Map<String, Map<String, Long>> trendData = new HashMap<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            String dateStr = date.format(formatter);

            long borrowCount = allRecords.stream()
                .filter(record -> record.getBorrowDate() != null && record.getBorrowDate().equals(date))
                .count();

            long returnCount = allRecords.stream()
                .filter(record -> record.getReturnDate() != null && record.getReturnDate().equals(date))
                .count();

            Map<String, Long> dayData = new HashMap<>();
            dayData.put("borrowCount", borrowCount);
            dayData.put("returnCount", returnCount);
            trendData.put(dateStr, dayData);
        }

        stats.put("statusStats", statusStats.entrySet().stream()
            .map(entry -> Map.of("name", entry.getKey(), "value", entry.getValue()))
            .collect(Collectors.toList()));

        stats.put("trendStats", trendData.entrySet().stream()
            .map(entry -> Map.of(
                "date", entry.getKey(),
                "borrowCount", entry.getValue().get("borrowCount"),
                "returnCount", entry.getValue().get("returnCount")
            ))
            .collect(Collectors.toList()));

        return stats;
    }
}