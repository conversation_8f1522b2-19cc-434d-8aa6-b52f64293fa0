-- 简化的图表数据插入脚本
USE university_management;

-- 插入基本学生数据
INSERT IGNORE INTO student (student_no, name, gender, birthday, age, phone, email, major_id, class_id, department_id, enroll_year, status)
VALUES
-- 计算机科学与技术专业 (major_id=1)
('S001', '张小明', 1, '2002-03-15', 22, '13800001001', '<EMAIL>', 1, 1, 1, 2020, 0),
('S002', '李小红', 0, '2002-05-20', 22, '13800001002', '<EMAIL>', 1, 1, 1, 2020, 0),
('S003', '王小刚', 1, '2002-07-10', 22, '13800001003', '<EMAIL>', 1, 1, 1, 2021, 0),
('S004', '赵小丽', 0, '2002-09-25', 22, '13800001004', '<EMAIL>', 1, 1, 1, 2021, 0),
('S005', '刘小强', 1, '2002-11-30', 22, '13800001005', '<EMAIL>', 1, 1, 1, 2022, 0),

-- 软件工程专业 (major_id=2)
('S006', '陈小华', 1, '2002-01-12', 22, '13800001006', '<EMAIL>', 2, 2, 1, 2020, 0),
('S007', '周小芳', 0, '2002-04-18', 22, '13800001007', '<EMAIL>', 2, 2, 1, 2021, 0),
('S008', '吴小军', 1, '2002-06-22', 22, '13800001008', '<EMAIL>', 2, 2, 1, 2021, 0),
('S009', '郑小燕', 0, '2002-08-14', 22, '13800001009', '<EMAIL>', 2, 2, 1, 2022, 0),

-- 网络工程专业 (major_id=3)
('S010', '孙小龙', 1, '2002-02-28', 22, '13800001010', '<EMAIL>', 3, 3, 1, 2020, 0),
('S011', '马小梅', 0, '2002-10-05', 22, '13800001011', '<EMAIL>', 3, 3, 1, 2021, 0),

-- 电子信息工程专业 (major_id=4)
('S012', '田小勇', 1, '2003-02-14', 21, '13800002005', '<EMAIL>', 4, 6, 2, 2021, 0),
('S013', '邓小玲', 0, '2003-04-18', 21, '13800002006', '<EMAIL>', 4, 6, 2, 2022, 0),

-- 通信工程专业 (major_id=5)
('S014', '韩小斌', 1, '2003-06-22', 21, '13800002007', '<EMAIL>', 5, 7, 2, 2021, 0),
('S015', '曹小敏', 0, '2003-08-30', 21, '13800002008', '<EMAIL>', 5, 7, 2, 2022, 0);

-- 插入基本教师数据
INSERT IGNORE INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, department_id, hire_date, status)
VALUES
('T001', '张教授', 1, '1970-01-15', 54, 3, '13900001111', '<EMAIL>', 1, '1995-07-01', 0),
('T002', '李副教授', 0, '1975-03-20', 49, 2, '13900002222', '<EMAIL>', 1, '2000-07-01', 0),
('T003', '王讲师', 1, '1980-05-10', 44, 1, '13900003333', '<EMAIL>', 1, '2005-07-01', 0),
('T004', '赵助教', 0, '1985-07-25', 39, 0, '13900004444', '<EMAIL>', 1, '2010-07-01', 0),
('T005', '陈教授', 1, '1968-09-30', 56, 3, '13900005555', '<EMAIL>', 2, '1992-07-01', 0),
('T006', '刘副教授', 0, '1973-11-12', 51, 2, '13900006666', '<EMAIL>', 2, '1998-07-01', 0),
('T007', '吴讲师', 1, '1978-02-14', 46, 1, '13900007777', '<EMAIL>', 3, '2003-07-01', 0),
('T008', '郑助教', 0, '1983-04-18', 41, 0, '13900008888', '<EMAIL>', 3, '2008-07-01', 0),
('T009', '孙教授', 1, '1965-06-22', 59, 3, '13900009999', '<EMAIL>', 4, '1990-07-01', 0),
('T010', '马副教授', 0, '1972-08-30', 52, 2, '13900010000', '<EMAIL>', 5, '1997-07-01', 0);

-- 插入基本课程数据
INSERT IGNORE INTO course (course_no, name, credit, hours, type, department_id, description, status)
VALUES
('C001', '高等数学', 4.0, 64, 0, 1, '高等数学基础课程', 0),
('C002', '线性代数', 3.0, 48, 0, 1, '线性代数基础课程', 0),
('C003', '概率论', 3.0, 48, 0, 1, '概率论与数理统计课程', 0),
('C004', '数据结构', 4.0, 64, 0, 1, '数据结构与算法', 0),
('C005', '计算机网络', 3.0, 48, 0, 1, '计算机网络原理', 0),
('C006', '操作系统', 4.0, 64, 0, 1, '操作系统原理与实践', 0),
('C007', '数据库系统', 3.0, 48, 0, 1, '数据库系统原理', 0),
('C008', '软件工程', 3.0, 48, 0, 1, '软件工程方法与实践', 0),
('C009', '人工智能', 2.0, 32, 1, 1, '人工智能基础', 0),
('C010', '机器学习', 2.0, 32, 1, 1, '机器学习算法与应用', 0);
