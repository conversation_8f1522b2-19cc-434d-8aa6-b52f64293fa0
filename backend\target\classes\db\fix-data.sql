-- 数据修复脚本
-- 修复图书表中的空字段

-- 更新图书编号和名称
UPDATE book SET book_no = 'B001', name = 'Java编程思想' WHERE id = 1;
UPDATE book SET book_no = 'B002', name = 'C++程序设计' WHERE id = 2;
UPDATE book SET book_no = 'B003', name = 'Python编程：从入门到实践' WHERE id = 3;
UPDATE book SET book_no = 'B004', name = '算法导论' WHERE id = 4;
UPDATE book SET book_no = 'B005', name = '数据结构与算法分析' WHERE id = 5;
UPDATE book SET book_no = 'B006', name = '深入理解计算机系统' WHERE id = 6;
UPDATE book SET book_no = 'B007', name = '操作系统概念' WHERE id = 7;
UPDATE book SET book_no = 'B008', name = '数据库系统概念' WHERE id = 8;
UPDATE book SET book_no = 'B009', name = '大学物理学' WHERE id = 9;
UPDATE book SET book_no = 'B010', name = '高等数学' WHERE id = 10;

-- 更新借阅记录编号（生成格式：BR + 年月日 + 序号）
UPDATE borrow_record SET borrow_no = CONCAT('BR', DATE_FORMAT(borrow_date, '%Y%m%d'), LPAD(id, 3, '0')) WHERE borrow_no = '' OR borrow_no IS NULL;

-- 更新体育场馆编号
UPDATE sports_venue SET venue_no = 'SV001' WHERE id = 1;
