-- 插入专业数据
USE university_management;

-- 插入专业数据（使用正确的department_id）
INSERT INTO major (major_no, name, department_id, description, length, degree) VALUES
-- Computer Science College majors (department_id = 529)
('CS0101', 'Computer Science and Technology', 529, 'Training senior professionals in computer science and technology', '4 years', 'B.Eng'),
('SE0101', 'Software Engineering', 529, 'Training professionals in software development and engineering management', '4 years', 'B.Eng'),
('NE0101', 'Network Engineering', 529, 'Training professionals in network system design and management', '4 years', 'B.Eng'),
('AI0101', 'Artificial Intelligence', 529, 'Training professionals in AI technology applications', '4 years', 'B.Eng'),
('DS0101', 'Data Science and Big Data Technology', 529, 'Training professionals in big data analysis and processing', '4 years', 'B.Eng'),

-- Electronic Engineering College majors (department_id = 530)
('EE0101', 'Electronic Information Engineering', 530, 'Training professionals in electronic information system design and applications', '4 years', 'B.Eng'),
('TC0101', 'Communication Engineering', 530, 'Training professionals in communication systems and network technology', '4 years', 'B.Eng'),
('AU0101', 'Automation', 530, 'Training professionals in automatic control system design', '4 years', 'B.Eng'),

-- Mechanical Engineering College majors (department_id = 531)
('ME0101', 'Mechanical Design Manufacturing and Automation', 531, 'Training professionals in mechanical design and manufacturing', '4 years', 'B.Eng'),
('ME0201', 'Mechatronics Engineering', 531, 'Training professionals in mechatronics technology', '4 years', 'B.Eng'),
('VE0101', 'Vehicle Engineering', 531, 'Training professionals in automotive design and manufacturing', '4 years', 'B.Eng'),

-- Economics and Management College majors (department_id = 532)
('BM0101', 'Business Administration', 532, 'Training professionals in business management', '4 years', 'B.Mgmt'),
('AC0101', 'Accounting', 532, 'Training professionals in financial accounting', '4 years', 'B.Mgmt'),
('MK0101', 'Marketing', 532, 'Training professionals in marketing', '4 years', 'B.Mgmt'),
('IT0101', 'International Economics and Trade', 532, 'Training professionals in international trade', '4 years', 'B.Econ'),

-- Foreign Languages College majors (department_id = 533)
('EN0101', 'English', 533, 'Training professionals in English language and literature', '4 years', 'B.A'),
('JP0101', 'Japanese', 533, 'Training professionals in Japanese language and literature', '4 years', 'B.A'),

-- Art and Design College majors (department_id = 534)
('VD0101', 'Visual Communication Design', 534, 'Training professionals in visual design', '4 years', 'B.A'),
('ED0101', 'Environmental Design', 534, 'Training professionals in environmental art design', '4 years', 'B.A');
