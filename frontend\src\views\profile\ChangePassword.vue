<template>
  <div class="change-password-container">
    <div class="header">
      <h2><el-icon><Key /></el-icon> 修改密码</h2>
    </div>

    <el-card class="password-card">
      <el-form
        ref="formRef"
        :model="passwordForm"
        :rules="rules"
        label-width="120px"
        class="password-form"
        v-loading="loading"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            clearable
          ></el-input>
          <div class="password-tips">
            <p>密码要求：</p>
            <ul>
              <li>长度至少8位</li>
              <li>包含大写字母</li>
              <li>包含小写字母</li>
              <li>包含数字</li>
            </ul>
          </div>
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            <el-icon><Check /></el-icon>
            确认修改
          </el-button>
          <el-button @click="resetForm">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 安全提示 -->
    <el-card class="security-tips">
      <template #header>
        <div class="card-header">
          <el-icon><InfoFilled /></el-icon>
          <span>安全提示</span>
        </div>
      </template>
      <div class="tips-content">
        <el-alert
          title="为了您的账户安全，请注意以下事项："
          type="info"
          :closable="false"
          show-icon
        >
          <ul class="tips-list">
            <li>定期更换密码，建议每3-6个月更换一次</li>
            <li>不要使用过于简单的密码，如生日、姓名等</li>
            <li>不要在多个网站使用相同的密码</li>
            <li>不要将密码告诉他人或写在容易被发现的地方</li>
            <li>如发现账户异常，请立即修改密码并联系管理员</li>
          </ul>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Key, Check, Refresh, ArrowLeft, InfoFilled } from '@element-plus/icons-vue'
import { changePassword } from '@/api/user'

export default {
  name: 'ChangePassword',
  components: {
    Key,
    Check,
    Refresh,
    ArrowLeft,
    InfoFilled
  },
  setup() {
    const router = useRouter()
    const formRef = ref(null)
    const loading = ref(false)

    // 密码表单
    const passwordForm = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 自定义验证函数
    const validateNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入新密码'))
      } else if (value === passwordForm.oldPassword) {
        callback(new Error('新密码不能与当前密码相同'))
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/.test(value)) {
        callback(new Error('密码必须包含大小写字母和数字，且不少于8位'))
      } else {
        callback()
      }
    }

    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请确认新密码'))
      } else if (value !== passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    // 表单验证规则
    const rules = reactive({
      oldPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { validator: validateNewPassword, trigger: 'blur' }
      ],
      confirmPassword: [
        { validator: validateConfirmPassword, trigger: 'blur' }
      ]
    })

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            await changePassword({
              oldPassword: passwordForm.oldPassword,
              newPassword: passwordForm.newPassword
            })
            ElMessage.success('密码修改成功，请重新登录')
            
            // 清除本地存储的token，强制用户重新登录
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            
            // 跳转到登录页
            setTimeout(() => {
              router.push('/login')
            }, 1500)
          } catch (error) {
            console.error('修改密码失败:', error)
            ElMessage.error(error.message || '修改密码失败')
          } finally {
            loading.value = false
          }
        } else {
          ElMessage.warning('请填写必要的表单项')
          return false
        }
      })
    }

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
      Object.assign(passwordForm, {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    return {
      formRef,
      loading,
      passwordForm,
      rules,
      submitForm,
      resetForm,
      goBack
    }
  }
}
</script>

<style scoped>
.change-password-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.password-card {
  margin-bottom: 20px;
}

.password-form {
  max-width: 500px;
}

.password-tips {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.password-tips p {
  margin: 0 0 5px 0;
  font-weight: bold;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.password-tips li {
  margin-bottom: 2px;
}

.security-tips {
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tips-content {
  margin-top: 10px;
}

.tips-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
