package com.university.management.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;

/**
 * API响应结果
 */
@ApiModel(value = "API响应结果")
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    @ApiModelProperty("是否成功")
    @JsonProperty("success")
    private Boolean success;

    /**
     * 响应消息
     */
    @ApiModelProperty("响应消息")
    @JsonProperty("message")
    private String message;

    /**
     * 响应数据
     */
    @ApiModelProperty("响应数据")
    @JsonProperty("data")
    private T data;

    /**
     * 无参构造器
     */
    public ApiResponse() {
    }

    /**
     * 全参构造器
     */
    public ApiResponse(Boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    // Getter和Setter方法
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    /**
     * 成功响应（无参数）
     *
     * @return API响应
     */
    public static ApiResponse<Void> success() {
        return new ApiResponse<Void>(true, encodeMessage("操作成功"), null);
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<T>(true, encodeMessage("操作成功"), data);
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<T>(true, encodeMessage(message), data);
    }

    /**
     * 失败响应
     *
     * @param message 响应消息
     * @return API响应
     */
    public static ApiResponse<Void> failure(String message) {
        return new ApiResponse<Void>(false, encodeMessage(message), null);
    }

    /**
     * 失败响应
     *
     * @param message 响应消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> failure(String message, T data) {
        return new ApiResponse<T>(false, encodeMessage(message), data);
    }

    /**
     * 错误响应
     *
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> error(String message) {
        return new ApiResponse<Void>(false, encodeMessage(message), null);
    }

    /**
     * 错误响应
     *
     * @param code    错误代码
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> error(int code, String message) {
        return new ApiResponse<Void>(false, encodeMessage(message), null);
    }

    /**
     * 错误响应（泛型版本）
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return API响应
     */
    @SuppressWarnings("unchecked")
    public static <T> ApiResponse<T> errorGeneric(String message) {
        return (ApiResponse<T>) new ApiResponse<Void>(false, encodeMessage(message), null);
    }

    /**
     * 错误响应（泛型版本）
     *
     * @param code    错误代码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return API响应
     */
    @SuppressWarnings("unchecked")
    public static <T> ApiResponse<T> errorGeneric(int code, String message) {
        return (ApiResponse<T>) new ApiResponse<Void>(false, encodeMessage(message), null);
    }
    
    /**
     * 确保消息使用UTF-8编码
     * 
     * @param message 原始消息
     * @return UTF-8编码的消息
     */
    private static String encodeMessage(String message) {
        if (message == null) {
            return null;
        }
        return new String(message.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
    }
} 