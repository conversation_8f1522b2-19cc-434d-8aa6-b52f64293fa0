<template>
  <div class="teacher-list-container">
    <div class="header">
      <h2>教师管理</h2>
      <el-button type="primary" @click="handleAdd">添加教师</el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="部门">
          <el-input v-model="searchForm.department" placeholder="请输入部门" clearable></el-input>
        </el-form-item>
        <el-form-item label="职称">
          <el-select v-model="searchForm.title" placeholder="请选择职称" clearable>
            <el-option label="教授" value="教授"></el-option>
            <el-option label="副教授" value="副教授"></el-option>
            <el-option label="讲师" value="讲师"></el-option>
            <el-option label="助教" value="助教"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table 
      v-loading="loading" 
      :data="teacherList" 
      border 
      style="width: 100%" 
      row-key="id"
      @sort-change="handleSortChange">
      <el-table-column prop="employeeNumber" label="工号" width="120" sortable></el-table-column>
      <el-table-column prop="name" label="姓名" width="120"></el-table-column>
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="scope">
          {{ scope.row.gender === 'MALE' ? '男' : '女' }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="所属部门" width="150"></el-table-column>
      <el-table-column prop="title" label="职称" width="100"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
      <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
      <el-table-column prop="hireDate" label="入职日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.hireDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="researchArea" label="研究领域" width="150"></el-table-column>
      <el-table-column label="操作" fixed="right" width="180">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 数据可视化 -->
    <div class="data-visualization">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container">
            <h3>教师部门分布</h3>
            <div id="departmentChart" style="height: 300px;"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container">
            <h3>教师职称分布</h3>
            <div id="titleChart" style="height: 300px;"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { getTeachersByPage, searchTeachers, deleteTeacher, getTeacherStatsByDepartment, getTeacherStatsByTitle } from '@/api/teacher'

export default {
  name: 'TeacherList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const teacherList = ref([])
    const departmentChart = ref(null)
    const titleChart = ref(null)

    // 分页设置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortBy: 'id'
    })

    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      title: ''
    })

    // 获取教师列表数据
    const fetchTeacherData = async () => {
      loading.value = true
      try {
        console.log('📡 发送真实API请求: GET /api/teachers/page')
        const response = await getTeachersByPage(
          pagination.currentPage - 1,
          pagination.pageSize,
          pagination.sortBy
        )

        if (response.data) {
          teacherList.value = response.data.content || response.data
          pagination.total = response.data.totalElements || response.data.length || 0
          console.log('✅ 成功获取教师数据，共', pagination.total, '条记录')
        }
      } catch (error) {
        console.error('❌ 教师API请求失败，使用模拟数据:', error)
        // API失败时使用模拟数据
        const mockTeachers = [
          // 计算机科学与技术学院
          {
            id: 1,
            employeeNumber: 'T001',
            name: '张教授',
            gender: '男',
            age: 45,
            department: '计算机科学与技术学院',
            title: '教授',
            phone: '13812345001',
            email: '<EMAIL>',
            hireDate: '2005-07-01',
            researchArea: '人工智能、机器学习',
            education: '博士',
            graduateSchool: '清华大学',
            officeLocation: 'CS楼301'
          },
          {
            id: 2,
            employeeNumber: 'T002',
            name: '李副教授',
            gender: '女',
            age: 38,
            department: '计算机科学与技术学院',
            title: '副教授',
            phone: '13812345002',
            email: '<EMAIL>',
            hireDate: '2010-09-01',
            researchArea: '数据库系统、大数据',
            education: '博士',
            graduateSchool: '北京大学',
            officeLocation: 'CS楼302'
          },
          {
            id: 3,
            employeeNumber: 'T003',
            name: '王讲师',
            gender: '男',
            age: 32,
            department: '计算机科学与技术学院',
            title: '讲师',
            phone: '15012345003',
            email: '<EMAIL>',
            hireDate: '2015-09-01',
            researchArea: '软件工程、系统架构',
            education: '硕士',
            graduateSchool: '浙江大学',
            officeLocation: 'CS楼303'
          },
          {
            id: 4,
            employeeNumber: 'T004',
            name: '陈博士',
            gender: '女',
            age: 29,
            department: '计算机科学与技术学院',
            title: '助教',
            phone: '13712345004',
            email: '<EMAIL>',
            hireDate: '2020-03-01',
            researchArea: '计算机视觉、深度学习',
            education: '博士',
            graduateSchool: '上海交通大学',
            officeLocation: 'CS楼304'
          },

          // 数学与统计学院
          {
            id: 5,
            employeeNumber: 'T005',
            name: '刘教授',
            gender: '男',
            age: 52,
            department: '数学与统计学院',
            title: '教授',
            phone: '18612345005',
            email: '<EMAIL>',
            hireDate: '2000-07-01',
            researchArea: '应用数学、数值分析',
            education: '博士',
            graduateSchool: '中科院数学所',
            officeLocation: 'MATH楼201'
          },
          {
            id: 6,
            employeeNumber: 'T006',
            name: '杨副教授',
            gender: '女',
            age: 41,
            department: '数学与统计学院',
            title: '副教授',
            phone: '13512345006',
            email: '<EMAIL>',
            hireDate: '2008-09-01',
            researchArea: '统计学、数据分析',
            education: '博士',
            graduateSchool: '复旦大学',
            officeLocation: 'MATH楼202'
          },

          // 物理学院
          {
            id: 7,
            employeeNumber: 'T007',
            name: '赵教授',
            gender: '男',
            age: 48,
            department: '物理学院',
            title: '教授',
            phone: '15912345007',
            email: '<EMAIL>',
            hireDate: '2003-08-01',
            researchArea: '理论物理、量子力学',
            education: '博士',
            graduateSchool: '中科院物理所',
            officeLocation: 'PHY楼101'
          },
          {
            id: 8,
            employeeNumber: 'T008',
            name: '孙讲师',
            gender: '女',
            age: 35,
            department: '物理学院',
            title: '讲师',
            phone: '13312345008',
            email: '<EMAIL>',
            hireDate: '2012-09-01',
            researchArea: '凝聚态物理、材料科学',
            education: '博士',
            graduateSchool: '南京大学',
            officeLocation: 'PHY楼102'
          },

          // 化学学院
          {
            id: 9,
            employeeNumber: 'T009',
            name: '周副教授',
            gender: '男',
            age: 39,
            department: '化学学院',
            title: '副教授',
            phone: '18812345009',
            email: '<EMAIL>',
            hireDate: '2009-07-01',
            researchArea: '有机化学、药物合成',
            education: '博士',
            graduateSchool: '华东理工大学',
            officeLocation: 'CHEM楼201'
          },
          {
            id: 10,
            employeeNumber: 'T010',
            name: '吴助教',
            gender: '女',
            age: 28,
            department: '化学学院',
            title: '助教',
            phone: '13612345010',
            email: '<EMAIL>',
            hireDate: '2019-09-01',
            researchArea: '分析化学、环境化学',
            education: '硕士',
            graduateSchool: '大连理工大学',
            officeLocation: 'CHEM楼202'
          },

          // 生物学院
          {
            id: 11,
            employeeNumber: 'T011',
            name: '郑教授',
            gender: '女',
            age: 46,
            department: '生物学院',
            title: '教授',
            phone: '15612345011',
            email: '<EMAIL>',
            hireDate: '2006-08-01',
            researchArea: '分子生物学、基因工程',
            education: '博士',
            graduateSchool: '中科院生物所',
            officeLocation: 'BIO楼301'
          },
          {
            id: 12,
            employeeNumber: 'T012',
            name: '冯讲师',
            gender: '男',
            age: 33,
            department: '生物学院',
            title: '讲师',
            phone: '13412345012',
            email: '<EMAIL>',
            hireDate: '2014-09-01',
            researchArea: '细胞生物学、生物信息学',
            education: '博士',
            graduateSchool: '中山大学',
            officeLocation: 'BIO楼302'
          },

          // 工程学院
          {
            id: 13,
            employeeNumber: 'T013',
            name: '韩副教授',
            gender: '男',
            age: 42,
            department: '工程学院',
            title: '副教授',
            phone: '18712345013',
            email: '<EMAIL>',
            hireDate: '2007-09-01',
            researchArea: '机械工程、自动化',
            education: '博士',
            graduateSchool: '西安交通大学',
            officeLocation: 'ENG楼401'
          },
          {
            id: 14,
            employeeNumber: 'T014',
            name: '曹助教',
            gender: '女',
            age: 30,
            department: '工程学院',
            title: '助教',
            phone: '13212345014',
            email: '<EMAIL>',
            hireDate: '2018-03-01',
            researchArea: '电气工程、控制系统',
            education: '硕士',
            graduateSchool: '华中科技大学',
            officeLocation: 'ENG楼402'
          },

          // 年轻教师
          {
            id: 15,
            employeeNumber: 'T015',
            name: '许博士',
            gender: '男',
            age: 31,
            department: '计算机科学与技术学院',
            title: '讲师',
            phone: '15312345015',
            email: '<EMAIL>',
            hireDate: '2021-07-01',
            researchArea: '网络安全、区块链',
            education: '博士',
            graduateSchool: '电子科技大学',
            officeLocation: 'CS楼305'
          }
        ]

        // 模拟分页数据
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize
        const endIndex = startIndex + pagination.pageSize
        teacherList.value = mockTeachers.slice(startIndex, endIndex)
        pagination.total = mockTeachers.length

        console.log('🔄 使用模拟教师数据，共', mockTeachers.length, '条记录')
        ElMessage.warning('教师API不可用，使用模拟数据')
      } finally {
        loading.value = false
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchTeacherData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      pagination.currentPage = 1
      fetchTeacherData()
    }

    // 处理排序变化
    const handleSortChange = (column) => {
      if (column.prop && column.order) {
        pagination.sortBy = column.prop + (column.order === 'ascending' ? ',asc' : ',desc')
      } else {
        pagination.sortBy = 'id'
      }
      fetchTeacherData()
    }

    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      fetchTeacherData()
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchTeacherData()
    }

    // 添加教师
    const handleAdd = () => {
      router.push('/teachers/add')
    }

    // 编辑教师
    const handleEdit = (row) => {
      router.push(`/teachers/edit/${row.id}`)
    }

    // 删除教师
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除教师 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteTeacher(row.id)
          ElMessage.success('删除成功')
          fetchTeacherData()
        } catch (error) {
          console.error('删除教师失败:', error)
          ElMessage.error('删除教师失败')
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    }

    // 日期格式化
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }

    // 初始化图表
    const initCharts = async () => {
      try {
        // 获取部门分布数据
        const departmentRes = await getTeacherStatsByDepartment()
        if (departmentRes) {
          const departmentData = departmentRes
          const departmentChartInstance = echarts.init(document.getElementById('departmentChart'))
          departmentChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: Object.keys(departmentData)
            },
            series: [
              {
                name: '部门分布',
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: Object.entries(departmentData).map(([name, value]) => ({ name, value }))
              }
            ]
          })
          departmentChart.value = departmentChartInstance
        }

        // 获取职称分布数据
        const titleRes = await getTeacherStatsByTitle()
        if (titleRes) {
          const titleData = titleRes
          const titleChartInstance = echarts.init(document.getElementById('titleChart'))
          titleChartInstance.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            xAxis: {
              type: 'category',
              data: Object.keys(titleData)
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '教师数量',
                type: 'bar',
                data: Object.values(titleData)
              }
            ]
          })
          titleChart.value = titleChartInstance
        }
      } catch (error) {
        console.error('加载图表数据失败:', error)
        ElMessage.error('加载图表数据失败')
      }
    }

    // 监听窗口大小变化，调整图表大小
    const resizeCharts = () => {
      if (departmentChart.value) {
        departmentChart.value.resize()
      }
      if (titleChart.value) {
        titleChart.value.resize()
      }
    }

    // 组件挂载后初始化
    onMounted(() => {
      fetchTeacherData()
      initCharts()
      window.addEventListener('resize', resizeCharts)
    })

    // 组件卸载前清理事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', resizeCharts)
      if (departmentChart.value) {
        departmentChart.value.dispose()
      }
      if (titleChart.value) {
        titleChart.value.dispose()
      }
    })

    return {
      loading,
      teacherList,
      pagination,
      searchForm,
      formatDate,
      fetchTeacherData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleEdit,
      handleDelete
    }
  }
}
</script>

<style scoped>
.teacher-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.data-visualization {
  margin-top: 30px;
}

.chart-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}
</style> 