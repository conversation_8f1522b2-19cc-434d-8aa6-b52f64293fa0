package com.university.management.model.common;

/**
 * 结果状态码常量
 */
public class ResultCodeConstant {

    /**
     * 成功
     */
    public static final String CODE_000000 = "000000";
    public static final String CODE_000000_MSG = "操作成功";

    /**
     * 失败
     */
    public static final String CODE_000001 = "000001";
    public static final String CODE_000001_MSG = "操作失败";

    /**
     * 参数错误
     */
    public static final String CODE_000002 = "000002";
    public static final String CODE_000002_MSG = "参数错误";

    /**
     * 未授权
     */
    public static final String CODE_000401 = "000401";
    public static final String CODE_000401_MSG = "未授权";

    /**
     * 禁止访问
     */
    public static final String CODE_000403 = "000403";
    public static final String CODE_000403_MSG = "禁止访问";

    /**
     * 资源不存在
     */
    public static final String CODE_000404 = "000404";
    public static final String CODE_000404_MSG = "资源不存在";

    /**
     * 服务器内部错误
     */
    public static final String CODE_000500 = "000500";
    public static final String CODE_000500_MSG = "服务器内部错误";
} 