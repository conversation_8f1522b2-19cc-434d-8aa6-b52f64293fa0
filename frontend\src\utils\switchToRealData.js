/**
 * 切换到真实数据模式的工具脚本
 * 用于确保前端使用后端真实数据而不是模拟数据
 */

import { toggleMockData, showMockStatus } from '@/config/mock'

/**
 * 切换到真实数据模式
 */
export function switchToRealData() {
  // 关闭模拟数据
  toggleMockData(false)
  
  // 清除可能的缓存
  localStorage.removeItem('useMockData')
  localStorage.setItem('useMockData', 'false')
  
  // 显示状态
  showMockStatus()
  
  console.log('%c✅ 已切换到真实数据模式', 'color: #67C23A; font-weight: bold;')
  console.log('📡 前端将从后端API获取数据')
  console.log('🔗 后端地址: http://localhost:8083')
  
  // 提示用户刷新页面
  if (confirm('已切换到真实数据模式，是否刷新页面以应用更改？')) {
    window.location.reload()
  }
}

/**
 * 切换到模拟数据模式
 */
export function switchToMockData() {
  // 启用模拟数据
  toggleMockData(true)
  
  // 设置缓存
  localStorage.setItem('useMockData', 'true')
  
  // 显示状态
  showMockStatus()
  
  console.log('%c🔧 已切换到模拟数据模式', 'color: #409EFF; font-weight: bold;')
  console.log('💾 前端将使用本地模拟数据')
  
  // 提示用户刷新页面
  if (confirm('已切换到模拟数据模式，是否刷新页面以应用更改？')) {
    window.location.reload()
  }
}

/**
 * 检查后端连接状态
 */
export async function checkBackendConnection() {
  try {
    console.log('🔍 检查后端连接状态...')

    // 首先尝试简单的ping接口
    const response = await fetch('/api/ping', {
      method: 'GET',
      timeout: 5000
    })

    if (response.ok) {
      console.log('%c✅ 后端连接正常', 'color: #67C23A; font-weight: bold;')
      return true
    } else {
      // 如果ping失败，尝试其他接口
      console.log('🔄 尝试其他接口...')
      const fallbackResponse = await fetch('/api/departments', {
        method: 'GET',
        timeout: 5000
      })

      if (fallbackResponse.ok) {
        console.log('%c✅ 后端连接正常（通过备用接口）', 'color: #67C23A; font-weight: bold;')
        return true
      } else {
        console.log('%c❌ 后端连接失败', 'color: #F56C6C; font-weight: bold;')
        return false
      }
    }
  } catch (error) {
    console.log('%c❌ 后端连接失败', 'color: #F56C6C; font-weight: bold;')
    console.error('连接错误:', error.message)
    return false
  }
}

/**
 * 自动检测并切换数据模式
 */
export async function autoSwitchDataMode() {
  console.log('🔄 自动检测数据模式...')
  
  const isBackendAvailable = await checkBackendConnection()
  
  if (isBackendAvailable) {
    console.log('✅ 后端可用，切换到真实数据模式')
    switchToRealData()
  } else {
    console.log('⚠️ 后端不可用，使用模拟数据模式')
    switchToMockData()
    
    // 显示提示信息
    console.group('💡 后端启动指南')
    console.log('1. 确保后端服务正在运行在 http://localhost:8083')
    console.log('2. 检查数据库连接是否正常')
    console.log('3. 确保已导入扩展数据（600个学生，100个教师）')
    console.log('4. 启动后端后，可以调用 switchToRealData() 切换到真实数据')
    console.groupEnd()
  }
}

/**
 * 显示当前数据模式状态
 */
export function showCurrentDataMode() {
  const useMockData = localStorage.getItem('useMockData') === 'true'
  
  console.group('📊 当前数据模式状态')
  console.log('模式:', useMockData ? '🔧 模拟数据模式' : '📡 真实数据模式')
  console.log('后端地址:', 'http://localhost:8083')
  console.log('前端代理:', '/api -> http://localhost:8083')
  
  if (useMockData) {
    console.log('%c💡 提示: 调用 switchToRealData() 切换到真实数据', 'color: #409EFF;')
  } else {
    console.log('%c💡 提示: 调用 switchToMockData() 切换到模拟数据', 'color: #409EFF;')
  }
  
  console.groupEnd()
  
  showMockStatus()
}

/**
 * 测试API连接
 */
export async function testApiConnection() {
  console.log('🧪 测试API连接...')
  
  const testEndpoints = [
    '/api/students/page?page=0&size=5',
    '/api/teachers/page?page=0&size=5',
    '/api/departments',
    '/api/courses/page?page=0&size=5'
  ]
  
  for (const endpoint of testEndpoints) {
    try {
      console.log(`测试: ${endpoint}`)
      const response = await fetch(endpoint)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${endpoint} - 成功`, data)
      } else {
        console.log(`❌ ${endpoint} - 失败 (${response.status})`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 错误:`, error.message)
    }
  }
}

// 在控制台中暴露这些函数，方便调试
if (typeof window !== 'undefined') {
  window.switchToRealData = switchToRealData
  window.switchToMockData = switchToMockData
  window.checkBackendConnection = checkBackendConnection
  window.autoSwitchDataMode = autoSwitchDataMode
  window.showCurrentDataMode = showCurrentDataMode
  window.testApiConnection = testApiConnection
  
  // 显示可用的调试函数
  console.group('🛠️ 数据模式调试工具')
  console.log('switchToRealData() - 切换到真实数据模式')
  console.log('switchToMockData() - 切换到模拟数据模式')
  console.log('checkBackendConnection() - 检查后端连接')
  console.log('autoSwitchDataMode() - 自动检测并切换模式')
  console.log('showCurrentDataMode() - 显示当前模式状态')
  console.log('testApiConnection() - 测试API连接')
  console.groupEnd()
}
