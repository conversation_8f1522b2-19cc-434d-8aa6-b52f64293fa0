package com.university.management.controller;

import com.university.management.model.entity.Teacher;
import com.university.management.model.vo.ApiResponse;
import com.university.management.service.TeacherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags = "教师管理")
@RestController
@RequestMapping("/api/teachers")
public class TeacherController {

    private final TeacherService teacherService;

    @Autowired
    public TeacherController(TeacherService teacherService) {
        this.teacherService = teacherService;
    }

    @ApiOperation("获取所有教师信息")
    @GetMapping
    public ApiResponse<List<Teacher>> getAllTeachers() {
        return ApiResponse.success(teacherService.findAll());
    }

    @ApiOperation("分页获取教师信息")
    @GetMapping("/page")
    public ApiResponse<Page<Teacher>> getTeachersByPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(sortBy));
        return ApiResponse.success(teacherService.findAll(pageRequest));
    }

    @ApiOperation("条件查询教师信息")
    @GetMapping("/search")
    public ApiResponse<Page<Teacher>> searchTeachers(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String position,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return ApiResponse.success(teacherService.findByConditions(name, department, position, pageRequest));
    }

    @ApiOperation("根据ID获取教师信息")
    @GetMapping("/{id}")
    public ApiResponse<Teacher> getTeacherById(@PathVariable Integer id) {
        return teacherService.findById(id)
                .map(ApiResponse::success)
                .orElse(ApiResponse.errorGeneric(404, "教师不存在"));
    }

    @ApiOperation("创建教师信息")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<Teacher> createTeacher(@Valid @RequestBody Teacher teacher) {
        return ApiResponse.success("创建成功", teacherService.createTeacher(teacher));
    }

    @ApiOperation("更新教师信息")
    @PutMapping("/{id}")
    public ApiResponse<Teacher> updateTeacher(@PathVariable Integer id, @Valid @RequestBody Teacher teacherDetails) {
        return ApiResponse.success("更新成功", teacherService.updateTeacher(id, teacherDetails));
    }

    @ApiOperation("删除教师信息")
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteTeacher(@PathVariable Integer id) {
        teacherService.deleteTeacher(id);
        return ApiResponse.success();
    }

    @ApiOperation("获取教师部门分布统计")
    @GetMapping("/stats/department")
    public ApiResponse<Map<String, Long>> getTeacherStatsByDepartment() {
        return ApiResponse.success(teacherService.getTeacherStatsByDepartment());
    }

    @ApiOperation("获取教师职位分布统计")
    @GetMapping("/stats/position")
    public ApiResponse<Map<String, Long>> getTeacherStatsByPosition() {
        return ApiResponse.success(teacherService.getTeacherStatsByPosition());
    }

    @ApiOperation("获取教师职称分布统计")
    @GetMapping("/stats/title")
    public ApiResponse<Map<String, Long>> getTeacherStatsByTitle() {
        return ApiResponse.success(teacherService.getTeacherStatsByTitle());
    }
}