<template>
  <div class="student-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学生管理</h2>
      <p>管理学生信息，包括查看、添加、编辑和删除学生记录</p>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入学生姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="专业">
          <el-select
            v-model="searchForm.major"
            placeholder="请选择专业"
            clearable
            style="width: 200px"
          >
            <el-option label="全部专业" value="" />
            <el-option label="计算机科学与技术" value="计算机科学与技术" />
            <el-option label="软件工程" value="软件工程" />
            <el-option label="网络工程" value="网络工程" />
            <el-option label="信息安全" value="信息安全" />
            <el-option label="数据科学与大数据技术" value="数据科学与大数据技术" />
            <el-option label="人工智能" value="人工智能" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select
            v-model="searchForm.grade"
            placeholder="请选择年级"
            clearable
            style="width: 150px"
          >
            <el-option label="全部年级" value="" />
            <el-option label="2020级" value="2020级" />
            <el-option label="2021级" value="2021级" />
            <el-option label="2022级" value="2022级" />
            <el-option label="2023级" value="2023级" />
            <el-option label="2024级" value="2024级" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card" shadow="never">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加学生
      </el-button>
      <el-button type="success" @click="handleExport" :loading="exportLoading">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
      <el-button type="warning" @click="handleRefresh" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="studentList"
        v-loading="loading"
        element-loading-text="正在加载学生数据..."
        stripe
        border
        style="width: 100%"
        :empty-text="loadingError ? '数据加载失败' : '暂无数据'"
      >
        <el-table-column prop="studentNo" label="学号" width="120" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.gender === 1 ? 'primary' : 'danger'">
              {{ scope.row.gender === 1 ? '男' : '女' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="majorName" label="专业" width="150" />
        <el-table-column prop="className" label="班级" width="120" />
        <el-table-column prop="enrollYear" label="入学年份" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
              {{ scope.row.status === 0 ? '在校' : '离校' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 错误提示 -->
    <el-alert
      v-if="loadingError"
      :title="loadingError"
      type="error"
      :closable="false"
      show-icon
      style="margin-top: 20px"
    >
      <template #default>
        <p>{{ loadingError }}</p>
        <el-button type="primary" size="small" @click="handleRefresh" style="margin-top: 10px">
          重新加载
        </el-button>
      </template>
    </el-alert>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Search, Refresh, Plus, Download } from '@element-plus/icons-vue'
import { getStudentsByPage, searchStudents } from '@/api/student.js'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const loadingError = ref(null)
const studentList = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  major: '',
  grade: ''
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 获取学生列表数据
const fetchStudentData = async () => {
  loading.value = true
  loadingError.value = null
  
  try {
    const params = {
      name: searchForm.name || null,
      major: searchForm.major || null,
      grade: searchForm.grade || null,
      page: pagination.currentPage - 1,  // Spring Data JPA 分页从0开始
      size: pagination.pageSize
    }

    console.log('📡 调用真实API获取学生数据...', params)

    // 根据是否有搜索条件选择不同的API
    let response
    if (params.name || params.major || params.grade) {
      // 有搜索条件，使用搜索API
      response = await searchStudents(params)
    } else {
      // 无搜索条件，使用分页API
      response = await getStudentsByPage(params.page, params.size)
    }
    
    if (response.success) {
      // 处理分页数据结构
      if (response.data && typeof response.data === 'object' && 'content' in response.data) {
        // 分页API返回的数据结构
        studentList.value = response.data.content || []
        pagination.total = response.data.totalElements || 0
      } else {
        // 搜索API或普通API返回的数据结构
        studentList.value = response.data || []
        pagination.total = response.total || response.data?.length || 0
      }

      console.log('✅ 成功获取真实学生数据，共', pagination.total, '条记录')

      if (studentList.value.length === 0) {
        ElMessage.info('暂无学生数据')
      }
    } else {
      throw new Error(response.message || '获取学生数据失败')
    }
  } catch (error) {
    console.error('获取学生数据失败:', error)
    loadingError.value = error.message || '获取学生数据失败'
    ElMessage.error({
      message: '获取学生数据失败: ' + (error.message || '未知错误'),
      duration: 5000,
      showClose: true
    })
    studentList.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.currentPage = 1
  fetchStudentData()
}

// 重置搜索
const handleReset = () => {
  searchForm.name = ''
  searchForm.major = ''
  searchForm.grade = ''
  pagination.currentPage = 1
  fetchStudentData()
}

// 刷新数据
const handleRefresh = () => {
  fetchStudentData()
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchStudentData()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  fetchStudentData()
}

// 操作处理
const handleAdd = () => {
  ElMessage.info('添加学生功能开发中...')
}

const handleView = (row) => {
  ElMessage.info(`查看学生: ${row.name}`)
}

const handleEdit = (row) => {
  ElMessage.info(`编辑学生: ${row.name}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学生 "${row.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    ElMessage.info(`删除学生功能开发中: ${row.name}`)
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleExport = () => {
  exportLoading.value = true
  setTimeout(() => {
    exportLoading.value = false
    ElMessage.info('导出功能开发中...')
  }, 1000)
}

// 组件挂载时获取数据
onMounted(() => {
  console.log('🚀 StudentList组件已挂载，开始获取真实数据')
  fetchStudentData()
})
</script>

<style scoped>
.student-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.el-table {
  font-size: 14px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}
</style>
