import request from '@/utils/request';
import type { Student, StudentQueryParams, StudentPageResult, MajorStats, GradeStats } from '@/types/student';

/**
 * 分页获取学生列表
 * @param params 查询参数
 * @returns Promise
 */
export function getStudentsByPage(params: StudentQueryParams) {
  return request({
    url: '/api/students/page',
    method: 'get',
    params
  });
}

/**
 * 条件查询学生
 * @param params 查询条件
 * @returns Promise
 */
export function searchStudents(params: StudentQueryParams) {
  return request({
    url: '/api/students/search',
    method: 'get',
    params
  });
}

/**
 * 获取所有学生
 * @returns Promise
 */
export function getAllStudents() {
  return request({
    url: '/api/students',
    method: 'get'
  });
}

/**
 * 根据ID获取学生信息
 * @param id 学生ID
 * @returns Promise
 */
export function getStudentById(id: number) {
  return request({
    url: `/api/students/${id}`,
    method: 'get'
  });
}

/**
 * 创建学生
 * @param data 学生数据
 * @returns Promise
 */
export function createStudent(data: Partial<Student>) {
  return request({
    url: '/api/students',
    method: 'post',
    data
  });
}

/**
 * 更新学生信息
 * @param id 学生ID
 * @param data 学生数据
 * @returns Promise
 */
export function updateStudent(id: number, data: Partial<Student>) {
  return request({
    url: `/api/students/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除学生
 * @param id 学生ID
 * @returns Promise
 */
export function deleteStudent(id: number) {
  return request({
    url: `/api/students/${id}`,
    method: 'delete'
  });
}

/**
 * 获取学生专业分布统计
 * @returns Promise
 */
export function getStudentStatsByMajor() {
  return request<MajorStats>({
    url: '/api/students/stats/major',
    method: 'get'
  });
}

/**
 * 获取学生年级分布统计
 * @returns Promise
 */
export function getStudentStatsByGrade() {
  return request<GradeStats>({
    url: '/api/students/stats/grade',
    method: 'get'
  });
} 