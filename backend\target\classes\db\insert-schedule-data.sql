-- 插入课程表数据
USE university_management;

-- 插入课程表数据（使用正确的course_id, teacher_id, classroom_id）
INSERT INTO schedule (course_id, teacher_id, classroom_id, semester, day_of_week, start_time, end_time, start_week, end_week, create_time, update_time, is_deleted) VALUES
-- 2024 Spring Semester Course Schedule
-- Computer Science Courses
(1131, 889, 649, '2024-Spring', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Programming Fundamentals - Monday morning
(1132, 890, 649, '2024-Spring', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Data Structures - Monday morning
(1133, 891, 650, '2024-Spring', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Algorithm Analysis - Monday afternoon
(1134, 892, 650, '2024-Spring', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Computer Networks - Tuesday morning
(1135, 893, 651, '2024-Spring', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Database Systems - Tuesday morning

(1136, 894, 649, '2024-Spring', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- C++ Programming - Wednesday morning
(1137, 895, 650, '2024-Spring', 3, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Discrete Mathematics - Wednesday morning
(1138, 896, 651, '2024-Spring', 3, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Computer Graphics - Wednesday afternoon
(1139, 889, 652, '2024-Spring', 4, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Machine Learning - Thursday morning
(1140, 890, 652, '2024-Spring', 4, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Artificial Intelligence - Thursday morning

(1141, 891, 653, '2024-Spring', 4, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Software Engineering - Thursday afternoon
(1142, 892, 653, '2024-Spring', 5, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Operating Systems - Friday morning

-- 电子工程课程
(1143, 897, 654, '2024春季', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Circuit Analysis - 周一上午
(1144, 898, 654, '2024春季', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Analog Electronics - 周一上午
(1145, 899, 655, '2024春季', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Digital Electronics - 周一下午
(1146, 900, 655, '2024春季', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Signal Processing - 周二上午
(1147, 901, 656, '2024春季', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Communication Systems - 周二上午

(1148, 897, 656, '2024春季', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Control Systems - 周三上午
(1149, 898, 657, '2024春季', 3, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Microprocessors - 周三上午

-- 机械工程课程
(1150, 902, 658, '2024春季', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Mechanical Drawing - 周一上午
(1151, 903, 658, '2024春季', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Theoretical Mechanics - 周一上午
(1152, 904, 659, '2024春季', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Materials Science - 周一下午
(1153, 902, 659, '2024春季', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Thermodynamics - 周二上午
(1154, 903, 660, '2024春季', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Fluid Mechanics - 周二上午

(1155, 904, 660, '2024春季', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Machine Design - 周三上午

-- 经济管理课程
(1156, 905, 661, '2024春季', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Management Principles - 周一上午
(1157, 906, 661, '2024春季', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Microeconomics - 周一上午
(1158, 907, 662, '2024春季', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Macroeconomics - 周一下午
(1159, 908, 662, '2024春季', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Financial Management - 周二上午
(1160, 905, 663, '2024春季', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Marketing Management - 周二上午

(1161, 906, 663, '2024春季', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Operations Management - 周三上午
(1162, 907, 664, '2024春季', 3, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- International Business - 周三上午

-- 外语课程
(1163, 909, 665, '2024春季', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Comprehensive English - 周一上午
(1164, 910, 665, '2024春季', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- English Grammar - 周一上午
(1165, 911, 666, '2024春季', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- English Literature - 周一下午
(1166, 909, 666, '2024春季', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Translation Theory - 周二上午
(1167, 910, 667, '2024春季', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Business English - 周二上午

(1168, 911, 667, '2024春季', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Japanese Language - 周三上午
(1169, 909, 668, '2024春季', 3, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Japanese Culture - 周三上午

-- 艺术设计课程
(1170, 913, 669, '2024春季', 1, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Design Fundamentals - 周一上午
(1171, 914, 669, '2024春季', 1, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Color Composition - 周一上午
(1172, 913, 670, '2024春季', 1, '14:00', '15:40', 1, 16, NOW(), NOW(), 0), -- Drawing Techniques - 周一下午
(1173, 914, 670, '2024春季', 2, '08:00', '09:40', 1, 16, NOW(), NOW(), 0), -- Graphic Design - 周二上午
(1174, 913, 671, '2024春季', 2, '10:00', '11:40', 1, 16, NOW(), NOW(), 0), -- Interior Design - 周二上午

(1175, 914, 671, '2024春季', 3, '08:00', '09:40', 1, 16, NOW(), NOW(), 0); -- Digital Media Design - 周三上午
