package com.university.management.service;

import com.university.management.model.entity.Teacher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface TeacherService {
    
    Teacher createTeacher(Teacher teacher);
    
    Teacher updateTeacher(Integer id, Teacher teacherDetails);

    void deleteTeacher(Integer id);

    Optional<Teacher> findById(Integer id);
    
    Optional<Teacher> findByTeacherNo(String teacherNumber);
    
    List<Teacher> findAll();
    
    Page<Teacher> findAll(Pageable pageable);
    
    Page<Teacher> findByConditions(String name, String department, String position, Pageable pageable);
    
    boolean existsByTeacherNo(String teacherNumber);
    
    long countByDepartment(String department);
    
    Map<String, Long> getTeacherStatsByDepartment();

    Map<String, Long> getTeacherStatsByPosition();

    Map<String, Long> getTeacherStatsByTitle();
}