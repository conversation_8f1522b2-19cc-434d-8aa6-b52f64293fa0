package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 教室实体类
 */
@Data
@Entity
@Table(name = "classroom")
@TableName("classroom")
@ApiModel(value = "教室实体", description = "教室信息")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Classroom extends BaseEntity {

    /**
     * 教室编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("教室编号")
    private String roomNo;

    /**
     * 教室名称
     */
    @Column(nullable = false, length = 100)
    @ApiModelProperty("教室名称")
    private String name;

    /**
     * 教学楼
     */
    @Column(length = 50)
    @ApiModelProperty("教学楼")
    private String building;

    /**
     * 楼层
     */
    @ApiModelProperty("楼层")
    private Integer floor;

    /**
     * 教室类型(0-普通教室，1-多媒体教室，2-实验室，3-会议室)
     */
    @ApiModelProperty("教室类型(0-普通教室，1-多媒体教室，2-实验室，3-会议室)")
    private Integer type;

    /**
     * 容纳人数
     */
    @ApiModelProperty("容纳人数")
    private Integer capacity;

    /**
     * 设备描述
     */
    @Column(length = 500)
    @ApiModelProperty("设备描述")
    private String equipment;

    /**
     * 教室状态(0-正常，1-维修中，2-已废弃)
     */
    @ApiModelProperty("教室状态(0-正常，1-维修中，2-已废弃)")
    private Integer status;
} 