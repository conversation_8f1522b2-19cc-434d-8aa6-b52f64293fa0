# 大学学生管理系统

## 项目介绍
本系统是一个综合性的大学学生管理平台，用于管理大学内的各项资源和业务流程，包括学生信息管理、教师管理、课程安排、教室分配、宿舍管理、图书馆管理和体育馆管理等功能。系统采用前后端分离架构，后端基于Spring Boot框架，前端基于Vue框架。

## 系统架构
- **后端**：Spring Boot 2.7.x + MySQL + MyBatis-Plus + Redis
- **前端**：Vue3 + TypeScript + Element Plus

## 主要模块
系统包含以下主要功能模块：

### 1. 用户管理
- 学生信息管理（学号、姓名、性别、年龄、专业、班级等）
- 教师信息管理（工号、姓名、性别、职称、所属院系等）
- 管理员管理（账号、权限等）

### 2. 课程管理
- 课程基本信息（课程号、课程名称、学分、课时等）
- 课程安排（授课教师、上课时间、教室等）
- 选课管理（学生选课、退课等）
- 成绩管理（录入、查询、统计分析等）

### 3. 教室管理
- 教室信息维护（教室编号、类型、容量、设备等）
- 教室使用情况查询
- 教室预约管理

### 4. 宿舍管理
- 宿舍楼信息管理
- 宿舍分配与调整
- 宿舍维修申请与处理
- 住宿费用管理

### 5. 图书馆管理
- 图书信息管理（书号、书名、作者、出版社、入库时间等）
- 借阅管理（借书、还书、续借等）
- 借阅统计与分析

### 6. 体育馆管理
- 体育场馆信息管理
- 场馆预约与使用管理
- 器材借用与维护

### 7. 系统管理
- 用户认证与授权
- 系统配置管理
- 日志管理

## 数据库设计
系统采用关系型数据库MySQL，主要包含以下数据表：
1. 用户表（学生、教师、管理员）
2. 课程表
3. 教室表
4. 宿舍表
5. 图书表
6. 体育场馆表
7. 关系表（如选课关系、借阅关系等）

## 技术栈
### 后端
- 核心框架：Spring Boot 2.7.x
- 数据库：MySQL 8.0
- ORM框架：MyBatis-Plus *******
- 缓存：Redis
- 权限管理：Spring Security
- 接口文档：Knife4j (基于Swagger)
- 日志框架：Logback
- Java版本：JDK 17

### 前端
- 框架：Vue 3
- 类型系统：TypeScript
- UI组件库：Element Plus
- HTTP客户端：Axios
- 状态管理：Pinia
- 路由管理：Vue Router 4
- 构建工具：Vite

## 项目优化特性
1. **性能优化**：
   - Redis缓存热点数据，减少数据库查询压力
   - 前端虚拟滚动，优化大数据渲染
   - 数据分页加载，提升列表展示性能

2. **安全增强**：
   - 环境变量管理敏感信息，避免硬编码
   - Spring Security权限控制，基于角色的访问控制
   - 全局异常处理，增强系统稳定性

3. **类型安全**：
   - TypeScript全面引入，增强代码类型安全性
   - 接口参数统一定义，减少运行时错误
   - Pinia类型化状态管理

4. **用户体验**：
   - 加载状态优化，提供友好的加载提示
   - 错误处理增强，完善错误提示和恢复机制
   - 响应式布局设计，适配不同屏幕尺寸

## 如何运行
### 后端
```bash
cd backend
./mvnw spring-boot:run
```

### 前端
```bash
cd frontend
npm install
npm run dev
```

## 环境要求
- JDK 17或更高版本
- Node.js 18.x或更高版本
- MySQL 8.0
- Redis 6.x或更高版本

## 项目团队
- 项目经理：XXX
- 后端开发：XXX
- 前端开发：XXX
- 测试：XXX

## 版本信息
- 版本号：1.1.0
- 发布日期：2023-11-10
- 上次更新：2023-11-10 