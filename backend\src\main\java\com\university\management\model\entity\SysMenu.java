package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统菜单实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "SysMenu", description = "系统菜单信息")
@Entity
@TableName("sys_menu")
public class SysMenu extends BaseEntity {

    @ApiModelProperty(value = "父菜单ID")
    @Column(name = "parent_id")
    private Integer parentId;

    @ApiModelProperty(value = "菜单名称")
    @Column(nullable = false, length = 50)
    private String name;

    @ApiModelProperty(value = "路由地址")
    @Column(length = 100)
    private String path;

    @ApiModelProperty(value = "组件路径")
    @Column(length = 100)
    private String component;

    @ApiModelProperty(value = "菜单图标")
    @Column(length = 100)
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否可见(0-可见，1-隐藏)")
    private Integer visible;

    @ApiModelProperty(value = "菜单类型(0-目录，1-菜单，2-按钮)")
    private Integer type;

    @ApiModelProperty(value = "权限标识")
    @Column(length = 100)
    private String permission;

    @ApiModelProperty(value = "状态(0-正常，1-禁用)")
    private Integer status;

    @ManyToMany(mappedBy = "menus", fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<SysRole> roles = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private SysMenu parent;

    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<SysMenu> children = new ArrayList<>();
} 