package com.university.management.service;

import com.university.management.model.entity.Student;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface StudentService {
    
    Student createStudent(Student student);
    
    Student updateStudent(Integer id, Student studentDetails);

    void deleteStudent(Integer id);

    Optional<Student> findById(Integer id);
    
    Optional<Student> findByStudentNo(String studentNumber);
    
    List<Student> findAll();
    
    Page<Student> findAll(Pageable pageable);
    
    Page<Student> findByConditions(String name, String major, Integer grade, Pageable pageable);
    
    boolean existsByStudentNo(String studentNumber);
    
    long countByGrade(Integer grade);
    
    Map<String, Long> getStudentStatsByMajor();
    
    Map<Integer, Long> getStudentStatsByGrade();
} 