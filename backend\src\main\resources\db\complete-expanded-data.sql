-- 大学学生管理系统完整扩展数据脚本
-- 包含600个学生，100个老师，50个院系、专业、班级、课程、教室、宿舍
-- 数据全面且真实

USE university_management;

-- 执行基础扩展数据
SOURCE expanded-data.sql;

-- 执行学生数据第一部分
SOURCE student-data.sql;

-- 执行学生数据第二部分  
SOURCE student-data-part2.sql;

-- 执行学生数据第三部分
SOURCE student-data-part3.sql;

-- 执行学生数据最终部分
SOURCE student-data-final.sql;

-- 扩展体育场馆数据（新增5个场馆）
INSERT INTO sports_venue (name, type, location, capacity, opening_hours, status)
VALUES
('羽毛球馆', 0, '校区东北侧', 150, '08:00-21:00', 0),
('乒乓球馆', 0, '校区西北侧', 100, '08:00-21:00', 0),
('健身房', 0, '校区中心南侧', 80, '06:00-22:00', 0),
('田径场', 1, '校区北侧', 1000, '06:00-22:00', 0),
('排球场', 1, '校区南侧', 200, '08:00-21:00', 0);

-- 扩展排课数据（为新增课程和教师安排课程）
INSERT INTO schedule (course_id, teacher_id, classroom_id, semester, day_of_week, start_time, end_time, start_week, end_week)
VALUES
-- 数学类课程
(11, 9, 9, '2023-2024-1', 1, '08:00', '09:40', 1, 16),
(12, 10, 10, '2023-2024-1', 2, '08:00', '09:40', 1, 16),
(13, 9, 11, '2023-2024-1', 3, '08:00', '09:40', 1, 16),
(14, 10, 12, '2023-2024-1', 4, '08:00', '09:40', 1, 16),
-- 物理类课程
(15, 11, 13, '2023-2024-1', 1, '10:00', '11:40', 1, 16),
(16, 12, 14, '2023-2024-1', 2, '10:00', '11:40', 1, 16),
(17, 11, 15, '2023-2024-1', 3, '10:00', '11:40', 1, 16),
(18, 12, 16, '2023-2024-1', 4, '10:00', '11:40', 1, 16),
-- 化学类课程
(19, 13, 17, '2023-2024-1', 1, '14:00', '15:40', 1, 16),
(20, 14, 18, '2023-2024-1', 2, '14:00', '15:40', 1, 16),
(21, 13, 19, '2023-2024-1', 3, '14:00', '15:40', 1, 16),
(22, 14, 20, '2023-2024-1', 4, '14:00', '15:40', 1, 16),
-- 生物类课程
(23, 15, 21, '2023-2024-1', 1, '16:00', '17:40', 1, 16),
(24, 16, 22, '2023-2024-1', 2, '16:00', '17:40', 1, 16),
(25, 15, 23, '2023-2024-1', 3, '16:00', '17:40', 1, 16),
(26, 16, 24, '2023-2024-1', 4, '16:00', '17:40', 1, 16),
-- 环境工程类课程
(27, 17, 25, '2023-2024-1', 1, '08:00', '09:40', 1, 16),
(28, 18, 26, '2023-2024-1', 2, '08:00', '09:40', 1, 16),
(29, 17, 27, '2023-2024-1', 3, '08:00', '09:40', 1, 16),
(30, 18, 28, '2023-2024-1', 4, '08:00', '09:40', 1, 16),
-- 设计类课程
(31, 19, 29, '2023-2024-1', 1, '10:00', '11:40', 1, 16),
(32, 20, 30, '2023-2024-1', 2, '10:00', '11:40', 1, 16),
(33, 19, 31, '2023-2024-1', 3, '10:00', '11:40', 1, 16),
(34, 20, 32, '2023-2024-1', 4, '10:00', '11:40', 1, 16),
-- 体育类课程
(35, 21, 33, '2023-2024-1', 1, '14:00', '15:40', 1, 16),
(36, 22, 34, '2023-2024-1', 2, '14:00', '15:40', 1, 16),
(37, 21, 35, '2023-2024-1', 3, '14:00', '15:40', 1, 16),
(38, 22, 36, '2023-2024-1', 4, '14:00', '15:40', 1, 16),
-- 音乐类课程
(39, 23, 49, '2023-2024-1', 1, '16:00', '17:40', 1, 16),
(40, 24, 50, '2023-2024-1', 2, '16:00', '17:40', 1, 16),
(41, 23, 49, '2023-2024-1', 3, '16:00', '17:40', 1, 16),
(42, 24, 50, '2023-2024-1', 4, '16:00', '17:40', 1, 16),
-- 新闻传播类课程
(43, 25, 37, '2023-2024-1', 1, '08:00', '09:40', 1, 16),
(44, 26, 38, '2023-2024-1', 2, '08:00', '09:40', 1, 16),
(45, 25, 39, '2023-2024-1', 3, '08:00', '09:40', 1, 16),
(46, 26, 40, '2023-2024-1', 4, '08:00', '09:40', 1, 16),
-- 法学类课程
(47, 27, 41, '2023-2024-1', 1, '10:00', '11:40', 1, 16),
(48, 28, 42, '2023-2024-1', 2, '10:00', '11:40', 1, 16),
(49, 27, 43, '2023-2024-1', 3, '10:00', '11:40', 1, 16),
(50, 28, 44, '2023-2024-1', 4, '10:00', '11:40', 1, 16);

-- 扩展选课数据（为新增学生安排选课）
INSERT INTO student_course (student_id, schedule_id, score, grade_point)
VALUES
-- 计算机专业学生选课
(9, 11, 88.5, 3.7), (9, 12, 91.0, 4.0), (9, 51, 85.0, 3.5),
(10, 11, 82.0, 3.3), (10, 12, 87.5, 3.6), (10, 51, 89.0, 3.8),
(11, 11, 90.5, 3.9), (11, 12, 85.5, 3.5), (11, 51, 92.0, 4.0),
(12, 11, 86.0, 3.6), (12, 12, 88.0, 3.7), (12, 51, 84.5, 3.4),
-- 软件工程专业学生选课
(13, 11, 89.0, 3.8), (13, 12, 86.5, 3.6), (13, 52, 91.5, 4.0),
(14, 11, 87.5, 3.6), (14, 12, 90.0, 3.9), (14, 52, 88.0, 3.7),
(15, 11, 85.0, 3.5), (15, 12, 89.5, 3.8), (15, 52, 86.5, 3.6),
(16, 11, 91.0, 4.0), (16, 12, 87.0, 3.6), (16, 52, 90.5, 3.9),
-- 数学专业学生选课
(17, 11, 95.0, 4.0), (17, 12, 93.5, 4.0), (17, 13, 92.0, 4.0),
(18, 11, 91.5, 4.0), (18, 12, 89.0, 3.8), (18, 13, 94.0, 4.0),
(19, 11, 88.0, 3.7), (19, 12, 91.5, 4.0), (19, 13, 89.5, 3.8),
(20, 11, 93.0, 4.0), (20, 12, 87.5, 3.6), (20, 13, 91.0, 4.0),
-- 物理专业学生选课
(21, 15, 87.0, 3.6), (21, 16, 89.5, 3.8), (21, 17, 85.5, 3.5),
(22, 15, 90.0, 3.9), (22, 16, 86.0, 3.6), (22, 17, 88.5, 3.7),
(23, 15, 85.5, 3.5), (23, 16, 91.0, 4.0), (23, 17, 87.0, 3.6),
(24, 15, 88.0, 3.7), (24, 16, 87.5, 3.6), (24, 17, 90.5, 3.9),
-- 化学专业学生选课
(25, 19, 86.5, 3.6), (25, 20, 88.0, 3.7), (25, 21, 90.0, 3.9),
(26, 19, 89.0, 3.8), (26, 20, 85.5, 3.5), (26, 21, 87.5, 3.6),
(27, 19, 91.5, 4.0), (27, 20, 89.5, 3.8), (27, 21, 86.0, 3.6),
(28, 19, 87.0, 3.6), (28, 20, 90.5, 3.9), (28, 21, 88.5, 3.7);

-- 扩展借阅记录（为新增学生和图书添加借阅记录）
INSERT INTO borrow_record (student_id, book_id, borrow_date, expected_return_date, actual_return_date, status)
VALUES
(9, 11, '2023-09-10', '2023-09-24', '2023-09-20', 1),
(10, 12, '2023-09-12', '2023-09-26', NULL, 0),
(11, 13, '2023-09-15', '2023-09-29', '2023-09-25', 1),
(12, 14, '2023-09-18', '2023-10-02', NULL, 0),
(13, 15, '2023-09-20', '2023-10-04', '2023-09-30', 1),
(14, 16, '2023-09-22', '2023-10-06', NULL, 0),
(15, 17, '2023-09-25', '2023-10-09', '2023-10-05', 1),
(16, 18, '2023-09-28', '2023-10-12', NULL, 0),
(17, 19, '2023-10-01', '2023-10-15', '2023-10-10', 1),
(18, 20, '2023-10-03', '2023-10-17', NULL, 0),
(19, 21, '2023-10-05', '2023-10-19', '2023-10-15', 1),
(20, 22, '2023-10-08', '2023-10-22', NULL, 0),
(21, 23, '2023-10-10', '2023-10-24', '2023-10-20', 1),
(22, 24, '2023-10-12', '2023-10-26', NULL, 0),
(23, 25, '2023-10-15', '2023-10-29', '2023-10-25', 1),
(24, 26, '2023-10-18', '2023-11-01', NULL, 0),
(25, 27, '2023-10-20', '2023-11-03', '2023-10-30', 1),
(26, 28, '2023-10-22', '2023-11-05', NULL, 0),
(27, 29, '2023-10-25', '2023-11-08', '2023-11-05', 1),
(28, 30, '2023-10-28', '2023-11-11', NULL, 0);

-- 扩展场馆预约记录
INSERT INTO venue_booking (venue_id, user_id, user_type, booking_date, start_time, end_time, purpose, status)
VALUES
(6, 3, 1, '2023-10-01', '14:00', '16:00', '羽毛球训练', 1),
(7, 4, 1, '2023-10-02', '16:00', '18:00', '乒乓球比赛', 1),
(8, 5, 1, '2023-10-03', '18:00', '20:00', '健身锻炼', 1),
(9, 6, 1, '2023-10-04', '08:00', '10:00', '晨跑训练', 1),
(10, 7, 1, '2023-10-05', '14:00', '16:00', '排球练习', 1),
(6, 3, 2, '2023-10-06', '10:00', '12:00', '教师羽毛球活动', 1),
(7, 4, 2, '2023-10-07', '14:00', '16:00', '教师乒乓球比赛', 1),
(8, 5, 2, '2023-10-08', '17:00', '19:00', '教师健身活动', 1),
(9, 6, 2, '2023-10-09', '07:00', '09:00', '教师晨练', 1),
(10, 7, 2, '2023-10-10', '15:00', '17:00', '教师排球赛', 1);

-- 更新宿舍入住情况
UPDATE dormitory SET occupied = 4 WHERE id IN (9, 11, 12, 13, 14, 15, 16, 17, 18, 19);
UPDATE dormitory SET occupied = 3 WHERE id IN (20, 21, 22, 23, 24, 25, 26, 27, 28, 29);
UPDATE dormitory SET occupied = 2 WHERE id IN (30, 31, 32, 33, 34, 35, 36, 37, 38, 39);
UPDATE dormitory SET occupied = 1 WHERE id IN (40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50);

-- 创建系统用户账号（为新增教师和学生创建登录账号）
INSERT INTO sys_user (username, password, real_name, user_type, related_id, phone, email, status)
VALUES
-- 教师账号（选择部分教师创建账号）
('teacher9', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '陈明', 2, 9, '13900009999', '<EMAIL>', 0),
('teacher15', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '张伟', 2, 15, '13900015555', '<EMAIL>', 0),
('teacher21', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '郑华', 2, 21, '13900021111', '<EMAIL>', 0),
('teacher29', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '杜强', 2, 29, '13900029999', '<EMAIL>', 0),
('teacher37', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '石伟', 2, 37, '13900037777', '<EMAIL>', 0),
-- 学生账号（选择部分学生创建账号）
('student9', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '陈志强', 1, 9, '13800009001', '<EMAIL>', 0),
('student17', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '邵志华', 1, 17, '13800009031', '<EMAIL>', 0),
('student25', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '谢志明', 1, 25, '13800009041', '<EMAIL>', 0),
('student33', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '阎志华', 1, 33, '13800009051', '<EMAIL>', 0),
('student41', '$2a$10$Y6wfIt.qMkQVVobx5P4toePK6OrBwYVbRKNVoEI9DtnqJ.PNEES2a', '方志明', 1, 41, '13800009061', '<EMAIL>', 0);

-- 为新用户分配角色
INSERT INTO sys_user_role (user_id, role_id)
VALUES
-- 教师角色
(6, 3), (7, 3), (8, 3), (9, 3), (10, 3),
-- 学生角色  
(11, 4), (12, 4), (13, 4), (14, 4), (15, 4);

COMMIT;
