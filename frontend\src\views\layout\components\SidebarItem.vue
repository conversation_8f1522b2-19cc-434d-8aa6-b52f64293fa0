<template>
  <div class="sidebar-item" :class="{ 'dashboard-mode': isDashboard }">
    <template v-if="hasOneShowingChild(item.children, item) && !onlyOneChild.children">
      <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown': !isNest}">
        <i :class="onlyOneChild.meta.icon || 'el-icon-document'"></i>
        <template #title>{{ onlyOneChild.meta.title }}</template>
      </el-menu-item>
    </template>

    <el-submenu v-else :index="resolvePath(item.path)" popper-append-to-body>
      <template #title>
        <i :class="item.meta.icon || 'el-icon-folder'"></i>
        <span>{{ item.meta.title }}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :is-nest="true"
        :base-path="resolvePath(child.path)"
        :is-dashboard="isDashboard"
      />
    </el-submenu>
  </div>
</template>

<script>
import { ref } from 'vue'
import path from 'path-browserify'

export default {
  name: 'SidebarItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    isDashboard: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const onlyOneChild = ref(null)

    /**
     * 判断是否只有一个显示的子菜单
     */
    const hasOneShowingChild = (children = [], parent) => {
      if (!children) {
        children = []
      }
      
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // 临时设置(如果只有一个子菜单则不显示父菜单)
          onlyOneChild.value = item
          return true
        }
      })

      // 如果只有一个子菜单，则不显示父菜单
      if (showingChildren.length === 1) {
        return true
      }

      // 如果没有子菜单，则使用父菜单作为唯一子菜单
      if (showingChildren.length === 0) {
        onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    }

    /**
     * 解析路由路径
     */
    const resolvePath = (routePath) => {
      if (/^(https?:|mailto:|tel:)/.test(routePath)) {
        return routePath
      }
      return path.resolve(props.basePath, routePath)
    }

    return {
      onlyOneChild,
      hasOneShowingChild,
      resolvePath
    }
  }
}
</script>

<style scoped>
.sidebar-item {
  width: 100%;
}

/* 菜单项样式优化 - 仅在非首页模式下应用 */
.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  padding: 0 20px !important;
  display: flex;
  align-items: center;
  border-radius: 0;
  margin: 0;
  text-align: left;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item i) {
  width: 20px;
  text-align: center;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
  display: inline-block;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item span) {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  white-space: nowrap;
}

/* 子菜单样式优化 - 仅在非首页模式下应用 */
.sidebar-item:not(.dashboard-mode) :deep(.el-submenu) {
  width: 100%;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-submenu__title) {
  height: 50px;
  line-height: 50px;
  padding: 0 20px !important;
  display: flex;
  align-items: center;
  border-radius: 0;
  margin: 0;
  text-align: left;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-submenu__title i) {
  width: 20px;
  text-align: center;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
  display: inline-block;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-submenu__title span) {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
  white-space: nowrap;
}

/* 子菜单内容样式 - 仅在非首页模式下应用 */
.sidebar-item:not(.dashboard-mode) :deep(.el-menu--inline) {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-menu--inline .el-menu-item) {
  padding-left: 50px !important;
  background-color: transparent !important;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-menu--inline .el-menu-item i) {
  width: 16px;
  font-size: 14px;
  margin-right: 8px;
}

/* 激活状态样式 - 仅在非首页模式下应用 */
.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item.is-active) {
  background-color: #409EFF !important;
  color: #fff !important;
}

.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item.is-active i) {
  color: #fff !important;
}

/* 悬停效果 - 仅在非首页模式下应用 */
.sidebar-item:not(.dashboard-mode) :deep(.el-menu-item:hover),
.sidebar-item:not(.dashboard-mode) :deep(.el-submenu__title:hover) {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.submenu-title-noDropdown {
  padding-left: 20px !important;
}
</style>