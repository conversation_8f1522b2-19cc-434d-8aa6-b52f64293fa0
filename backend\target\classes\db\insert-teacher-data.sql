-- 插入教师数据
USE university_management;

-- 插入教师数据（使用正确的department_id）
INSERT INTO teacher (teacher_no, name, gender, birthday, age, title, phone, email, department_id, hire_date, status) VALUES
-- Computer Science College Teachers (department_id = 529)
('T001001', '<PERSON>', 1, '1975-03-15', 49, 3, '13900001001', 'z<PERSON><PERSON>@university.edu', 529, '2000-09-01', 0),
('T001002', '<PERSON>', 0, '1980-07-22', 44, 2, '13900001002', '<EMAIL>', 529, '2005-09-01', 0),
('T001003', '<PERSON>', 1, '1978-11-08', 46, 2, '13900001003', 'wang<PERSON><PERSON>@university.edu', 529, '2003-09-01', 0),
('T001004', '<PERSON>', 0, '1985-05-12', 39, 1, '13900001004', '<EMAIL>', 529, '2010-09-01', 0),
('T001005', '<PERSON>', 1, '1982-09-25', 42, 1, '13900001005', 'ch<PERSON><EMAIL>', 529, '2008-09-01', 0),
('T001006', '<PERSON> <PERSON>', 0, '1987-02-28', 37, 1, '13900001006', '<EMAIL>', 529, '2013-09-01', 0),
('T001007', '<PERSON> <PERSON>', 1, '1981-10-12', 43, 2, '13900001007', 'lin<PERSON>@university.edu', 529, '2007-09-01', 0),
('T001008', 'He Jing', 0, '1988-03-08', 36, 1, '13900001008', '<EMAIL>', 529, '2015-09-01', 0),

-- Electronic Engineering College Teachers (department_id = 530)
('T002001', 'Tian Jun', 1, '1976-05-10', 48, 3, '13900002001', '<EMAIL>', 530, '2001-09-01', 0),
('T002002', 'Deng Hong', 0, '1982-09-18', 42, 2, '13900002002', '<EMAIL>', 530, '2008-09-01', 0),
('T002003', 'Han Bin', 1, '1984-01-25', 40, 1, '13900002003', '<EMAIL>', 530, '2011-09-01', 0),
('T002004', 'Cao Min', 0, '1980-12-03', 44, 2, '13900002004', '<EMAIL>', 530, '2006-09-01', 0),
('T002005', 'Jiang Tao', 1, '1986-08-16', 38, 1, '13900002005', '<EMAIL>', 530, '2013-09-01', 0),

-- Mechanical Engineering College Teachers (department_id = 531)
('T003001', 'Fang Jun', 1, '1974-02-20', 50, 3, '13900003001', '<EMAIL>', 531, '1999-09-01', 0),
('T003002', 'Shi Hong', 0, '1981-06-14', 43, 2, '13900003002', '<EMAIL>', 531, '2007-09-01', 0),
('T003003', 'Yuan Bin', 1, '1987-11-30', 37, 1, '13900003003', '<EMAIL>', 531, '2014-09-01', 0),
('T003004', 'Wen Xiaojun', 1, '1979-06-18', 45, 2, '13900003004', '<EMAIL>', 531, '2005-09-01', 0),

-- Economics and Management College Teachers (department_id = 532)
('T004001', 'Long Li', 0, '1979-03-25', 45, 2, '13900004001', '<EMAIL>', 532, '2004-09-01', 0),
('T004002', 'Duan Jun', 1, '1983-07-08', 41, 1, '13900004002', '<EMAIL>', 532, '2010-09-01', 0),
('T004003', 'Shi Min', 0, '1982-05-16', 42, 2, '13900004003', '<EMAIL>', 532, '2008-09-01', 0),
('T004004', 'Gu Tao', 1, '1986-09-22', 38, 1, '13900004004', '<EMAIL>', 532, '2013-09-01', 0),

-- Foreign Languages College Teachers (department_id = 533)
('T005001', 'Xia Hua', 0, '1980-01-12', 44, 2, '13900005001', '<EMAIL>', 533, '2006-09-01', 0),
('T005002', 'Hou Bin', 1, '1984-08-05', 40, 1, '13900005002', '<EMAIL>', 533, '2011-09-01', 0),
('T005003', 'Tan Li', 0, '1985-12-18', 39, 1, '13900005003', '<EMAIL>', 533, '2012-09-01', 0),

-- Art and Design College Teachers (department_id = 534)
('T006001', 'Pan Jun', 1, '1981-04-28', 43, 2, '13900006001', '<EMAIL>', 534, '2007-09-01', 0),
('T006002', 'Cui Min', 0, '1987-10-15', 37, 1, '13900006002', '<EMAIL>', 534, '2014-09-01', 0);
