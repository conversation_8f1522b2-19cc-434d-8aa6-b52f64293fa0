import request from '@/utils/request'

const baseUrl = '/api/students'

// 获取所有学生信息
export function getAllStudents() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取学生信息
export function getStudentsByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询学生信息
export function searchStudents({ name, major, grade, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      major,
      grade,
      page,
      size
    }
  })
}

// 根据ID获取学生信息
export function getStudentById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建学生信息
export function createStudent(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新学生信息
export function updateStudent(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除学生信息
export function deleteStudent(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取学生专业分布统计
export function getStudentStatsByMajor() {
  return request({
    url: `${baseUrl}/stats/major`,
    method: 'get'
  })
}

// 获取学生年级分布统计
export function getStudentStatsByGrade() {
  return request({
    url: `${baseUrl}/stats/grade`,
    method: 'get'
  })
} 