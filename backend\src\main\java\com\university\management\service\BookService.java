package com.university.management.service;

import com.university.management.model.entity.Book;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 图书服务接口
 */
public interface BookService {

    /**
     * 保存图书
     */
    Book save(Book book);

    /**
     * 根据ID查找图书
     */
    Optional<Book> findById(Integer id);

    /**
     * 获取所有图书
     */
    List<Book> findAll();

    /**
     * 分页获取图书
     */
    Page<Book> findAll(Pageable pageable);

    /**
     * 根据ID删除图书
     */
    void deleteById(Integer id);
} 