package com.university.management.repository;

import com.university.management.model.entity.BorrowRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 借阅记录数据访问接口
 */
@Repository
public interface BorrowRecordRepository extends JpaRepository<BorrowRecord, Integer> {
    
    /**
     * 根据学生ID查询借阅记录
     *
     * @param studentId 学生ID
     * @return 借阅记录列表
     */
    List<BorrowRecord> findByStudentId(Integer studentId);
    
    /**
     * 根据图书ID查询借阅记录
     *
     * @param bookId 图书ID
     * @return 借阅记录列表
     */
    List<BorrowRecord> findByBookId(Integer bookId);
    
    /**
     * 根据状态查询借阅记录
     *
     * @param status 状态
     * @return 借阅记录列表
     */
    List<BorrowRecord> findByStatus(Integer status);
    
    /**
     * 条件查询借阅记录
     *
     * @param studentId 学生ID
     * @param bookId 图书ID
     * @param status 状态
     * @param pageable 分页参数
     * @return 借阅记录分页结果
     */
    @Query("SELECT br FROM BorrowRecord br WHERE " +
           "(:studentId IS NULL OR br.studentId = :studentId) AND " +
           "(:bookId IS NULL OR br.bookId = :bookId) AND " +
           "(:status IS NULL OR br.status = :status)")
    Page<BorrowRecord> findByConditions(
            @Param("studentId") Integer studentId, 
            @Param("bookId") Integer bookId, 
            @Param("status") Integer status, 
            Pageable pageable);
    
    /**
     * 查询逾期未还的借阅记录
     *
     * @param currentDate 当前日期
     * @return 逾期未还的借阅记录列表
     */
    @Query("SELECT br FROM BorrowRecord br WHERE br.status = 0 AND br.dueDate < :currentDate")
    List<BorrowRecord> findOverdueRecords(@Param("currentDate") LocalDate currentDate);
} 