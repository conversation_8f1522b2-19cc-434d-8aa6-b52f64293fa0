package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 排课表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Schedule", description = "排课表信息")
@Entity
@TableName("schedule")
public class Schedule extends BaseEntity {

    @ApiModelProperty(value = "课程ID")
    @Column(nullable = false)
    private Integer courseId;

    @ApiModelProperty(value = "教师ID")
    @Column(nullable = false)
    private Integer teacherId;

    @ApiModelProperty(value = "教室ID")
    @Column(nullable = false)
    private Integer classroomId;

    @ApiModelProperty(value = "学期")
    @Column(length = 20)
    private String semester;

    @ApiModelProperty(value = "星期几(1-7)")
    private Integer dayOfWeek;

    @ApiModelProperty(value = "开始时间")
    @Column(length = 10)
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @Column(length = 10)
    private String endTime;

    @ApiModelProperty(value = "开始周")
    private Integer startWeek;

    @ApiModelProperty(value = "结束周")
    private Integer endWeek;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "courseId", insertable = false, updatable = false)
    @TableField(exist = false)
    private Course course;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "teacherId", insertable = false, updatable = false)
    @TableField(exist = false)
    private Teacher teacher;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "classroomId", insertable = false, updatable = false)
    @TableField(exist = false)
    private Classroom classroom;

    @OneToMany(mappedBy = "schedule", fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<StudentCourse> studentCourses = new ArrayList<>();
} 