/**
 * 模拟API服务
 * 用于前端开发和测试，当后端API不可用时使用
 */

import { 
  students, 
  teachers, 
  courses, 
  departments, 
  majors, 
  classes, 
  books, 
  borrowingRecords, 
  classrooms, 
  dormitories, 
  sportsVenues 
} from './data.js'

// 模拟延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 分页工具函数
const paginate = (data, page = 0, size = 10) => {
  const start = page * size
  const end = start + size
  return {
    content: data.slice(start, end),
    totalElements: data.length,
    totalPages: Math.ceil(data.length / size),
    size: size,
    number: page,
    first: page === 0,
    last: page >= Math.ceil(data.length / size) - 1
  }
}

// 搜索工具函数
const search = (data, searchParams, searchFields) => {
  return data.filter(item => {
    return searchFields.some(field => {
      const value = item[field]
      const searchValue = searchParams[field]
      if (!searchValue) return true
      if (typeof value === 'string') {
        return value.toLowerCase().includes(searchValue.toLowerCase())
      }
      return value === searchValue
    })
  })
}

// 模拟API响应
export const mockApi = {
  // 学生管理API
  students: {
    getAll: async () => {
      await delay()
      return { success: true, data: students }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(students, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, major, grade, page = 0, size = 10 } = params
      let filtered = students
      
      if (name) {
        filtered = filtered.filter(s => s.name.includes(name))
      }
      if (major) {
        filtered = filtered.filter(s => s.majorName.includes(major))
      }
      if (grade) {
        filtered = filtered.filter(s => s.enrollYear.toString() === grade)
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    },
    
    getById: async (id) => {
      await delay()
      const student = students.find(s => s.id === parseInt(id))
      return student ? { success: true, data: student } : { success: false, message: '学生不存在' }
    },
    
    create: async (data) => {
      await delay()
      const newStudent = { ...data, id: students.length + 1, createdAt: new Date().toISOString() }
      students.push(newStudent)
      return { success: true, data: newStudent }
    },
    
    update: async (id, data) => {
      await delay()
      const index = students.findIndex(s => s.id === parseInt(id))
      if (index !== -1) {
        students[index] = { ...students[index], ...data }
        return { success: true, data: students[index] }
      }
      return { success: false, message: '学生不存在' }
    },
    
    delete: async (id) => {
      await delay()
      const index = students.findIndex(s => s.id === parseInt(id))
      if (index !== -1) {
        students.splice(index, 1)
        return { success: true, message: '删除成功' }
      }
      return { success: false, message: '学生不存在' }
    }
  },

  // 教师管理API
  teachers: {
    getAll: async () => {
      await delay()
      return { success: true, data: teachers }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(teachers, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, department, title, page = 0, size = 10 } = params
      let filtered = teachers
      
      if (name) {
        filtered = filtered.filter(t => t.name.includes(name))
      }
      if (department) {
        filtered = filtered.filter(t => t.departmentName.includes(department))
      }
      if (title) {
        filtered = filtered.filter(t => t.title === title)
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    },
    
    getById: async (id) => {
      await delay()
      const teacher = teachers.find(t => t.id === parseInt(id))
      return teacher ? { success: true, data: teacher } : { success: false, message: '教师不存在' }
    },
    
    create: async (data) => {
      await delay()
      const newTeacher = { ...data, id: teachers.length + 1, createdAt: new Date().toISOString() }
      teachers.push(newTeacher)
      return { success: true, data: newTeacher }
    },
    
    update: async (id, data) => {
      await delay()
      const index = teachers.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        teachers[index] = { ...teachers[index], ...data }
        return { success: true, data: teachers[index] }
      }
      return { success: false, message: '教师不存在' }
    },
    
    delete: async (id) => {
      await delay()
      const index = teachers.findIndex(t => t.id === parseInt(id))
      if (index !== -1) {
        teachers.splice(index, 1)
        return { success: true, message: '删除成功' }
      }
      return { success: false, message: '教师不存在' }
    }
  },

  // 课程管理API
  courses: {
    getAll: async () => {
      await delay()
      return { success: true, data: courses }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(courses, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, department, type, page = 0, size = 10 } = params
      let filtered = courses
      
      if (name) {
        filtered = filtered.filter(c => c.name.includes(name))
      }
      if (department) {
        filtered = filtered.filter(c => c.departmentName.includes(department))
      }
      if (type) {
        filtered = filtered.filter(c => c.type === type)
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    },
    
    getById: async (id) => {
      await delay()
      const course = courses.find(c => c.id === parseInt(id))
      return course ? { success: true, data: course } : { success: false, message: '课程不存在' }
    },
    
    create: async (data) => {
      await delay()
      const newCourse = { ...data, id: courses.length + 1, createdAt: new Date().toISOString() }
      courses.push(newCourse)
      return { success: true, data: newCourse }
    },
    
    update: async (id, data) => {
      await delay()
      const index = courses.findIndex(c => c.id === parseInt(id))
      if (index !== -1) {
        courses[index] = { ...courses[index], ...data }
        return { success: true, data: courses[index] }
      }
      return { success: false, message: '课程不存在' }
    },
    
    delete: async (id) => {
      await delay()
      const index = courses.findIndex(c => c.id === parseInt(id))
      if (index !== -1) {
        courses.splice(index, 1)
        return { success: true, message: '删除成功' }
      }
      return { success: false, message: '课程不存在' }
    }
  },

  // 院系管理API
  departments: {
    getAll: async () => {
      await delay()
      return { success: true, data: departments }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(departments, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, code, page = 0, size = 10 } = params
      let filtered = departments
      
      if (name) {
        filtered = filtered.filter(d => d.name.includes(name))
      }
      if (code) {
        filtered = filtered.filter(d => d.code.includes(code))
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 专业管理API
  majors: {
    getAll: async () => {
      await delay()
      return { success: true, data: majors }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(majors, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, code, departmentId, page = 0, size = 10 } = params
      let filtered = majors
      
      if (name) {
        filtered = filtered.filter(m => m.name.includes(name))
      }
      if (code) {
        filtered = filtered.filter(m => m.code.includes(code))
      }
      if (departmentId) {
        filtered = filtered.filter(m => m.departmentId === parseInt(departmentId))
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 班级管理API
  classes: {
    getAll: async () => {
      await delay()
      return { success: true, data: classes }
    },
    
    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(classes, page, size) }
    },
    
    search: async (params) => {
      await delay()
      const { name, grade, majorId, page = 0, size = 10 } = params
      let filtered = classes
      
      if (name) {
        filtered = filtered.filter(c => c.name.includes(name))
      }
      if (grade) {
        filtered = filtered.filter(c => c.grade === grade)
      }
      if (majorId) {
        filtered = filtered.filter(c => c.majorId === parseInt(majorId))
      }
      
      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 图书管理API
  books: {
    getAll: async () => {
      await delay()
      return { success: true, data: books }
    },

    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(books, page, size) }
    },

    search: async (params) => {
      await delay()
      const { title, author, category, page = 0, size = 10 } = params
      let filtered = books

      if (title) {
        filtered = filtered.filter(b => b.title.includes(title))
      }
      if (author) {
        filtered = filtered.filter(b => b.author.includes(author))
      }
      if (category) {
        filtered = filtered.filter(b => b.category === category)
      }

      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 借阅记录API
  borrowingRecords: {
    getAll: async () => {
      await delay()
      return { success: true, data: borrowingRecords }
    },

    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(borrowingRecords, page, size) }
    },

    search: async (params) => {
      await delay()
      const { studentName, bookName, status, page = 0, size = 10 } = params
      let filtered = borrowingRecords

      if (studentName) {
        filtered = filtered.filter(r => r.studentName.includes(studentName))
      }
      if (bookName) {
        filtered = filtered.filter(r => r.bookTitle.includes(bookName))
      }
      if (status) {
        filtered = filtered.filter(r => r.status === status)
      }

      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 教室管理API
  classrooms: {
    getAll: async () => {
      await delay()
      return { success: true, data: classrooms }
    },

    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(classrooms, page, size) }
    },

    search: async (params) => {
      await delay()
      const { roomNo, building, type, page = 0, size = 10 } = params
      let filtered = classrooms

      if (roomNo) {
        filtered = filtered.filter(c => c.roomNo.includes(roomNo))
      }
      if (building) {
        filtered = filtered.filter(c => c.building.includes(building))
      }
      if (type) {
        filtered = filtered.filter(c => c.type === type)
      }

      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 宿舍管理API
  dormitories: {
    getAll: async () => {
      await delay()
      return { success: true, data: dormitories }
    },

    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(dormitories, page, size) }
    },

    search: async (params) => {
      await delay()
      const { dormitoryNo, building, type, page = 0, size = 10 } = params
      let filtered = dormitories

      if (dormitoryNo) {
        filtered = filtered.filter(d => d.dormitoryNo.includes(dormitoryNo))
      }
      if (building) {
        filtered = filtered.filter(d => d.building.includes(building))
      }
      if (type) {
        filtered = filtered.filter(d => d.type === type)
      }

      return { success: true, data: paginate(filtered, page, size) }
    }
  },

  // 体育场馆API
  sportsVenues: {
    getAll: async () => {
      await delay()
      return { success: true, data: sportsVenues }
    },

    getByPage: async (page = 0, size = 10) => {
      await delay()
      return { success: true, data: paginate(sportsVenues, page, size) }
    },

    search: async (params) => {
      await delay()
      const { name, type, location, page = 0, size = 10 } = params
      let filtered = sportsVenues

      if (name) {
        filtered = filtered.filter(s => s.name.includes(name))
      }
      if (type) {
        filtered = filtered.filter(s => s.type === type)
      }
      if (location) {
        filtered = filtered.filter(s => s.location.includes(location))
      }

      return { success: true, data: paginate(filtered, page, size) }
    }
  }
}
