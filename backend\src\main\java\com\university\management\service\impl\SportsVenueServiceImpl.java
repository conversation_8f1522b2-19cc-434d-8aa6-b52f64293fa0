package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.SportsVenue;
import com.university.management.repository.SportsVenueRepository;
import com.university.management.service.SportsVenueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 体育场馆服务实现类
 */
@Service
@Transactional
public class SportsVenueServiceImpl implements SportsVenueService {

    private final SportsVenueRepository sportsVenueRepository;

    @Autowired
    public SportsVenueServiceImpl(SportsVenueRepository sportsVenueRepository) {
        this.sportsVenueRepository = sportsVenueRepository;
    }

    @Override
    public SportsVenue createVenue(SportsVenue venue) {
        return sportsVenueRepository.save(venue);
    }

    @Override
    public SportsVenue updateVenue(Integer id, SportsVenue venueDetails) {
        SportsVenue venue = sportsVenueRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("SportsVenue not found with id: " + id));

        venue.setName(venueDetails.getName());
        venue.setVenueNo(venueDetails.getVenueNo());
        venue.setType(venueDetails.getType());
        venue.setArea(venueDetails.getArea());
        venue.setLocation(venueDetails.getLocation());
        venue.setCapacity(venueDetails.getCapacity());
        venue.setOpeningHours(venueDetails.getOpeningHours());
        venue.setFee(venueDetails.getFee());
        venue.setManagerId(venueDetails.getManagerId());
        venue.setEquipment(venueDetails.getEquipment());
        venue.setDescription(venueDetails.getDescription());
        venue.setStatus(venueDetails.getStatus());
        
        return sportsVenueRepository.save(venue);
    }

    @Override
    public void deleteVenue(Integer id) {
        SportsVenue venue = sportsVenueRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("SportsVenue not found with id: " + id));
        sportsVenueRepository.delete(venue);
    }

    @Override
    public Optional<SportsVenue> findById(Integer id) {
        return sportsVenueRepository.findById(id);
    }

    @Override
    public Optional<SportsVenue> findByName(String name) {
        return sportsVenueRepository.findByName(name);
    }

    @Override
    public List<SportsVenue> findAll() {
        return sportsVenueRepository.findAll();
    }

    @Override
    public Page<SportsVenue> findAll(Pageable pageable) {
        return sportsVenueRepository.findAll(pageable);
    }

    @Override
    public Page<SportsVenue> findByConditions(String venueName, Integer venueType, String location, Boolean isAvailable, Pageable pageable) {
        return sportsVenueRepository.findByConditions(venueName, venueType, location, isAvailable, pageable);
    }

    @Override
    public List<SportsVenue> findByType(Integer type) {
        return sportsVenueRepository.findByType(type);
    }

    @Override
    public List<SportsVenue> findByLocation(String location) {
        return sportsVenueRepository.findByLocation(location);
    }

    @Override
    public Map<Integer, Long> getVenueStatsByType() {
        Map<Integer, Long> stats = new HashMap<>();
        List<SportsVenue> venues = sportsVenueRepository.findAll();
        
        stats = venues.stream()
                .collect(Collectors.groupingBy(SportsVenue::getType, Collectors.counting()));
        
        return stats;
    }

    @Override
    public Long countAvailableVenues() {
        return sportsVenueRepository.countByAvailable();
    }
} 