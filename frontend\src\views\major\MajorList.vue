<template>
  <div class="major-list-container">
    <div class="header">
      <h2><el-icon><School /></el-icon> 专业管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加专业
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="专业名称">
          <el-input v-model="searchForm.name" placeholder="请输入专业名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="专业代码">
          <el-input v-model="searchForm.code" placeholder="请输入专业代码" clearable></el-input>
        </el-form-item>
        <el-form-item label="所属院系">
          <el-select v-model="searchForm.departmentId" placeholder="请选择院系" clearable>
            <el-option 
              v-for="dept in departmentOptions" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table 
        v-loading="loading" 
        :data="majorList" 
        border 
        style="width: 100%" 
        row-key="id"
        @sort-change="handleSortChange">
        <el-table-column prop="majorNo" label="专业代码" width="120" sortable="custom"></el-table-column>
        <el-table-column prop="name" label="专业名称" width="200"></el-table-column>
        <el-table-column prop="departmentId" label="所属院系" width="180">
          <template #default="scope">
            {{ getDepartmentName(scope.row.departmentId) }}
          </template>
        </el-table-column>
        <el-table-column prop="degree" label="学位类型" width="120">
          <template #default="scope">
            <el-tag :type="getDegreeTagType(scope.row.degree)">
              {{ scope.row.degree || '未设置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="length" label="学制" width="80">
          <template #default="scope">
            {{ scope.row.length || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="studentCount" label="在校学生" width="100" sortable="custom">
          <template #default="scope">
            <el-link type="primary" @click="viewStudents(scope.row)">
              {{ scope.row.studentCount || 0 }}人
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isDeleted === 0 ? 'success' : 'danger'">
              {{ scope.row.isDeleted === 0 ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable="custom">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button size="small" type="primary" @click="handleEdit(scope.row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 统计图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <h3><el-icon><PieChart /></el-icon> 专业学位分布</h3>
        <div id="degreeChart" style="width: 100%; height: 300px;"></div>
      </div>
      <div class="chart-container">
        <h3><el-icon><Histogram /></el-icon> 各院系专业数量</h3>
        <div id="departmentChart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete, School, PieChart, Histogram } from '@element-plus/icons-vue'
import { searchMajors, deleteMajor, getMajorStats } from '@/api/major'
import { getAllDepartments } from '@/api/department'
import * as echarts from 'echarts'

export default {
  name: 'MajorList',
  components: {
    Plus, Search, Refresh, View, Edit, Delete, School, PieChart, Histogram
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const majorList = ref([])
    const departmentOptions = ref([])
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      code: '',
      departmentId: null
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 排序信息
    const sortInfo = reactive({
      prop: '',
      order: ''
    })

    // 图表实例
    const degreeChart = ref(null)
    const departmentChart = ref(null)

    // 获取专业列表数据
    const fetchMajorData = async () => {
      loading.value = true
      try {
        const params = {
          name: searchForm.name || null,
          code: searchForm.code || null,
          departmentId: searchForm.departmentId || null,
          page: pagination.currentPage - 1,
          size: pagination.pageSize
        }

        const res = await searchMajors(params)
        if (res) {
          majorList.value = res.content || []
          pagination.total = res.totalElements || 0
        }
      } catch (error) {
        console.error('获取专业数据失败:', error)
        if (error.message && error.message.includes('404')) {
          ElMessage.warning('专业管理功能暂未开放，请联系系统管理员')
        } else {
          ElMessage.error('获取专业数据失败')
        }
      } finally {
        loading.value = false
      }
    }

    // 获取院系选项
    const fetchDepartmentOptions = async () => {
      try {
        const res = await getAllDepartments()
        if (res) {
          departmentOptions.value = res
        }
      } catch (error) {
        console.error('获取院系数据失败:', error)
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchMajorData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        name: '',
        code: '',
        departmentId: null
      })
      pagination.currentPage = 1
      fetchMajorData()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      pagination.currentPage = 1
      fetchMajorData()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchMajorData()
    }

    // 排序改变
    const handleSortChange = ({ prop, order }) => {
      sortInfo.prop = prop
      sortInfo.order = order
      fetchMajorData()
    }

    // 添加专业
    const handleAdd = () => {
      router.push('/majors/add')
    }

    // 查看专业
    const handleView = (row) => {
      ElMessage.info(`查看专业：${row.name}`)
    }

    // 编辑专业
    const handleEdit = (row) => {
      router.push(`/majors/edit/${row.id}`)
    }

    // 删除专业
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除专业 ${row.name} 吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        await deleteMajor(row.id)
        ElMessage.success('删除成功')
        fetchMajorData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除专业失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 查看学生
    const viewStudents = (row) => {
      router.push(`/students?majorId=${row.id}`)
    }

    // 根据院系ID获取院系名称
    const getDepartmentName = (departmentId) => {
      if (!departmentId) return '未设置'
      const department = departmentOptions.value.find(dept => dept.id === departmentId)
      return department ? department.name : `院系ID: ${departmentId}`
    }

    // 获取学位类型标签类型
    const getDegreeTagType = (degree) => {
      const typeMap = {
        'BACHELOR': 'primary',
        'MASTER': 'success',
        'DOCTOR': 'warning'
      }
      return typeMap[degree] || 'info'
    }

    // 获取学位类型文本
    const getDegreeText = (degree) => {
      const textMap = {
        'BACHELOR': '学士',
        'MASTER': '硕士',
        'DOCTOR': '博士'
      }
      return textMap[degree] || degree
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    }

    // 初始化图表
    const initCharts = async () => {
      try {
        const statsRes = await getMajorStats()
        if (statsRes) {
          // 学位分布图表
          const degreeChartInstance = echarts.init(document.getElementById('degreeChart'))
          degreeChartInstance.setOption({
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [
              {
                name: '学位分布',
                type: 'pie',
                radius: '50%',
                data: statsRes.degreeStats || [],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
          degreeChart.value = degreeChartInstance

          // 院系专业数量图表
          const departmentChartInstance = echarts.init(document.getElementById('departmentChart'))
          departmentChartInstance.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: statsRes.departmentStats?.map(item => item.name) || []
            },
            yAxis: {
              type: 'value'
            },
            series: [
              {
                name: '专业数量',
                type: 'bar',
                data: statsRes.departmentStats?.map(item => item.count) || [],
                itemStyle: {
                  color: '#409EFF'
                }
              }
            ]
          })
          departmentChart.value = departmentChartInstance
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }

    // 窗口大小改变时重新调整图表
    const resizeCharts = () => {
      if (degreeChart.value) {
        degreeChart.value.resize()
      }
      if (departmentChart.value) {
        departmentChart.value.resize()
      }
    }

    onMounted(() => {
      fetchMajorData()
      fetchDepartmentOptions()
      setTimeout(() => {
        initCharts()
      }, 100)
      window.addEventListener('resize', resizeCharts)
    })

    onUnmounted(() => {
      if (degreeChart.value) {
        degreeChart.value.dispose()
      }
      if (departmentChart.value) {
        departmentChart.value.dispose()
      }
      window.removeEventListener('resize', resizeCharts)
    })

    return {
      loading,
      majorList,
      departmentOptions,
      searchForm,
      pagination,
      fetchMajorData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleView,
      handleEdit,
      handleDelete,
      viewStudents,
      getDepartmentName,
      getDegreeTagType,
      getDegreeText,
      formatDate
    }
  }
}
</script>

<style scoped>
.major-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  margin: 0;
}
</style>
