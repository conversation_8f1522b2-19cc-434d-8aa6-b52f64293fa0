package com.university.management.repository;

import com.university.management.model.entity.Student;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface StudentRepository extends JpaRepository<Student, Integer> {
    
    Optional<Student> findByStudentNo(String studentNo);
    
    Page<Student> findByNameContaining(String name, Pageable pageable);
    
    @Query("SELECT s FROM Student s WHERE " +
           "(:name IS NULL OR s.name LIKE %:name%) AND " +
           "(:major IS NULL OR s.majorId = :major) AND " +
           "(:grade IS NULL OR s.enrollYear = :grade)")
    Page<Student> findByConditions(
            @Param("name") String name, 
            @Param("major") String major, 
            @Param("grade") Integer grade, 
            Pageable pageable);
    
    boolean existsByStudentNo(String studentNo);
    
    long countByEnrollYear(Integer enrollYear);
} 