package com.university.management.service;

import com.university.management.model.entity.BorrowRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 借阅记录服务接口
 */
public interface BorrowRecordService {

    /**
     * 创建借阅记录
     *
     * @param borrowRecord 借阅记录信息
     * @return 创建后的借阅记录
     */
    BorrowRecord createBorrowRecord(BorrowRecord borrowRecord);

    /**
     * 更新借阅记录
     *
     * @param id 借阅记录ID
     * @param borrowRecordDetails 借阅记录详情
     * @return 更新后的借阅记录
     */
    BorrowRecord updateBorrowRecord(Integer id, BorrowRecord borrowRecordDetails);

    /**
     * 删除借阅记录
     *
     * @param id 借阅记录ID
     */
    void deleteBorrowRecord(Integer id);

    /**
     * 根据ID查询借阅记录
     *
     * @param id 借阅记录ID
     * @return 借阅记录信息
     */
    Optional<BorrowRecord> findById(Integer id);

    /**
     * 查询所有借阅记录
     *
     * @return 借阅记录列表
     */
    List<BorrowRecord> findAll();

    /**
     * 分页查询借阅记录
     *
     * @param pageable 分页参数
     * @return 借阅记录分页结果
     */
    Page<BorrowRecord> findAll(Pageable pageable);

    /**
     * 条件查询借阅记录
     *
     * @param studentId 学生ID
     * @param bookId 图书ID
     * @param status 状态
     * @param pageable 分页参数
     * @return 借阅记录分页结果
     */
    Page<BorrowRecord> findByConditions(Integer studentId, Integer bookId, Integer status, Pageable pageable);

    /**
     * 归还图书
     *
     * @param id 借阅记录ID
     * @return 更新后的借阅记录
     */
    BorrowRecord returnBook(Integer id);

    /**
     * 获取借阅统计信息
     *
     * @return 借阅统计信息
     */
    Map<String, Object> getBorrowingStats();
}