<template>
  <div class="settings-container">
    <div class="header">
      <h2><el-icon><Setting /></el-icon> 系统设置</h2>
    </div>

    <el-row :gutter="20">
      <!-- 个人偏好设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>个人偏好</span>
            </div>
          </template>
          
          <el-form label-width="120px" class="settings-form">
            <el-form-item label="主题模式">
              <el-radio-group v-model="settings.theme" @change="handleThemeChange">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言设置">
              <el-select v-model="settings.language" @change="handleLanguageChange" style="width: 200px">
                <el-option label="简体中文" value="zh-CN"></el-option>
                <el-option label="English" value="en-US"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="侧边栏">
              <el-switch
                v-model="settings.sidebarCollapsed"
                @change="handleSidebarChange"
                active-text="收起"
                inactive-text="展开"
              />
            </el-form-item>

            <el-form-item label="页面大小">
              <el-radio-group v-model="settings.pageSize" @change="handlePageSizeChange">
                <el-radio :label="10">10条/页</el-radio>
                <el-radio :label="20">20条/页</el-radio>
                <el-radio :label="50">50条/页</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 通知设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
            </div>
          </template>
          
          <el-form label-width="120px" class="settings-form">
            <el-form-item label="系统通知">
              <el-switch
                v-model="settings.notifications.system"
                @change="handleNotificationChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="邮件通知">
              <el-switch
                v-model="settings.notifications.email"
                @change="handleNotificationChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="短信通知">
              <el-switch
                v-model="settings.notifications.sms"
                @change="handleNotificationChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="浏览器通知">
              <el-switch
                v-model="settings.notifications.browser"
                @change="handleBrowserNotificationChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 安全设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </div>
          </template>
          
          <el-form label-width="120px" class="settings-form">
            <el-form-item label="自动登出">
              <el-select v-model="settings.autoLogout" @change="handleAutoLogoutChange" style="width: 200px">
                <el-option label="30分钟" :value="30"></el-option>
                <el-option label="1小时" :value="60"></el-option>
                <el-option label="2小时" :value="120"></el-option>
                <el-option label="4小时" :value="240"></el-option>
                <el-option label="永不" :value="0"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="登录验证">
              <el-switch
                v-model="settings.twoFactorAuth"
                @change="handleTwoFactorChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="记住登录">
              <el-switch
                v-model="settings.rememberLogin"
                @change="handleRememberLoginChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 数据设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <el-icon><DataBoard /></el-icon>
              <span>数据设置</span>
            </div>
          </template>
          
          <el-form label-width="120px" class="settings-form">
            <el-form-item label="数据缓存">
              <el-switch
                v-model="settings.dataCache"
                @change="handleDataCacheChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item label="自动保存">
              <el-switch
                v-model="settings.autoSave"
                @change="handleAutoSaveChange"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="danger" @click="handleClearCache">
                <el-icon><Delete /></el-icon>
                清除缓存
              </el-button>
              <el-button type="warning" @click="handleExportData">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="saveSettings" :loading="saving">
        <el-icon><Check /></el-icon>
        保存设置
      </el-button>
      <el-button @click="resetSettings">
        <el-icon><Refresh /></el-icon>
        重置默认
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Setting, User, Bell, Lock, DataBoard, 
  Check, Refresh, Delete, Download 
} from '@element-plus/icons-vue'

export default {
  name: 'Settings',
  components: {
    Setting, User, Bell, Lock, DataBoard,
    Check, Refresh, Delete, Download
  },
  setup() {
    const saving = ref(false)

    // 设置数据
    const settings = reactive({
      theme: 'light',
      language: 'zh-CN',
      sidebarCollapsed: false,
      pageSize: 20,
      notifications: {
        system: true,
        email: true,
        sms: false,
        browser: true
      },
      autoLogout: 60,
      twoFactorAuth: false,
      rememberLogin: true,
      dataCache: true,
      autoSave: true
    })

    // 默认设置
    const defaultSettings = { ...settings }

    // 加载设置
    const loadSettings = () => {
      const savedSettings = localStorage.getItem('userSettings')
      if (savedSettings) {
        try {
          Object.assign(settings, JSON.parse(savedSettings))
        } catch (error) {
          console.error('加载设置失败:', error)
        }
      }
    }

    // 保存设置
    const saveSettings = async () => {
      saving.value = true
      try {
        localStorage.setItem('userSettings', JSON.stringify(settings))
        ElMessage.success('设置保存成功')
      } catch (error) {
        console.error('保存设置失败:', error)
        ElMessage.error('保存设置失败')
      } finally {
        saving.value = false
      }
    }

    // 重置设置
    const resetSettings = () => {
      ElMessageBox.confirm('确定要重置为默认设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Object.assign(settings, defaultSettings)
        ElMessage.success('已重置为默认设置')
      }).catch(() => {
        // 取消操作
      })
    }

    // 主题变更
    const handleThemeChange = (theme) => {
      document.documentElement.setAttribute('data-theme', theme)
      ElMessage.success(`已切换到${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '自动'}主题`)
    }

    // 语言变更
    const handleLanguageChange = (language) => {
      ElMessage.success(`语言已切换为${language === 'zh-CN' ? '简体中文' : 'English'}`)
    }

    // 侧边栏变更
    const handleSidebarChange = (collapsed) => {
      ElMessage.info(`侧边栏已${collapsed ? '收起' : '展开'}`)
    }

    // 页面大小变更
    const handlePageSizeChange = (size) => {
      ElMessage.info(`页面大小已设置为${size}条/页`)
    }

    // 通知设置变更
    const handleNotificationChange = () => {
      ElMessage.info('通知设置已更新')
    }

    // 浏览器通知变更
    const handleBrowserNotificationChange = (enabled) => {
      if (enabled && 'Notification' in window) {
        Notification.requestPermission()
      }
    }

    // 自动登出变更
    const handleAutoLogoutChange = (minutes) => {
      const text = minutes === 0 ? '永不自动登出' : `${minutes}分钟后自动登出`
      ElMessage.info(text)
    }

    // 双因子认证变更
    const handleTwoFactorChange = (enabled) => {
      ElMessage.info(`双因子认证已${enabled ? '开启' : '关闭'}`)
    }

    // 记住登录变更
    const handleRememberLoginChange = (enabled) => {
      ElMessage.info(`记住登录已${enabled ? '开启' : '关闭'}`)
    }

    // 数据缓存变更
    const handleDataCacheChange = (enabled) => {
      ElMessage.info(`数据缓存已${enabled ? '开启' : '关闭'}`)
    }

    // 自动保存变更
    const handleAutoSaveChange = (enabled) => {
      ElMessage.info(`自动保存已${enabled ? '开启' : '关闭'}`)
    }

    // 清除缓存
    const handleClearCache = () => {
      ElMessageBox.confirm('确定要清除所有缓存数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除缓存逻辑
        ElMessage.success('缓存已清除')
      }).catch(() => {
        // 取消操作
      })
    }

    // 导出数据
    const handleExportData = () => {
      ElMessage.info('数据导出功能待实现')
    }

    // 组件挂载时加载设置
    onMounted(() => {
      loadSettings()
    })

    return {
      saving,
      settings,
      saveSettings,
      resetSettings,
      handleThemeChange,
      handleLanguageChange,
      handleSidebarChange,
      handlePageSizeChange,
      handleNotificationChange,
      handleBrowserNotificationChange,
      handleAutoLogoutChange,
      handleTwoFactorChange,
      handleRememberLoginChange,
      handleDataCacheChange,
      handleAutoSaveChange,
      handleClearCache,
      handleExportData
    }
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.settings-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-form {
  padding: 10px 0;
}

.action-buttons {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
