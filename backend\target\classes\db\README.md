# 大学学生管理系统扩展数据说明

## 概述
本数据包为大学学生管理系统提供了全面的测试数据，包含600个学生、100个教师以及50个院系、专业、班级、课程、教室、宿舍等完整数据。

## 数据规模

### 基础数据
- **院系**: 50个（涵盖工学、理学、管理学、文学、艺术学、教育学、法学、医学等）
- **专业**: 50个（分布在各个院系下）
- **班级**: 50个（2018-2023级，不同专业）
- **教师**: 100个（分布在各个院系，不同职称）
- **学生**: 600个（分布在不同专业、年级）

### 教学资源
- **课程**: 50门（必修课、选修课）
- **教室**: 50个（普通教室、多媒体教室、实验室）
- **图书**: 50本（各学科专业书籍）
- **体育场馆**: 10个（室内外各类运动场所）
- **宿舍**: 50个（男女宿舍，不同类型）

## 文件说明

### 主要数据文件
1. **expanded-data.sql** - 基础扩展数据（院系、专业、班级、教师、课程、教室、宿舍、图书）
2. **student-data.sql** - 学生数据第一部分（约150个学生）
3. **student-data-part2.sql** - 学生数据第二部分（约150个学生）
4. **student-data-part3.sql** - 学生数据第三部分（约150个学生）
5. **student-data-final.sql** - 学生数据最终部分（约150个学生）
6. **complete-expanded-data.sql** - 完整数据整合脚本

### 辅助文件
- **execute-all-data.sql** - 执行指南和数据统计
- **README.md** - 本说明文件

## 执行顺序

### 方法一：分步执行
```bash
# 1. 执行基础扩展数据
mysql -u root -p university_management < expanded-data.sql

# 2. 执行学生数据（按顺序）
mysql -u root -p university_management < student-data.sql
mysql -u root -p university_management < student-data-part2.sql
mysql -u root -p university_management < student-data-part3.sql
mysql -u root -p university_management < student-data-final.sql

# 3. 执行完整扩展数据
mysql -u root -p university_management < complete-expanded-data.sql
```

### 方法二：MySQL客户端执行
```sql
USE university_management;
\. expanded-data.sql
\. student-data.sql
\. student-data-part2.sql
\. student-data-part3.sql
\. student-data-final.sql
\. complete-expanded-data.sql
```

## 数据特点

### 学生分布
- **2019级**: 20个学生（临床医学、口腔医学5年制专业）
- **2020级**: 150个学生（各专业一年级）
- **2021级**: 200个学生（各专业二年级）
- **2022级**: 120个学生（各专业三年级）
- **2023级**: 110个学生（各专业四年级）

### 专业覆盖
- **工学类**: 计算机科学与技术、软件工程、网络工程、电子信息工程、通信工程、机械设计制造及其自动化、工业设计、化学工程与工艺、材料科学与工程、生物医学工程、环境工程、土木工程、能源与动力工程等
- **理学类**: 数学与应用数学、统计学、应用物理学、电子科学与技术、生物技术、药学、中药学等
- **管理学类**: 工商管理、市场营销、物流管理等
- **文学类**: 英语、新闻学、广播电视学等
- **艺术学类**: 视觉传达设计、产品设计、音乐表演、音乐学等
- **教育学类**: 体育教育、运动训练等
- **法学类**: 法学、知识产权等
- **医学类**: 临床医学、口腔医学、护理学、助产学等

### 教师分布
- **教授**: 15人（资深教师，主要负责重要课程）
- **副教授**: 35人（中级教师，承担主要教学任务）
- **讲师**: 40人（青年教师，参与教学和科研）
- **助教**: 10人（新入职教师，协助教学工作）

### 数据真实性
- 所有姓名均为常见中文姓名
- 身份证号码符合规范（虚构但格式正确）
- 手机号码、邮箱地址格式规范
- 地址信息真实（杭州市各区县）
- 课程设置符合实际教学安排

## 注意事项

1. **数据清理**: 执行前会清理现有数据，请确保备份重要信息
2. **外键约束**: 数据插入时会暂时禁用外键检查，执行完成后恢复
3. **字符编码**: 所有数据使用UTF-8编码，确保中文显示正常
4. **密码加密**: 用户密码使用BCrypt加密，默认密码为"123456"
5. **数据一致性**: 所有关联数据保持一致性，如学生的专业、班级、院系等

## 测试建议

### 功能测试
- 学生信息管理（增删改查）
- 教师信息管理
- 课程管理和排课
- 选课和成绩管理
- 图书借阅管理
- 宿舍管理
- 体育场馆预约

### 性能测试
- 大数据量查询性能
- 分页查询效果
- 复杂关联查询
- 并发访问测试

### 业务场景测试
- 学生选课流程
- 成绩录入和查询
- 图书借还流程
- 宿舍分配管理
- 教师课程安排

## 数据维护

### 定期清理
- 清理过期的借阅记录
- 更新学生年级信息
- 调整课程安排

### 数据扩展
- 可根据需要继续添加更多学生
- 增加新的专业和课程
- 扩展教学资源

## 联系方式
如有问题或建议，请联系系统管理员。
