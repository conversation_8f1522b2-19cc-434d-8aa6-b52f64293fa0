<template>
  <div class="user-list-container">
    <div class="header">
      <h2>用户管理</h2>
      <el-button type="primary" @click="handleAdd">添加用户</el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input v-model="searchForm.realName" placeholder="请输入真实姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.roleId" placeholder="请选择角色" clearable>
            <el-option 
              v-for="role in roleOptions" 
              :key="role.id" 
              :label="role.name" 
              :value="role.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table 
      v-loading="loading" 
      :data="userList" 
      border 
      style="width: 100%" 
      row-key="id"
      @sort-change="handleSortChange">
      <el-table-column prop="username" label="用户名" width="120"></el-table-column>
      <el-table-column prop="realName" label="真实姓名" width="120"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
      <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
      <el-table-column prop="roles" label="角色" width="180">
        <template #default="scope">
          <el-tag 
            v-for="role in scope.row.roles" 
            :key="role.id" 
            class="role-tag"
            type="success"
            effect="light">
            {{ role.name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag 
            :type="scope.row.status === 1 ? 'success' : 'danger'" 
            effect="dark"
            size="small">
            {{ scope.row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" sortable>
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginTime" label="最后登录" width="160">
        <template #default="scope">
          {{ scope.row.lastLoginTime ? formatDate(scope.row.lastLoginTime) : '未登录' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="280">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)">
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="info" @click="handleResetPassword(scope.row)">重置密码</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { searchUsers, deleteUser, resetPassword, updateUser } from '@/api/user'
import { getAllRoles } from '@/api/role'

export default {
  name: 'UserList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const userList = ref([])
    const roleOptions = ref([])

    // 分页设置
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0,
      sortBy: 'id'
    })

    // 搜索表单
    const searchForm = reactive({
      username: '',
      realName: '',
      roleId: ''
    })

    // 获取所有角色列表
    const fetchRoles = async () => {
      try {
        const res = await getAllRoles()
        if (res.data) {
          roleOptions.value = res.data
        }
      } catch (error) {
        console.error('获取角色列表失败:', error)

        // 使用模拟角色数据
        roleOptions.value = [
          { id: 1, name: '超级管理员', code: 'SUPER_ADMIN' },
          { id: 2, name: '系统管理员', code: 'ADMIN' },
          { id: 3, name: '教师', code: 'TEACHER' },
          { id: 4, name: '学生', code: 'STUDENT' },
          { id: 5, name: '访客', code: 'GUEST' }
        ]

        console.log('使用模拟角色数据')
      }
    }

    // 获取用户列表数据
    const fetchUserData = async () => {
      loading.value = true
      try {
        console.log('📡 发送真实API请求: GET /api/users/search')
        const params = {
          username: searchForm.username || null,
          realName: searchForm.realName || null,
          roleId: searchForm.roleId || null,
          page: pagination.currentPage - 1,  // Spring Data JPA 分页从0开始
          size: pagination.pageSize
        }

        const res = await searchUsers(params)
        if (res) {
          userList.value = res.content || []
          pagination.total = res.totalElements || 0
          console.log('✅ 成功获取用户数据，共', pagination.total, '条记录')
        }
      } catch (error) {
        console.error('❌ 用户API请求失败，使用模拟数据:', error)

        // API失败时使用模拟数据
        const mockUsers = [
          {
            id: 1,
            username: 'admin',
            realName: '系统管理员',
            email: '<EMAIL>',
            phone: '13800138000',
            roles: [{ id: 1, name: '超级管理员' }],
            status: 1,
            createTime: '2024-01-01 10:00:00',
            lastLoginTime: '2024-01-15 09:30:00'
          },
          {
            id: 2,
            username: 'teacher001',
            realName: '张教授',
            email: '<EMAIL>',
            phone: '13800138001',
            roles: [{ id: 3, name: '教师' }],
            status: 1,
            createTime: '2024-01-02 10:00:00',
            lastLoginTime: '2024-01-14 14:20:00'
          },
          {
            id: 3,
            username: 'student001',
            realName: '李小明',
            email: '<EMAIL>',
            phone: '13800138002',
            roles: [{ id: 4, name: '学生' }],
            status: 1,
            createTime: '2024-01-03 10:00:00',
            lastLoginTime: '2024-01-13 16:45:00'
          }
        ]

        // 模拟分页数据
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize
        const endIndex = startIndex + pagination.pageSize
        userList.value = mockUsers.slice(startIndex, endIndex)
        pagination.total = mockUsers.length

        console.log('🔄 使用模拟用户数据，共', mockUsers.length, '条记录')
        ElMessage.warning('用户API不可用，使用模拟数据')
      } finally {
        loading.value = false
      }
    }

    // 搜索操作
    const handleSearch = () => {
      pagination.currentPage = 1
      fetchUserData()
    }

    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      pagination.currentPage = 1
      fetchUserData()
    }

    // 处理排序变化
    const handleSortChange = (column) => {
      if (column.prop && column.order) {
        pagination.sortBy = column.prop + (column.order === 'ascending' ? ',asc' : ',desc')
      } else {
        pagination.sortBy = 'id'
      }
      fetchUserData()
    }

    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pagination.pageSize = val
      fetchUserData()
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      pagination.currentPage = val
      fetchUserData()
    }

    // 添加用户
    const handleAdd = () => {
      router.push('/users/add')
    }

    // 编辑用户
    const handleEdit = (row) => {
      router.push(`/users/edit/${row.id}`)
    }

    // 切换用户状态
    const handleToggleStatus = (row) => {
      const statusText = row.status === 1 ? '禁用' : '启用'
      ElMessageBox.confirm(`确定要${statusText}用户 ${row.username} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const updatedUser = { ...row, status: row.status === 1 ? 0 : 1 }
          await updateUser(row.id, updatedUser)
          ElMessage.success(`${statusText}成功`)
          fetchUserData()
        } catch (error) {
          console.error(`${statusText}用户失败:`, error)
          ElMessage.error(`${statusText}用户失败`)
        }
      }).catch(() => {
        // 取消操作，不做任何处理
      })
    }

    // 重置密码
    const handleResetPassword = (row) => {
      ElMessageBox.confirm(`确定要重置用户 ${row.username} 的密码吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await resetPassword(row.id)
          ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
        } catch (error) {
          console.error('重置密码失败:', error)
          ElMessage.error('重置密码失败')
        }
      }).catch(() => {
        // 取消操作，不做任何处理
      })
    }

    // 删除用户
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteUser(row.id)
          ElMessage.success('删除成功')
          fetchUserData()
        } catch (error) {
          console.error('删除用户失败:', error)
          ElMessage.error('删除用户失败')
        }
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    }

    // 日期格式化
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }

    // 组件挂载后初始化
    onMounted(() => {
      fetchRoles()
      fetchUserData()
    })

    return {
      loading,
      userList,
      roleOptions,
      pagination,
      searchForm,
      formatDate,
      fetchUserData,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSortChange,
      handleAdd,
      handleEdit,
      handleToggleStatus,
      handleResetPassword,
      handleDelete
    }
  }
}
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.role-tag {
  margin-right: 5px;
}
</style> 