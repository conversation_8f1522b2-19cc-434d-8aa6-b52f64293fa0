package com.university.management.service.impl;

import com.university.management.model.entity.Major;
import com.university.management.repository.MajorRepository;
import com.university.management.service.MajorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 专业服务实现类
 */
@Service
@Transactional
public class MajorServiceImpl implements MajorService {

    private final MajorRepository majorRepository;

    @Autowired
    public MajorServiceImpl(MajorRepository majorRepository) {
        this.majorRepository = majorRepository;
    }

    @Override
    public List<Major> findAll() {
        return majorRepository.findAll();
    }

    @Override
    public Page<Major> findAll(PageRequest pageRequest) {
        return majorRepository.findAll(pageRequest);
    }

    @Override
    public Page<Major> findByConditions(String name, Integer departmentId, PageRequest pageRequest) {
        if (name != null && departmentId != null) {
            return majorRepository.findByNameContainingAndDepartmentId(name, departmentId, pageRequest);
        } else if (name != null) {
            return majorRepository.findByNameContaining(name, pageRequest);
        } else if (departmentId != null) {
            return majorRepository.findByDepartmentId(departmentId, pageRequest);
        } else {
            return majorRepository.findAll(pageRequest);
        }
    }

    @Override
    public Optional<Major> findById(Integer id) {
        return majorRepository.findById(id);
    }

    @Override
    public Major save(Major major) {
        return majorRepository.save(major);
    }

    @Override
    public void deleteById(Integer id) {
        majorRepository.deleteById(id);
    }

    @Override
    public List<Major> findByDepartmentId(Integer departmentId) {
        return majorRepository.findByDepartmentId(departmentId);
    }

    @Override
    public Map<String, Object> getMajorStats() {
        List<Major> allMajors = majorRepository.findAll();
        Map<String, Object> stats = new HashMap<>();

        // 学位分布统计
        Map<String, Long> degreeStats = allMajors.stream()
            .collect(Collectors.groupingBy(
                major -> major.getDegree() != null ? major.getDegree() : "未知",
                Collectors.counting()
            ));

        // 各院系专业数量统计
        Map<String, Long> departmentStats = allMajors.stream()
            .collect(Collectors.groupingBy(
                major -> major.getDepartment() != null ? major.getDepartment().getName() : "未知院系",
                Collectors.counting()
            ));

        // 转换为前端需要的格式
        List<Map<String, Object>> degreeList = degreeStats.entrySet().stream()
            .map(entry -> {
                Map<String, Object> item = new HashMap<>();
                item.put("name", entry.getKey());
                item.put("value", entry.getValue());
                return item;
            })
            .collect(Collectors.toList());

        List<Map<String, Object>> departmentList = departmentStats.entrySet().stream()
            .map(entry -> {
                Map<String, Object> item = new HashMap<>();
                item.put("name", entry.getKey());
                item.put("count", entry.getValue());
                return item;
            })
            .collect(Collectors.toList());

        stats.put("degreeStats", degreeList);
        stats.put("departmentStats", departmentList);

        return stats;
    }
}
