package com.university.management.service.impl;

import com.university.management.exception.ResourceNotFoundException;
import com.university.management.model.entity.Teacher;
import com.university.management.repository.TeacherRepository;
import com.university.management.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class TeacherServiceImpl implements TeacherService {

    private final TeacherRepository teacherRepository;

    @Autowired
    public TeacherServiceImpl(TeacherRepository teacherRepository) {
        this.teacherRepository = teacherRepository;
    }

    @Override
    public Teacher createTeacher(Teacher teacher) {
        return teacherRepository.save(teacher);
    }

    @Override
    public Teacher updateTeacher(Integer id, Teacher teacherDetails) {
        Teacher teacher = teacherRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Teacher not found with id: " + id));

        teacher.setName(teacherDetails.getName());
        teacher.setTeacherNo(teacherDetails.getTeacherNo());
        teacher.setGender(teacherDetails.getGender());
        teacher.setAge(teacherDetails.getAge());
        teacher.setBirthday(teacherDetails.getBirthday());
        teacher.setCollegeId(teacherDetails.getCollegeId());
        teacher.setTitle(teacherDetails.getTitle());
        teacher.setPhone(teacherDetails.getPhone());
        teacher.setEmail(teacherDetails.getEmail());
        teacher.setIdCard(teacherDetails.getIdCard());
        teacher.setHireDate(teacherDetails.getHireDate());
        teacher.setStatus(teacherDetails.getStatus());
        
        return teacherRepository.save(teacher);
    }

    @Override
    public void deleteTeacher(Integer id) {
        Teacher teacher = teacherRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Teacher not found with id: " + id));
        teacherRepository.delete(teacher);
    }

    @Override
    public Optional<Teacher> findById(Integer id) {
        return teacherRepository.findById(id);
    }

    @Override
    public Optional<Teacher> findByTeacherNo(String teacherNumber) {
        return teacherRepository.findByTeacherNo(teacherNumber);
    }

    @Override
    public List<Teacher> findAll() {
        return teacherRepository.findAll();
    }

    @Override
    public Page<Teacher> findAll(Pageable pageable) {
        return teacherRepository.findAll(pageable);
    }

    @Override
    public Page<Teacher> findByConditions(String name, String department, String position, Pageable pageable) {
        // 这里根据实际情况修改，使用collegeId代替department，title代替position
        return teacherRepository.findByConditions(name, department, position, pageable);
    }

    @Override
    public boolean existsByTeacherNo(String teacherNumber) {
        return teacherRepository.existsByTeacherNo(teacherNumber);
    }

    @Override
    public long countByDepartment(String department) {
        // 这里根据实际情况修改，使用collegeId代替department
        return teacherRepository.countByCollegeId(Integer.valueOf(department));
    }

    @Override
    public Map<String, Long> getTeacherStatsByDepartment() {
        Map<String, Long> stats = new HashMap<>();
        List<Teacher> teachers = teacherRepository.findAll();

        // 使用部门名称进行分组，而不是collegeId
        stats = teachers.stream()
                .filter(teacher -> teacher.getDepartment() != null) // 过滤掉没有部门信息的教师
                .collect(Collectors.groupingBy(
                    teacher -> teacher.getDepartment().getName() != null ? teacher.getDepartment().getName() : "未知部门",
                    Collectors.counting()
                ));

        return stats;
    }

    @Override
    public Map<String, Long> getTeacherStatsByPosition() {
        Map<String, Long> stats = new HashMap<>();
        List<Teacher> teachers = teacherRepository.findAll();
        
        // 修改为使用title进行分组
        stats = teachers.stream()
                .collect(Collectors.groupingBy(teacher -> String.valueOf(teacher.getTitle()), Collectors.counting()));

        return stats;
    }

    @Override
    public Map<String, Long> getTeacherStatsByTitle() {
        List<Teacher> teachers = teacherRepository.findAll();

        // 使用title进行分组统计，将Integer转换为对应的字符串
        Map<String, Long> stats = teachers.stream()
                .collect(Collectors.groupingBy(teacher -> {
                    Integer title = teacher.getTitle();
                    if (title == null) return "未知";
                    switch (title) {
                        case 0: return "助教";
                        case 1: return "讲师";
                        case 2: return "副教授";
                        case 3: return "教授";
                        default: return "其他";
                    }
                }, Collectors.counting()));

        return stats;
    }
}