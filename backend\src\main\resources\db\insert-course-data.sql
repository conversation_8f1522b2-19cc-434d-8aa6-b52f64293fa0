-- 插入课程数据
USE university_management;

-- 插入课程数据（使用正确的department_id）
INSERT INTO course (course_no, name, credit, hours, type, department_id, description, status) VALUES
-- Computer Science College Courses (department_id = 529)
('CS101', 'Programming Fundamentals', 3.0, 48, 0, 529, 'Basic course in computer programming', 0),
('CS102', 'Data Structures', 4.0, 64, 0, 529, 'Learning principles and applications of various data structures', 0),
('CS103', 'Algorithm Analysis', 3.0, 48, 0, 529, 'Algorithm design and analysis methods', 0),
('CS104', 'Computer Networks', 3.0, 48, 0, 529, 'Computer network principles and technology', 0),
('CS105', 'Database Systems', 3.0, 48, 0, 529, 'Database theory and practice', 0),
('CS201', 'C++ Programming', 3.0, 48, 0, 529, 'C++ object-oriented programming language', 0),
('CS202', 'Discrete Mathematics', 3.0, 48, 0, 529, 'Mathematical foundations of computer science', 0),
('CS203', 'Computer Graphics', 3.0, 48, 1, 529, 'Computer graphics generation and processing technology', 0),
('CS204', 'Machine Learning', 3.0, 48, 1, 529, 'Introduction to machine learning algorithms and applications', 0),
('CS205', 'Artificial Intelligence', 3.0, 48, 1, 529, 'Fundamentals of artificial intelligence', 0),
('CS206', 'Software Engineering', 3.0, 48, 0, 529, 'Software development methodologies and practices', 0),
('CS207', 'Operating Systems', 3.0, 48, 0, 529, 'Operating system principles and implementation', 0),

-- Electronic Engineering College Courses (department_id = 530)
('EE101', 'Circuit Analysis', 3.0, 48, 0, 530, 'Circuit theory fundamentals', 0),
('EE102', 'Analog Electronics', 3.0, 48, 0, 530, 'Analog circuit design and analysis', 0),
('EE103', 'Digital Electronics', 3.0, 48, 0, 530, 'Digital circuit principles and applications', 0),
('EE201', 'Signal Processing', 3.0, 48, 0, 530, 'Digital signal processing techniques', 0),
('EE202', 'Communication Systems', 3.0, 48, 0, 530, 'Communication system design and analysis', 0),
('EE203', 'Control Systems', 3.0, 48, 0, 530, 'Automatic control system design', 0),
('EE204', 'Microprocessors', 3.0, 48, 1, 530, 'Microprocessor architecture and programming', 0),

-- Mechanical Engineering College Courses (department_id = 531)
('ME101', 'Mechanical Drawing', 3.0, 48, 0, 531, 'Mechanical drawing standards and specifications', 0),
('ME102', 'Theoretical Mechanics', 3.0, 48, 0, 531, 'Fundamental theories of mechanics', 0),
('ME103', 'Materials Science', 3.0, 48, 0, 531, 'Properties and applications of engineering materials', 0),
('ME201', 'Thermodynamics', 3.0, 48, 0, 531, 'Principles of thermodynamics and heat transfer', 0),
('ME202', 'Fluid Mechanics', 3.0, 48, 0, 531, 'Fluid flow and hydraulic systems', 0),
('ME203', 'Machine Design', 3.0, 48, 1, 531, 'Design of mechanical components and systems', 0),

-- Economics and Management College Courses (department_id = 532)
('EM101', 'Management Principles', 3.0, 48, 0, 532, 'Modern management theory fundamentals', 0),
('EM102', 'Microeconomics', 3.0, 48, 0, 532, 'Microeconomic theory analysis', 0),
('EM103', 'Macroeconomics', 3.0, 48, 0, 532, 'Macroeconomic theory and policy', 0),
('EM201', 'Financial Management', 3.0, 48, 0, 532, 'Corporate financial decision making', 0),
('EM202', 'Marketing Management', 3.0, 48, 0, 532, 'Marketing strategies and consumer behavior', 0),
('EM203', 'Operations Management', 3.0, 48, 1, 532, 'Production and operations management', 0),
('EM204', 'International Business', 3.0, 48, 1, 532, 'Global business environment and strategies', 0),

-- Foreign Languages College Courses (department_id = 533)
('FL101', 'Comprehensive English', 3.0, 48, 0, 533, 'English comprehensive skills training', 0),
('FL102', 'English Grammar', 2.0, 32, 0, 533, 'Systematic explanation of English grammar', 0),
('FL103', 'English Literature', 3.0, 48, 0, 533, 'Survey of English and American literature', 0),
('FL201', 'Translation Theory', 2.0, 32, 1, 533, 'Translation techniques and practice', 0),
('FL202', 'Business English', 3.0, 48, 1, 533, 'English for business communication', 0),
('FL301', 'Japanese Language', 3.0, 48, 0, 533, 'Basic Japanese language skills', 0),
('FL302', 'Japanese Culture', 2.0, 32, 1, 533, 'Introduction to Japanese culture and society', 0),

-- Art and Design College Courses (department_id = 534)
('AD101', 'Design Fundamentals', 3.0, 48, 0, 534, 'Design theory and practice fundamentals', 0),
('AD102', 'Color Composition', 2.0, 32, 0, 534, 'Color theory and applications', 0),
('AD103', 'Drawing Techniques', 3.0, 48, 0, 534, 'Basic drawing and sketching skills', 0),
('AD201', 'Graphic Design', 3.0, 48, 1, 534, 'Visual communication design principles', 0),
('AD202', 'Interior Design', 3.0, 48, 1, 534, 'Space planning and interior design', 0),
('AD203', 'Digital Media Design', 3.0, 48, 1, 534, 'Digital design tools and techniques', 0);
