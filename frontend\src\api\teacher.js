import request from '@/utils/request'

const baseUrl = '/api/teachers'

// 获取所有教师信息
export function getAllTeachers() {
  return request({
    url: baseUrl,
    method: 'get'
  })
}

// 分页获取教师信息
export function getTeachersByPage(page = 0, size = 10, sortBy = 'id') {
  return request({
    url: `${baseUrl}/page`,
    method: 'get',
    params: {
      page,
      size,
      sortBy
    }
  })
}

// 条件查询教师信息
export function searchTeachers({ name, department, title, page = 0, size = 10 }) {
  return request({
    url: `${baseUrl}/search`,
    method: 'get',
    params: {
      name,
      department,
      title,
      page,
      size
    }
  })
}

// 根据ID获取教师信息
export function getTeacherById(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'get'
  })
}

// 创建教师信息
export function createTeacher(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data
  })
}

// 更新教师信息
export function updateTeacher(id, data) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'put',
    data
  })
}

// 删除教师信息
export function deleteTeacher(id) {
  return request({
    url: `${baseUrl}/${id}`,
    method: 'delete'
  })
}

// 获取教师部门分布统计
export function getTeacherStatsByDepartment() {
  return request({
    url: `${baseUrl}/stats/department`,
    method: 'get'
  })
}

// 获取教师职称分布统计
export function getTeacherStatsByTitle() {
  return request({
    url: `${baseUrl}/stats/title`,
    method: 'get'
  })
} 