package com.university.management.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 院系实体类
 */
@ApiModel(value = "Department", description = "院系信息")
@Entity
@TableName("department")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Department extends BaseEntity {

    /**
     * 院系编号
     */
    @Column(unique = true, nullable = false, length = 20)
    @ApiModelProperty("院系编号")
    private String departmentNo;

    /**
     * 院系名称
     */
    @Column(nullable = false, length = 50)
    @ApiModelProperty("院系名称")
    private String name;

    /**
     * 院系简介
     */
    @Column(length = 500)
    @ApiModelProperty("院系简介")
    private String description;

    /**
     * 院长
     */
    @Column(length = 20)
    @ApiModelProperty("院长")
    private String dean;

    /**
     * 联系电话
     */
    @Column(length = 20)
    @ApiModelProperty("联系电话")
    private String telephone;

    /**
     * 邮箱
     */
    @Column(length = 50)
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 地址
     */
    @Column(length = 200)
    @ApiModelProperty("地址")
    private String address;

    /**
     * 成立日期
     */
    @Column(length = 20)
    @ApiModelProperty("成立日期")
    private String establishedDate;

    /**
     * 网址
     */
    @Column(length = 100)
    @ApiModelProperty("网址")
    private String website;

    /**
     * 排序
     */
    @Column
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 状态（0-正常，1-禁用）
     */
    @Column
    @ApiModelProperty("状态（0-正常，1-禁用）")
    private Integer status;

    /**
     * 专业列表
     */
    @OneToMany(mappedBy = "department", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Major> majors = new ArrayList<>();

    /**
     * 班级列表
     */
    @OneToMany(mappedBy = "department", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Class> classes = new ArrayList<>();

    /**
     * 学生列表
     */
    @OneToMany(mappedBy = "department", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Student> students = new ArrayList<>();

    /**
     * 教师列表
     */
    @OneToMany(mappedBy = "department", fetch = FetchType.LAZY)
    @JsonIgnore
    @TableField(exist = false)
    private List<Teacher> teachers = new ArrayList<>();

    // Getter和Setter方法
    public String getDepartmentNo() {
        return departmentNo;
    }

    public void setDepartmentNo(String departmentNo) {
        this.departmentNo = departmentNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDean() {
        return dean;
    }

    public void setDean(String dean) {
        this.dean = dean;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEstablishedDate() {
        return establishedDate;
    }

    public void setEstablishedDate(String establishedDate) {
        this.establishedDate = establishedDate;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<Major> getMajors() {
        return majors;
    }

    public void setMajors(List<Major> majors) {
        this.majors = majors;
    }

    public List<Class> getClasses() {
        return classes;
    }

    public void setClasses(List<Class> classes) {
        this.classes = classes;
    }

    public List<Student> getStudents() {
        return students;
    }

    public void setStudents(List<Student> students) {
        this.students = students;
    }

    public List<Teacher> getTeachers() {
        return teachers;
    }

    public void setTeachers(List<Teacher> teachers) {
        this.teachers = teachers;
    }
}