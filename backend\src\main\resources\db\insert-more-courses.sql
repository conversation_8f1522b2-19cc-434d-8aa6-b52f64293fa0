-- 插入更多课程数据
USE university_management;

-- 插入更多课程数据（使用正确的department_id）
INSERT INTO course (course_no, name, credit, hours, type, department_id, description, status) VALUES
-- Computer Science College Additional Courses (department_id = 529)
('CS301', 'Advanced Programming', 4.0, 64, 0, 529, 'Advanced programming concepts and techniques', 0),
('CS302', 'Computer Architecture', 3.0, 48, 0, 529, 'Computer system architecture and design', 0),
('CS303', 'Compiler Principles', 3.0, 48, 1, 529, 'Compiler design and implementation', 0),
('CS304', 'Distributed Systems', 3.0, 48, 1, 529, 'Distributed computing systems', 0),
('CS305', 'Information Security', 3.0, 48, 1, 529, 'Computer and network security principles', 0),
('CS306', 'Mobile Application Development', 3.0, 48, 1, 529, 'iOS and Android app development', 0),
('CS307', 'Web Development', 3.0, 48, 1, 529, 'Full-stack web development technologies', 0),
('CS308', 'Cloud Computing', 3.0, 48, 1, 529, 'Cloud platforms and services', 0),

-- Electronic Engineering College Additional Courses (department_id = 530)
('EE301', 'Advanced Circuit Design', 3.0, 48, 1, 530, 'Advanced electronic circuit design', 0),
('EE302', 'Embedded Systems', 3.0, 48, 1, 530, 'Embedded system design and programming', 0),
('EE303', 'Wireless Communication', 3.0, 48, 1, 530, 'Wireless communication technologies', 0),
('EE304', 'Power Electronics', 3.0, 48, 1, 530, 'Power electronic devices and systems', 0),
('EE305', 'VLSI Design', 3.0, 48, 1, 530, 'Very Large Scale Integration design', 0),

-- Mechanical Engineering College Additional Courses (department_id = 531)
('ME301', 'Advanced Manufacturing', 3.0, 48, 1, 531, 'Advanced manufacturing processes', 0),
('ME302', 'Robotics Engineering', 3.0, 48, 1, 531, 'Robot design and control systems', 0),
('ME303', 'Automotive Engineering', 3.0, 48, 1, 531, 'Automotive design and technology', 0),
('ME304', 'Renewable Energy Systems', 3.0, 48, 1, 531, 'Renewable energy technologies', 0),
('ME305', 'Mechatronics Systems', 3.0, 48, 1, 531, 'Integration of mechanical and electronic systems', 0),

-- Economics and Management College Additional Courses (department_id = 532)
('EM301', 'Strategic Management', 3.0, 48, 1, 532, 'Corporate strategy and planning', 0),
('EM302', 'Investment Analysis', 3.0, 48, 1, 532, 'Investment decision making and portfolio management', 0),
('EM303', 'Supply Chain Management', 3.0, 48, 1, 532, 'Supply chain optimization and logistics', 0),
('EM304', 'Digital Marketing', 3.0, 48, 1, 532, 'Online marketing strategies and tools', 0),
('EM305', 'Business Analytics', 3.0, 48, 1, 532, 'Data analysis for business decisions', 0),
('EM306', 'Entrepreneurship', 3.0, 48, 1, 532, 'Starting and managing new ventures', 0),

-- Foreign Languages College Additional Courses (department_id = 533)
('FL303', 'Advanced English Writing', 3.0, 48, 1, 533, 'Academic and professional writing skills', 0),
('FL304', 'English Linguistics', 3.0, 48, 1, 533, 'English language structure and analysis', 0),
('FL305', 'Cross-cultural Communication', 2.0, 32, 1, 533, 'Intercultural communication skills', 0),
('FL306', 'Advanced Japanese', 3.0, 48, 1, 533, 'Advanced Japanese language skills', 0),
('FL307', 'Japanese Business Culture', 2.0, 32, 1, 533, 'Japanese business practices and culture', 0),

-- Art and Design College Additional Courses (department_id = 534)
('AD301', 'Advanced Graphic Design', 3.0, 48, 1, 534, 'Professional graphic design techniques', 0),
('AD302', 'User Experience Design', 3.0, 48, 1, 534, 'UX/UI design principles and practice', 0),
('AD303', 'Animation Design', 3.0, 48, 1, 534, '2D and 3D animation techniques', 0),
('AD304', 'Brand Design', 3.0, 48, 1, 534, 'Brand identity and visual communication', 0),
('AD305', 'Exhibition Design', 3.0, 48, 1, 534, 'Exhibition space and display design', 0),

-- General Education Courses
('GE101', 'College Mathematics', 4.0, 64, 0, 529, 'Fundamental mathematics for engineering', 0),
('GE102', 'College Physics', 4.0, 64, 0, 530, 'Basic physics principles and applications', 0),
('GE103', 'College Chemistry', 3.0, 48, 0, 531, 'General chemistry for engineering students', 0),
('GE104', 'Statistics and Probability', 3.0, 48, 0, 532, 'Statistical analysis and probability theory', 0),
('GE105', 'Technical Writing', 2.0, 32, 0, 533, 'Technical communication skills', 0);
